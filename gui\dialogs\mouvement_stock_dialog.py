"""
Dialogue pour les mouvements de stock (entrée/sortie)
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.produit import Produit

class MouvementStockDialog:
    def __init__(self, parent, title, type_mouvement, current_user):
        self.result = None
        self.type_mouvement = type_mouvement  # "entree" ou "sortie"
        self.current_user = current_user
        
        # C<PERSON>er la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
        
        # Charger les produits
        self.load_products()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.reference_var = tk.StringVar()
        self.commentaire_var = tk.StringVar()
        self.produits_data = []  # Liste des produits sélectionnés
        
        # Générer une référence par défaut
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        type_ref = "ENT" if self.type_mouvement == "entree" else "SOR"
        self.reference_var.set(f"{type_ref}_{timestamp}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        icon = "📥" if self.type_mouvement == "entree" else "📤"
        title_text = f"{icon} {self.window.title()}"
        ttk.Label(main_frame, text=title_text, font=('Arial', 14, 'bold')).pack(pady=(0, 20))
        
        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Référence
        ttk.Label(info_frame, text="Référence:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.reference_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Commentaire
        ttk.Label(info_frame, text="Commentaire:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.commentaire_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        info_frame.columnconfigure(1, weight=1)
        
        # Sélection des produits
        produits_frame = ttk.LabelFrame(main_frame, text="Produits", padding="10")
        produits_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # Frame pour ajouter un produit
        add_frame = ttk.Frame(produits_frame)
        add_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(add_frame, text="Produit:").pack(side=tk.LEFT)
        self.produit_var = tk.StringVar()
        self.produit_combo = ttk.Combobox(add_frame, textvariable=self.produit_var, state="readonly", width=25)
        self.produit_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(add_frame, text="Quantité:").pack(side=tk.LEFT)
        self.quantite_var = tk.StringVar()
        quantite_entry = ttk.Entry(add_frame, textvariable=self.quantite_var, width=10)
        quantite_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(add_frame, text="Ajouter", command=self.ajouter_produit).pack(side=tk.LEFT, padx=(5, 0))
        
        # Liste des produits sélectionnés
        columns = ("Produit", "Quantité", "Stock actuel", "Nouveau stock")
        self.produits_tree = ttk.Treeview(produits_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.produits_tree.heading(col, text=col)
            self.produits_tree.column(col, width=120)
        
        # Scrollbar
        produits_scrollbar = ttk.Scrollbar(produits_frame, orient=tk.VERTICAL, command=self.produits_tree.yview)
        self.produits_tree.configure(yscrollcommand=produits_scrollbar.set)
        
        self.produits_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        produits_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons pour gérer la liste
        list_buttons_frame = ttk.Frame(produits_frame)
        list_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(list_buttons_frame, text="Supprimer", command=self.supprimer_produit).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(list_buttons_frame, text="Vider la liste", command=self.vider_liste).pack(side=tk.LEFT)
        
        # Boutons principaux
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Valider", command=self.valider).pack(side=tk.RIGHT)
        
        # Bind Enter key
        quantite_entry.bind('<Return>', lambda e: self.ajouter_produit())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def load_products(self):
        """Charge les produits dans le combobox"""
        try:
            if self.type_mouvement == "entree":
                # Pour les entrées, tous les produits actifs
                produits = Produit.find_all("actif = 1")
            else:
                # Pour les sorties, seulement les produits avec stock
                produits = Produit.find_all("actif = 1 AND stock_actuel > 0")
            
            produit_values = [f"{p.nom} (Stock: {p.stock_actuel})" for p in produits]
            self.produit_combo['values'] = produit_values
            self.produits_disponibles = produits
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def ajouter_produit(self):
        """Ajoute un produit à la liste"""
        try:
            if not self.produit_var.get():
                messagebox.showerror("Erreur", "Veuillez sélectionner un produit")
                return
            
            quantite = int(self.quantite_var.get())
            if quantite <= 0:
                messagebox.showerror("Erreur", "La quantité doit être positive")
                return
            
            # Trouver le produit sélectionné
            produit_index = self.produit_combo.current()
            if produit_index < 0:
                return
            
            produit = self.produits_disponibles[produit_index]
            
            # Vérifier si le produit n'est pas déjà dans la liste
            for item in self.produits_data:
                if item['produit_id'] == produit.id:
                    messagebox.showerror("Erreur", "Ce produit est déjà dans la liste")
                    return
            
            # Vérifier le stock pour les sorties
            if self.type_mouvement == "sortie" and quantite > produit.stock_actuel:
                messagebox.showerror("Erreur", "Quantité supérieure au stock disponible")
                return
            
            # Calculer le nouveau stock
            if self.type_mouvement == "entree":
                nouveau_stock = produit.stock_actuel + quantite
            else:
                nouveau_stock = produit.stock_actuel - quantite
            
            # Ajouter à la liste
            self.produits_data.append({
                'produit_id': produit.id,
                'produit_nom': produit.nom,
                'quantite': quantite,
                'stock_actuel': produit.stock_actuel,
                'nouveau_stock': nouveau_stock
            })
            
            # Ajouter au treeview
            self.produits_tree.insert("", "end", values=(
                produit.nom,
                quantite,
                produit.stock_actuel,
                nouveau_stock
            ))
            
            # Réinitialiser les champs
            self.produit_var.set("")
            self.quantite_var.set("")
            
        except ValueError:
            messagebox.showerror("Erreur", "Quantité invalide")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {e}")
    
    def supprimer_produit(self):
        """Supprime le produit sélectionné de la liste"""
        selection = self.produits_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à supprimer")
            return
        
        # Supprimer du treeview
        item_index = self.produits_tree.index(selection[0])
        self.produits_tree.delete(selection[0])
        
        # Supprimer des données
        del self.produits_data[item_index]
    
    def vider_liste(self):
        """Vide la liste des produits"""
        if self.produits_data and messagebox.askyesno("Confirmation", "Vider la liste des produits ?"):
            self.produits_data.clear()
            for item in self.produits_tree.get_children():
                self.produits_tree.delete(item)
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        if not self.produits_data:
            errors.append("Veuillez ajouter au moins un produit")
        
        if not self.reference_var.get().strip():
            errors.append("La référence est obligatoire")
        
        return errors
    
    def valider(self):
        """Valide et enregistre les mouvements"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            reference = self.reference_var.get().strip()
            commentaire = self.commentaire_var.get().strip()
            
            # Enregistrer chaque mouvement
            for item in self.produits_data:
                produit = Produit.find_by_id(item['produit_id'])
                if produit:
                    produit.ajuster_stock(
                        quantite=item['quantite'],
                        type_mouvement=self.type_mouvement,
                        utilisateur_id=self.current_user.id,
                        reference=reference,
                        commentaire=commentaire
                    )
            
            self.result = {
                'reference': reference,
                'commentaire': commentaire,
                'produits': self.produits_data,
                'type_mouvement': self.type_mouvement
            }
            
            messagebox.showinfo("Succès", 
                              f"{len(self.produits_data)} mouvement(s) de stock enregistré(s)")
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
