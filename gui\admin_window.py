"""
Fenêtre d'administration avancée
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
from datetime import datetime, date, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.scheduler_manager import scheduler_manager
from utils.analytics_manager import analytics_manager
from utils.notification_manager import notification_manager
from utils.backup_manager import BackupManager
from utils.export_manager import ExportManager
from utils.barcode_generator import barcode_generator

class AdminWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        # Vérifier les permissions d'administration
        if not self.current_user.role == 'gerant':
            messagebox.showerror("Accès refusé", "Seuls les gérants peuvent accéder à cette section")
            return
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="Administration Avancée", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Notebook pour les différentes sections
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglets
        self.setup_scheduler_tab(notebook)
        self.setup_analytics_tab(notebook)
        self.setup_maintenance_tab(notebook)
        self.setup_notifications_tab(notebook)
        self.setup_tools_tab(notebook)
    
    def setup_scheduler_tab(self, notebook):
        """Configure l'onglet planificateur"""
        scheduler_frame = ttk.Frame(notebook)
        notebook.add(scheduler_frame, text="Planificateur")
        
        # Statut du planificateur
        status_frame = ttk.LabelFrame(scheduler_frame, text="Statut du planificateur", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.scheduler_status_label = ttk.Label(status_frame, text="Vérification...")
        self.scheduler_status_label.pack(side=tk.LEFT)
        
        ttk.Button(status_frame, text="Démarrer", 
                  command=self.start_scheduler).pack(side=tk.RIGHT, padx=2)
        ttk.Button(status_frame, text="Arrêter", 
                  command=self.stop_scheduler).pack(side=tk.RIGHT, padx=2)
        
        # Liste des tâches
        tasks_frame = ttk.LabelFrame(scheduler_frame, text="Tâches planifiées", padding="5")
        tasks_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("ID", "Description", "Prochaine exécution", "Dernière exécution", "Statut")
        self.tasks_tree = ttk.Treeview(tasks_frame, columns=columns, show="headings")
        
        for col in columns:
            self.tasks_tree.heading(col, text=col)
            self.tasks_tree.column(col, width=120)
        
        tasks_scrollbar = ttk.Scrollbar(tasks_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)
        
        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons d'action
        actions_frame = ttk.Frame(scheduler_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(actions_frame, text="Exécuter maintenant", 
                  command=self.run_task_now).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="Pause/Reprendre", 
                  command=self.toggle_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="Actualiser", 
                  command=self.refresh_tasks).pack(side=tk.RIGHT, padx=2)
    
    def setup_analytics_tab(self, notebook):
        """Configure l'onglet analyses"""
        analytics_frame = ttk.Frame(notebook)
        notebook.add(analytics_frame, text="Analyses")
        
        # Boutons d'analyse
        buttons_frame = ttk.Frame(analytics_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="📈 Tendances de ventes", 
                  command=self.show_sales_trends).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🏆 Performance produits", 
                  command=self.show_product_performance).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="👥 Comportement clients", 
                  command=self.show_customer_behavior).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="📦 Efficacité inventaire", 
                  command=self.show_inventory_efficiency).pack(side=tk.LEFT, padx=2)
        
        # Zone d'affichage des résultats
        self.analytics_text = tk.Text(analytics_frame, wrap=tk.WORD, height=20)
        analytics_scrollbar = ttk.Scrollbar(analytics_frame, orient=tk.VERTICAL, command=self.analytics_text.yview)
        self.analytics_text.configure(yscrollcommand=analytics_scrollbar.set)
        
        self.analytics_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        analytics_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons d'export
        export_frame = ttk.Frame(analytics_frame)
        export_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(export_frame, text="💾 Exporter rapport", 
                  command=self.export_analytics).pack(side=tk.LEFT, padx=2)
        ttk.Button(export_frame, text="🧠 Générer insights", 
                  command=self.generate_insights).pack(side=tk.LEFT, padx=2)
        ttk.Button(export_frame, text="🗑️ Vider cache", 
                  command=self.clear_analytics_cache).pack(side=tk.RIGHT, padx=2)
    
    def setup_maintenance_tab(self, notebook):
        """Configure l'onglet maintenance"""
        maintenance_frame = ttk.Frame(notebook)
        notebook.add(maintenance_frame, text="Maintenance")
        
        # Sauvegardes
        backup_frame = ttk.LabelFrame(maintenance_frame, text="Sauvegardes", padding="10")
        backup_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(backup_frame, text="🔄 Créer sauvegarde", 
                  command=self.create_backup).pack(side=tk.LEFT, padx=2)
        ttk.Button(backup_frame, text="📂 Restaurer sauvegarde", 
                  command=self.restore_backup).pack(side=tk.LEFT, padx=2)
        ttk.Button(backup_frame, text="🗑️ Nettoyer anciennes", 
                  command=self.cleanup_backups).pack(side=tk.LEFT, padx=2)
        
        # Base de données
        db_frame = ttk.LabelFrame(maintenance_frame, text="Base de données", padding="10")
        db_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(db_frame, text="🔧 Optimiser BDD", 
                  command=self.optimize_database).pack(side=tk.LEFT, padx=2)
        ttk.Button(db_frame, text="📊 Statistiques BDD", 
                  command=self.show_db_stats).pack(side=tk.LEFT, padx=2)
        ttk.Button(db_frame, text="🧹 Nettoyer logs", 
                  command=self.cleanup_logs).pack(side=tk.LEFT, padx=2)
        
        # Informations système
        system_frame = ttk.LabelFrame(maintenance_frame, text="Informations système", padding="10")
        system_frame.pack(fill=tk.BOTH, expand=True)
        
        self.system_info_text = tk.Text(system_frame, wrap=tk.WORD, height=10)
        system_scrollbar = ttk.Scrollbar(system_frame, orient=tk.VERTICAL, command=self.system_info_text.yview)
        self.system_info_text.configure(yscrollcommand=system_scrollbar.set)
        
        self.system_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        system_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_notifications_tab(self, notebook):
        """Configure l'onglet notifications"""
        notifications_frame = ttk.Frame(notebook)
        notebook.add(notifications_frame, text="Notifications")
        
        # Contrôles
        controls_frame = ttk.Frame(notifications_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(controls_frame, text="📋 Centre de notifications", 
                  command=self.show_notification_center).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="🔔 Tester alerte", 
                  command=self.test_notification).pack(side=tk.LEFT, padx=2)
        ttk.Button(controls_frame, text="🔍 Vérifier alertes", 
                  command=self.check_all_alerts).pack(side=tk.LEFT, padx=2)
        
        # Résumé des notifications
        summary_frame = ttk.LabelFrame(notifications_frame, text="Résumé", padding="10")
        summary_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.notifications_summary_label = ttk.Label(summary_frame, text="Chargement...")
        self.notifications_summary_label.pack()
        
        # Configuration
        config_frame = ttk.LabelFrame(notifications_frame, text="Configuration", padding="10")
        config_frame.pack(fill=tk.X)
        
        self.auto_check_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(config_frame, text="Vérification automatique", 
                       variable=self.auto_check_var).pack(anchor=tk.W)
        
        ttk.Label(config_frame, text="Intervalle (minutes):").pack(anchor=tk.W)
        self.interval_var = tk.StringVar(value="5")
        ttk.Entry(config_frame, textvariable=self.interval_var, width=10).pack(anchor=tk.W)
    
    def setup_tools_tab(self, notebook):
        """Configure l'onglet outils"""
        tools_frame = ttk.Frame(notebook)
        notebook.add(tools_frame, text="Outils")
        
        # Codes-barres
        barcode_frame = ttk.LabelFrame(tools_frame, text="Codes-barres", padding="10")
        barcode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(barcode_frame, text="🏷️ Générer codes-barres", 
                  command=self.generate_barcodes).pack(side=tk.LEFT, padx=2)
        ttk.Button(barcode_frame, text="✅ Valider code-barres", 
                  command=self.validate_barcode).pack(side=tk.LEFT, padx=2)
        
        # Export/Import
        export_frame = ttk.LabelFrame(tools_frame, text="Export/Import", padding="10")
        export_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(export_frame, text="📤 Export complet", 
                  command=self.full_export).pack(side=tk.LEFT, padx=2)
        ttk.Button(export_frame, text="📥 Import données", 
                  command=self.import_data).pack(side=tk.LEFT, padx=2)
        
        # Utilitaires
        utils_frame = ttk.LabelFrame(tools_frame, text="Utilitaires", padding="10")
        utils_frame.pack(fill=tk.X)
        
        ttk.Button(utils_frame, text="🔄 Recalculer stocks", 
                  command=self.recalculate_stocks).pack(side=tk.LEFT, padx=2)
        ttk.Button(utils_frame, text="🧮 Vérifier cohérence", 
                  command=self.check_data_consistency).pack(side=tk.LEFT, padx=2)
    
    def load_data(self):
        """Charge les données initiales"""
        self.refresh_scheduler_status()
        self.refresh_notifications_summary()
        self.show_system_info()
    
    def refresh_scheduler_status(self):
        """Met à jour le statut du planificateur"""
        if scheduler_manager.is_available():
            status = "Actif" if scheduler_manager.running else "Arrêté"
            self.scheduler_status_label.config(text=f"Planificateur: {status}")
        else:
            self.scheduler_status_label.config(text="Planificateur: Non disponible (APScheduler manquant)")
    
    def start_scheduler(self):
        """Démarre le planificateur"""
        if scheduler_manager.start():
            messagebox.showinfo("Succès", "Planificateur démarré")
            self.refresh_scheduler_status()
            self.refresh_tasks()
        else:
            messagebox.showerror("Erreur", "Impossible de démarrer le planificateur")
    
    def stop_scheduler(self):
        """Arrête le planificateur"""
        if scheduler_manager.stop():
            messagebox.showinfo("Succès", "Planificateur arrêté")
            self.refresh_scheduler_status()
            self.refresh_tasks()
        else:
            messagebox.showerror("Erreur", "Impossible d'arrêter le planificateur")
    
    def refresh_tasks(self):
        """Met à jour la liste des tâches"""
        # Nettoyer le treeview
        for item in self.tasks_tree.get_children():
            self.tasks_tree.delete(item)
        
        if not scheduler_manager.is_available():
            return
        
        jobs_status = scheduler_manager.get_all_jobs_status()
        
        for job_id, status in jobs_status.items():
            if status:
                next_run = status['next_run'].strftime('%d/%m/%Y %H:%M') if status['next_run'] else "N/A"
                last_run = status['last_run'].strftime('%d/%m/%Y %H:%M') if status['last_run'] else "Jamais"
                job_status = "Actif" if status['enabled'] else "En pause"
                
                self.tasks_tree.insert("", "end", values=(
                    job_id,
                    status['description'],
                    next_run,
                    last_run,
                    job_status
                ))
    
    def run_task_now(self):
        """Exécute une tâche immédiatement"""
        selection = self.tasks_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une tâche")
            return
        
        job_id = self.tasks_tree.item(selection[0])['values'][0]
        
        if scheduler_manager.run_job_now(job_id):
            messagebox.showinfo("Succès", f"Tâche {job_id} exécutée")
            self.refresh_tasks()
        else:
            messagebox.showerror("Erreur", f"Impossible d'exécuter la tâche {job_id}")
    
    def toggle_task(self):
        """Met en pause ou reprend une tâche"""
        selection = self.tasks_tree.selection()
        if not selection:
            messagebox.showwarning("Sélection", "Veuillez sélectionner une tâche")
            return
        
        job_id = self.tasks_tree.item(selection[0])['values'][0]
        status = self.tasks_tree.item(selection[0])['values'][4]
        
        if status == "Actif":
            success = scheduler_manager.pause_job(job_id)
            action = "mise en pause"
        else:
            success = scheduler_manager.resume_job(job_id)
            action = "reprise"
        
        if success:
            messagebox.showinfo("Succès", f"Tâche {job_id} {action}")
            self.refresh_tasks()
        else:
            messagebox.showerror("Erreur", f"Impossible de faire la {action} de la tâche {job_id}")
    
    def show_sales_trends(self):
        """Affiche les tendances de ventes"""
        trends = analytics_manager.analyze_sales_trends()
        if trends:
            self.display_analytics_result("Tendances de Ventes", trends)
        else:
            messagebox.showerror("Erreur", "Impossible d'analyser les tendances de ventes")
    
    def show_product_performance(self):
        """Affiche la performance des produits"""
        performance = analytics_manager.analyze_product_performance()
        if performance:
            self.display_analytics_result("Performance des Produits", performance)
        else:
            messagebox.showerror("Erreur", "Impossible d'analyser la performance des produits")
    
    def show_customer_behavior(self):
        """Affiche le comportement des clients"""
        behavior = analytics_manager.analyze_customer_behavior()
        if behavior:
            self.display_analytics_result("Comportement des Clients", behavior)
        else:
            messagebox.showerror("Erreur", "Impossible d'analyser le comportement des clients")
    
    def show_inventory_efficiency(self):
        """Affiche l'efficacité de l'inventaire"""
        efficiency = analytics_manager.analyze_inventory_efficiency()
        if efficiency:
            self.display_analytics_result("Efficacité de l'Inventaire", efficiency)
        else:
            messagebox.showerror("Erreur", "Impossible d'analyser l'efficacité de l'inventaire")
    
    def display_analytics_result(self, title, data):
        """Affiche un résultat d'analyse"""
        self.analytics_text.config(state=tk.NORMAL)
        self.analytics_text.delete(1.0, tk.END)
        
        self.analytics_text.insert(tk.END, f"=== {title} ===\n\n")
        
        # Formater les données de manière lisible
        import json
        formatted_data = json.dumps(data, indent=2, ensure_ascii=False, default=str)
        self.analytics_text.insert(tk.END, formatted_data)
        
        self.analytics_text.config(state=tk.DISABLED)
    
    def generate_insights(self):
        """Génère des insights business"""
        insights = analytics_manager.generate_business_insights()
        if insights:
            self.display_analytics_result("Insights Business", insights)
        else:
            messagebox.showerror("Erreur", "Impossible de générer les insights")
    
    def export_analytics(self):
        """Exporte le rapport d'analyse"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            result = analytics_manager.export_analytics_report(filename)
            if result:
                messagebox.showinfo("Succès", f"Rapport exporté: {result}")
            else:
                messagebox.showerror("Erreur", "Impossible d'exporter le rapport")
    
    def clear_analytics_cache(self):
        """Vide le cache des analyses"""
        analytics_manager.clear_cache()
        messagebox.showinfo("Succès", "Cache vidé")
    
    def refresh_notifications_summary(self):
        """Met à jour le résumé des notifications"""
        summary = notification_manager.get_notification_summary()
        text = f"Total: {summary['total']} | Non lues: {summary['unread']} | Urgentes: {summary['urgent']} | Importantes: {summary['high']}"
        self.notifications_summary_label.config(text=text)
    
    def show_notification_center(self):
        """Affiche le centre de notifications"""
        notification_manager.show_notification_center(self)
    
    def test_notification(self):
        """Teste une notification"""
        notification_manager.add_notification(
            "Test",
            "Ceci est une notification de test",
            "info",
            "normal"
        )
        messagebox.showinfo("Test", "Notification de test ajoutée")
        self.refresh_notifications_summary()
    
    def check_all_alerts(self):
        """Vérifie toutes les alertes"""
        notification_manager.check_stock_alerts()
        notification_manager.check_sales_alerts()
        notification_manager.check_system_alerts()
        messagebox.showinfo("Succès", "Vérification des alertes terminée")
        self.refresh_notifications_summary()
    
    def show_system_info(self):
        """Affiche les informations système"""
        self.system_info_text.config(state=tk.NORMAL)
        self.system_info_text.delete(1.0, tk.END)
        
        info = f"""INFORMATIONS SYSTÈME
{'='*50}

Date/Heure: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
Version Python: {sys.version}
Plateforme: {sys.platform}

ÉTAT DE L'APPLICATION
{'='*50}

Planificateur: {'Disponible' if scheduler_manager.is_available() else 'Non disponible'}
Gestionnaire de notifications: Actif
Gestionnaire d'analyses: Actif

STATISTIQUES BASE DE DONNÉES
{'='*50}

Taille de la base: {notification_manager.get_database_size():.2f} MB
Utilisation disque: {notification_manager.get_disk_usage():.1f}%

DERNIÈRES ACTIVITÉS
{'='*50}

Dernière sauvegarde: À implémenter
Dernière analyse: À implémenter
Dernière vérification: À implémenter
"""
        
        self.system_info_text.insert(tk.END, info)
        self.system_info_text.config(state=tk.DISABLED)
    
    def create_backup(self):
        """Crée une sauvegarde"""
        try:
            backup_manager = BackupManager()
            backup_path = backup_manager.create_backup()
            messagebox.showinfo("Succès", f"Sauvegarde créée: {backup_path}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def restore_backup(self):
        """Restaure une sauvegarde"""
        filename = filedialog.askopenfilename(
            title="Sélectionner une sauvegarde",
            filetypes=[("Zip files", "*.zip"), ("All files", "*.*")]
        )
        
        if filename:
            if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir restaurer cette sauvegarde ? Cela remplacera les données actuelles."):
                try:
                    backup_manager = BackupManager()
                    backup_manager.restore_backup(filename)
                    messagebox.showinfo("Succès", "Sauvegarde restaurée. Redémarrez l'application.")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de la restauration: {e}")
    
    def cleanup_backups(self):
        """Nettoie les anciennes sauvegardes"""
        try:
            backup_manager = BackupManager()
            count = backup_manager.cleanup_old_backups(30)  # Garder 30 jours
            messagebox.showinfo("Succès", f"{count} anciennes sauvegardes supprimées")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
    
    def optimize_database(self):
        """Optimise la base de données"""
        try:
            from database.db_manager import DatabaseManager
            db = DatabaseManager()
            db.execute_query("VACUUM")
            messagebox.showinfo("Succès", "Base de données optimisée")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'optimisation: {e}")
    
    def show_db_stats(self):
        """Affiche les statistiques de la base de données"""
        try:
            from database.db_manager import DatabaseManager
            db = DatabaseManager()
            
            # Compter les enregistrements dans chaque table
            tables = ['produits', 'ventes', 'details_vente', 'mouvements_stock', 'utilisateurs', 'approvisionnements']
            stats = {}
            
            for table in tables:
                result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                stats[table] = result[0][0] if result else 0
            
            stats_text = "STATISTIQUES BASE DE DONNÉES\n" + "="*40 + "\n\n"
            for table, count in stats.items():
                stats_text += f"{table.capitalize()}: {count}\n"
            
            messagebox.showinfo("Statistiques BDD", stats_text)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la récupération des statistiques: {e}")
    
    def cleanup_logs(self):
        """Nettoie les anciens logs"""
        try:
            import os
            import glob
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=30)
            log_files = glob.glob("*.log*")
            count = 0
            
            for log_file in log_files:
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    if file_time < cutoff_date:
                        os.remove(log_file)
                        count += 1
                except:
                    pass
            
            messagebox.showinfo("Succès", f"{count} anciens logs supprimés")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
    
    def generate_barcodes(self):
        """Génère des codes-barres pour les produits"""
        try:
            from models.produit import Produit
            
            # Demander le nombre de codes à générer
            count = tk.simpledialog.askinteger("Codes-barres", "Nombre de codes à générer:", minvalue=1, maxvalue=100)
            if not count:
                return
            
            codes = barcode_generator.generate_batch_codes(count, "ean13")
            
            # Exporter en CSV
            filename = barcode_generator.export_codes_csv(codes)
            messagebox.showinfo("Succès", f"{count} codes-barres générés et exportés: {filename}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération: {e}")
    
    def validate_barcode(self):
        """Valide un code-barres"""
        code = tk.simpledialog.askstring("Validation", "Code-barres à valider:")
        if code:
            info = barcode_generator.get_barcode_info(code)
            
            status = "✅ Valide" if info['valid'] else "❌ Invalide"
            message = f"Code: {code}\nType: {info['type']}\nStatut: {status}"
            
            if info['country']:
                message += f"\nPays/Usage: {info['country']}"
            
            messagebox.showinfo("Validation", message)
    
    def full_export(self):
        """Export complet des données"""
        try:
            export_manager = ExportManager()
            
            # Demander le répertoire de destination
            directory = filedialog.askdirectory(title="Sélectionner le répertoire d'export")
            if not directory:
                return
            
            # Exporter toutes les données
            from models.produit import Produit
            from models.vente import Vente
            from models.mouvement_stock import MouvementStock
            
            produits = Produit.find_all()
            ventes = Vente.find_all()
            mouvements = MouvementStock.find_all()
            
            # Créer les exports
            export_manager.export_produits_csv(produits)
            export_manager.export_ventes_csv(ventes)
            export_manager.export_mouvements_stock_csv(mouvements)
            
            messagebox.showinfo("Succès", f"Export complet terminé dans: {export_manager.export_dir}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export: {e}")
    
    def import_data(self):
        """Importe des données"""
        messagebox.showinfo("Info", "Fonctionnalité d'import à implémenter selon les besoins spécifiques")
    
    def recalculate_stocks(self):
        """Recalcule tous les stocks"""
        if messagebox.askyesno("Confirmation", "Recalculer tous les stocks ? Cette opération peut prendre du temps."):
            try:
                from models.produit import Produit
                from models.mouvement_stock import MouvementStock
                
                produits = Produit.find_all("actif = 1")
                count = 0
                
                for produit in produits:
                    # Recalculer le stock basé sur les mouvements
                    mouvements = MouvementStock.find_all("produit_id = ?", (produit.id,))
                    
                    stock_calcule = 0
                    for mouvement in mouvements:
                        if mouvement.type_mouvement == "entree":
                            stock_calcule += mouvement.quantite
                        elif mouvement.type_mouvement == "sortie":
                            stock_calcule -= mouvement.quantite
                        elif mouvement.type_mouvement == "inventaire":
                            stock_calcule = mouvement.quantite
                    
                    if stock_calcule != produit.stock_actuel:
                        produit.stock_actuel = stock_calcule
                        produit.save()
                        count += 1
                
                messagebox.showinfo("Succès", f"{count} stocks recalculés")
                
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du recalcul: {e}")
    
    def check_data_consistency(self):
        """Vérifie la cohérence des données"""
        try:
            issues = []
            
            # Vérifier les produits sans catégorie
            from models.produit import Produit
            produits_sans_categorie = Produit.find_all("categorie_id IS NULL AND actif = 1")
            if produits_sans_categorie:
                issues.append(f"{len(produits_sans_categorie)} produits sans catégorie")
            
            # Vérifier les ventes sans détails
            from models.vente import Vente
            ventes = Vente.find_all()
            ventes_sans_details = []
            for vente in ventes:
                vente.load_details()
                if not vente.details:
                    ventes_sans_details.append(vente)
            
            if ventes_sans_details:
                issues.append(f"{len(ventes_sans_details)} ventes sans détails")
            
            # Vérifier les stocks négatifs
            produits_stock_negatif = Produit.find_all("stock_actuel < 0")
            if produits_stock_negatif:
                issues.append(f"{len(produits_stock_negatif)} produits avec stock négatif")
            
            if issues:
                message = "Problèmes détectés:\n\n" + "\n".join(f"• {issue}" for issue in issues)
                messagebox.showwarning("Problèmes détectés", message)
            else:
                messagebox.showinfo("Succès", "Aucun problème de cohérence détecté")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la vérification: {e}")
