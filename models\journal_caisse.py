"""
Modèle pour le journal de caisse
"""
from models.base_model import BaseModel
from datetime import datetime, date

class JournalCaisse(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "journal_caisse"
        self.date_operation = None
        self.type_operation = ""  # "ouverture", "vente", "depense", "fermeture"
        self.description = ""
        self.montant_entree = 0.0
        self.montant_sortie = 0.0
        self.solde = 0.0
        self.utilisateur_id = None
        self.reference = ""
        self.mode_paiement = ""
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'date_operation': self.date_operation or datetime.now().date(),
            'type_operation': self.type_operation,
            'description': self.description,
            'montant_entree': self.montant_entree,
            'montant_sortie': self.montant_sortie,
            'solde': self.solde,
            'utilisateur_id': self.utilisateur_id,
            'reference': self.reference,
            'mode_paiement': self.mode_paiement
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.date_operation = row[1]
        self.type_operation = row[2]
        self.description = row[3]
        self.montant_entree = float(row[4]) if row[4] else 0.0
        self.montant_sortie = float(row[5]) if row[5] else 0.0
        self.solde = float(row[6]) if row[6] else 0.0
        self.utilisateur_id = row[7]
        self.reference = row[8]
        self.mode_paiement = row[9]
        self.created_at = row[10]
    
    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Inconnu"
        return "Système"
    
    @classmethod
    def ouvrir_caisse(cls, utilisateur_id, montant_initial=0.0):
        """Ouvre la caisse pour la journée"""
        # Vérifier qu'il n'y a pas déjà une ouverture aujourd'hui
        today = date.today()
        existing = cls.find_all(
            "date_operation = ? AND type_operation = 'ouverture'",
            (today,)
        )
        
        if existing:
            raise ValueError("La caisse a déjà été ouverte aujourd'hui")
        
        # Calculer le solde de départ (solde de fermeture d'hier)
        solde_precedent = cls.get_solde_fermeture_precedent()
        solde_initial = solde_precedent + montant_initial
        
        # Créer l'entrée d'ouverture
        ouverture = cls()
        ouverture.type_operation = "ouverture"
        ouverture.description = f"Ouverture de caisse - Fond de caisse: {montant_initial:.2f}€"
        ouverture.montant_entree = montant_initial
        ouverture.solde = solde_initial
        ouverture.utilisateur_id = utilisateur_id
        ouverture.reference = f"OUVERTURE_{today.strftime('%Y%m%d')}"
        
        ouverture.save()
        return ouverture
    
    @classmethod
    def fermer_caisse(cls, utilisateur_id, montant_reel=None, commentaire=""):
        """Ferme la caisse pour la journée"""
        today = date.today()
        
        # Vérifier qu'il y a une ouverture aujourd'hui
        ouverture = cls.find_all(
            "date_operation = ? AND type_operation = 'ouverture'",
            (today,)
        )
        
        if not ouverture:
            raise ValueError("La caisse n'a pas été ouverte aujourd'hui")
        
        # Vérifier qu'il n'y a pas déjà une fermeture
        fermeture_existante = cls.find_all(
            "date_operation = ? AND type_operation = 'fermeture'",
            (today,)
        )
        
        if fermeture_existante:
            raise ValueError("La caisse a déjà été fermée aujourd'hui")
        
        # Calculer le solde théorique
        solde_theorique = cls.get_solde_actuel()
        
        # Créer l'entrée de fermeture
        fermeture = cls()
        fermeture.type_operation = "fermeture"
        fermeture.utilisateur_id = utilisateur_id
        fermeture.reference = f"FERMETURE_{today.strftime('%Y%m%d')}"
        
        if montant_reel is not None:
            # Fermeture avec comptage
            ecart = montant_reel - solde_theorique
            fermeture.description = (f"Fermeture de caisse - Théorique: {solde_theorique:.2f}€, "
                                   f"Réel: {montant_reel:.2f}€, Écart: {ecart:.2f}€")
            if ecart != 0:
                fermeture.description += f" - {commentaire}"
                if ecart > 0:
                    fermeture.montant_entree = ecart
                else:
                    fermeture.montant_sortie = abs(ecart)
            fermeture.solde = montant_reel
        else:
            # Fermeture sans comptage
            fermeture.description = f"Fermeture de caisse - Solde théorique: {solde_theorique:.2f}€"
            fermeture.solde = solde_theorique
        
        fermeture.save()
        return fermeture
    
    @classmethod
    def enregistrer_vente(cls, vente, utilisateur_id):
        """Enregistre une vente dans le journal de caisse"""
        # Ne traiter que les paiements en espèces
        if vente.mode_paiement != "cash":
            return None
        
        solde_actuel = cls.get_solde_actuel()
        nouveau_solde = solde_actuel + vente.total_ttc
        
        entree = cls()
        entree.type_operation = "vente"
        entree.description = f"Vente {vente.numero_ticket}"
        entree.montant_entree = vente.total_ttc
        entree.solde = nouveau_solde
        entree.utilisateur_id = utilisateur_id
        entree.reference = vente.numero_ticket
        entree.mode_paiement = vente.mode_paiement
        
        entree.save()
        return entree
    
    @classmethod
    def enregistrer_depense(cls, montant, description, utilisateur_id, reference="", mode_paiement="cash"):
        """Enregistre une dépense dans le journal de caisse"""
        if montant <= 0:
            raise ValueError("Le montant de la dépense doit être positif")
        
        solde_actuel = cls.get_solde_actuel()
        if solde_actuel < montant:
            raise ValueError("Solde insuffisant pour cette dépense")
        
        nouveau_solde = solde_actuel - montant
        
        depense = cls()
        depense.type_operation = "depense"
        depense.description = description
        depense.montant_sortie = montant
        depense.solde = nouveau_solde
        depense.utilisateur_id = utilisateur_id
        depense.reference = reference or f"DEP_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        depense.mode_paiement = mode_paiement
        
        depense.save()
        return depense
    
    @classmethod
    def get_solde_actuel(cls):
        """Retourne le solde actuel de la caisse"""
        # Prendre la dernière entrée de la journée
        today = date.today()
        derniere_entree = cls.find_all(
            "date_operation = ? ORDER BY created_at DESC LIMIT 1",
            (today,)
        )
        
        if derniere_entree:
            return derniere_entree[0].solde
        
        # Si pas d'entrée aujourd'hui, prendre le solde de fermeture d'hier
        return cls.get_solde_fermeture_precedent()
    
    @classmethod
    def get_solde_fermeture_precedent(cls):
        """Retourne le solde de fermeture du jour précédent"""
        # Chercher la dernière fermeture
        derniere_fermeture = cls.find_all(
            "type_operation = 'fermeture' ORDER BY date_operation DESC, created_at DESC LIMIT 1"
        )
        
        if derniere_fermeture:
            return derniere_fermeture[0].solde
        
        return 0.0
    
    @classmethod
    def get_operations_jour(cls, date_operation=None):
        """Retourne toutes les opérations d'un jour"""
        if date_operation is None:
            date_operation = date.today()
        
        return cls.find_all(
            "date_operation = ? ORDER BY created_at",
            (date_operation,)
        )
    
    @classmethod
    def get_recettes_periode(cls, date_debut, date_fin):
        """Retourne les recettes d'une période"""
        operations = cls.find_all(
            "date_operation BETWEEN ? AND ? AND type_operation IN ('vente', 'ouverture')",
            (date_debut, date_fin)
        )
        
        total_recettes = sum(op.montant_entree for op in operations)
        return total_recettes, operations
    
    @classmethod
    def get_depenses_periode(cls, date_debut, date_fin):
        """Retourne les dépenses d'une période"""
        operations = cls.find_all(
            "date_operation BETWEEN ? AND ? AND type_operation = 'depense'",
            (date_debut, date_fin)
        )
        
        total_depenses = sum(op.montant_sortie for op in operations)
        return total_depenses, operations
    
    @classmethod
    def is_caisse_ouverte(cls):
        """Vérifie si la caisse est ouverte aujourd'hui"""
        today = date.today()
        
        ouverture = cls.find_all(
            "date_operation = ? AND type_operation = 'ouverture'",
            (today,)
        )
        
        fermeture = cls.find_all(
            "date_operation = ? AND type_operation = 'fermeture'",
            (today,)
        )
        
        return len(ouverture) > 0 and len(fermeture) == 0
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'date_operation': self.date_operation,
            'type_operation': self.type_operation,
            'description': self.description,
            'montant_entree': self.montant_entree,
            'montant_sortie': self.montant_sortie,
            'solde': self.solde,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'reference': self.reference,
            'mode_paiement': self.mode_paiement,
            'created_at': self.created_at
        }
