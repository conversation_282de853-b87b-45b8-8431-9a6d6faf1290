"""
Fenêtre principale de l'application de gestion de stock du bar
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.utilisateur import Utilisateur
from gui.login_window import LoginWindow
from gui.dashboard import Dashboard
from gui.produits_window import ProduitsWindow
from gui.ventes_window import VentesWindow
from gui.stock_window import StockWindow
from gui.utilisateurs_window import UtilisateursWindow
from gui.rapports_window import RapportsWindow
from gui.approvisionnements_window import ApprovisionnementWindow
from gui.clients_credit_window import ClientsCreditWindow
from gui.parametres_window import ParametresWindow
from gui.financier_window import FinancierWindow
from gui.audit_window import AuditWindow
from gui.security_window import SecurityWindow
from gui.admin_window import Admin<PERSON>indow
from utils.security_decorators import require_authentication, require_permission, require_role, audit_action, secure_operation
from utils.session_manager import session_manager

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestion de Stock - Bar")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximiser la fenêtre
        
        # Variables
        self.current_user = None
        self.current_frame = None
        
        # Style
        self.setup_styles()
        
        # Interface
        self.setup_ui()
        
        # Authentification
        self.show_login()

        # Démarrer la vérification automatique de session
        self.start_session_check()
    
    def setup_styles(self):
        """Configure les styles de l'application"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Couleurs du thème
        self.colors = {
            'primary': '#2c3e50',
            'secondary': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#34495e'
        }
        
        # Configuration des styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Primary.TButton', background=self.colors['primary'])
        style.configure('Success.TButton', background=self.colors['success'])
        style.configure('Warning.TButton', background=self.colors['warning'])
        style.configure('Danger.TButton', background=self.colors['danger'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Menu principal
        self.create_menu()
        
        # Barre d'outils
        self.create_toolbar()
        
        # Zone de contenu principal
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Barre de statut
        self.create_status_bar()
    
    def create_menu(self):
        """Crée le menu principal"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        file_menu.add_command(label="Nouveau", command=self.nouveau_fichier)
        file_menu.add_command(label="Ouvrir", command=self.ouvrir_fichier)
        file_menu.add_separator()
        file_menu.add_command(label="Sauvegarder", command=self.sauvegarder)
        file_menu.add_command(label="Exporter", command=self.exporter)
        file_menu.add_separator()
        file_menu.add_command(label="Quitter", command=self.quitter)
        
        # Menu Gestion
        gestion_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Gestion", menu=gestion_menu)
        gestion_menu.add_command(label="Produits", command=self.show_produits)
        gestion_menu.add_command(label="Catégories", command=self.show_categories)
        gestion_menu.add_command(label="Fournisseurs", command=self.show_fournisseurs)
        gestion_menu.add_command(label="Clients à crédit", command=self.show_clients_credit)
        
        # Menu Ventes
        ventes_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Ventes", menu=ventes_menu)
        ventes_menu.add_command(label="Point de vente", command=self.show_pos)
        ventes_menu.add_command(label="Historique des ventes", command=self.show_ventes)
        
        # Menu Stock
        stock_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Stock", menu=stock_menu)
        stock_menu.add_command(label="État du stock", command=self.show_stock)
        stock_menu.add_command(label="Mouvements", command=self.show_mouvements)
        stock_menu.add_command(label="Approvisionnements", command=self.show_approvisionnements)
        stock_menu.add_command(label="Inventaire", command=self.show_inventaire)

        # Menu Financier
        financier_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Financier", menu=financier_menu)
        financier_menu.add_command(label="Journal de caisse", command=self.show_financier)
        financier_menu.add_separator()
        financier_menu.add_command(label="Rapports financiers", command=self.show_rapports_financier)

        # Menu Rapports
        rapports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Rapports", menu=rapports_menu)
        rapports_menu.add_command(label="Ventes", command=self.show_rapports_ventes)
        rapports_menu.add_command(label="Stock", command=self.show_rapports_stock)
        rapports_menu.add_command(label="Financier", command=self.show_rapports_financier)
        
        # Menu Administration
        admin_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Administration", menu=admin_menu)
        admin_menu.add_command(label="Utilisateurs", command=self.show_utilisateurs)
        admin_menu.add_command(label="Sécurité", command=self.show_security)
        admin_menu.add_command(label="Paramètres", command=self.show_parametres)
        admin_menu.add_command(label="Logs d'audit", command=self.show_audit_logs)
        admin_menu.add_separator()
        admin_menu.add_command(label="Administration avancée", command=self.show_admin_advanced)
        
        # Menu Aide
        aide_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Aide", menu=aide_menu)
        aide_menu.add_command(label="Documentation", command=self.show_documentation)
        aide_menu.add_command(label="À propos", command=self.show_about)
    
    def create_toolbar(self):
        """Crée la barre d'outils"""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(fill=tk.X, padx=5, pady=2)
        
        # Boutons principaux
        ttk.Button(self.toolbar, text="🏠 Accueil", command=self.show_dashboard).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="🛒 Vente", command=self.show_pos).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="📦 Produits", command=self.show_produits).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="📊 Stock", command=self.show_stock).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.toolbar, text="📈 Rapports", command=self.show_rapports_ventes).pack(side=tk.LEFT, padx=2)
        
        # Séparateur
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Informations utilisateur
        self.user_label = ttk.Label(self.toolbar, text="Non connecté")
        self.user_label.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(self.toolbar, text="🚪 Déconnexion", command=self.logout).pack(side=tk.RIGHT, padx=2)
    
    def create_status_bar(self):
        """Crée la barre de statut"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="Prêt")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Indicateurs
        self.indicators_frame = ttk.Frame(self.status_bar)
        self.indicators_frame.pack(side=tk.RIGHT, padx=5)
    
    def show_login(self):
        """Affiche la fenêtre de connexion"""
        login_window = LoginWindow(self.root, self.on_login_success)
    
    def on_login_success(self, user):
        """Callback appelé après une connexion réussie"""
        self.current_user = user
        self.user_label.config(text=f"Connecté: {user.nom_complet} ({user.role})")
        self.update_menu_permissions()
        self.show_dashboard()
        self.update_status("Connexion réussie")
    
    def update_menu_permissions(self):
        """Met à jour les permissions du menu selon le rôle de l'utilisateur"""
        # Cette méthode sera implémentée pour désactiver certains menus
        # selon les permissions de l'utilisateur
        pass
    
    def show_frame(self, frame_class, *args, **kwargs):
        """Affiche un nouveau frame dans la zone principale"""
        if self.current_frame:
            self.current_frame.destroy()
        
        self.current_frame = frame_class(self.main_frame, self.current_user, *args, **kwargs)
        self.current_frame.pack(fill=tk.BOTH, expand=True)
    
    @require_authentication
    @audit_action("VIEW_DASHBOARD", "Accès au tableau de bord")
    def show_dashboard(self):
        """Affiche le tableau de bord"""
        self.show_frame(Dashboard)
        self.update_status("Tableau de bord")
    
    @require_authentication
    @require_permission('produits')
    @audit_action("VIEW_PRODUCTS", "Accès à la gestion des produits")
    def show_produits(self):
        """Affiche la gestion des produits"""
        self.show_frame(ProduitsWindow)
        self.update_status("Gestion des produits")
    
    @require_authentication
    @require_permission('ventes')
    @audit_action("ACCESS_POS", "Accès au point de vente")
    def show_pos(self):
        """Affiche le point de vente"""
        self.show_frame(VentesWindow, mode="pos")
        self.update_status("Point de vente")
    
    @require_authentication
    @require_permission('ventes')
    @audit_action("VIEW_SALES_HISTORY", "Accès à l'historique des ventes")
    def show_ventes(self):
        """Affiche l'historique des ventes"""
        self.show_frame(VentesWindow, mode="historique")
        self.update_status("Historique des ventes")
    
    @require_authentication
    @require_permission('stock')
    @audit_action("VIEW_STOCK", "Accès à la gestion du stock")
    def show_stock(self):
        """Affiche la gestion du stock"""
        self.show_frame(StockWindow)
        self.update_status("Gestion du stock")
    
    def show_utilisateurs(self):
        """Affiche la gestion des utilisateurs"""
        if self.current_user and self.current_user.can_manage_users():
            self.show_frame(UtilisateursWindow)
            self.update_status("Gestion des utilisateurs")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder à cette section")
    
    @require_authentication
    @require_permission('rapports')
    @audit_action("VIEW_SALES_REPORTS", "Accès aux rapports de ventes")
    def show_rapports_ventes(self):
        """Affiche les rapports de ventes"""
        self.show_frame(RapportsWindow, type_rapport="ventes")
        self.update_status("Rapports de ventes")

    @require_authentication
    @require_permission('rapports')
    @audit_action("VIEW_STOCK_REPORTS", "Accès aux rapports de stock")
    def show_rapports_stock(self):
        """Affiche les rapports de stock"""
        self.show_frame(RapportsWindow, type_rapport="stock")
        self.update_status("Rapports de stock")

    @require_authentication
    @require_permission('rapports')
    @audit_action("VIEW_FINANCIAL_REPORTS", "Accès aux rapports financiers")
    def show_rapports_financier(self):
        """Affiche les rapports financiers"""
        self.show_frame(RapportsWindow, type_rapport="financier")
        self.update_status("Rapports financiers")
    
    def show_approvisionnements(self):
        """Affiche la gestion des approvisionnements"""
        if self.current_user and self.current_user.can_manage_stock():
            self.show_frame(ApprovisionnementWindow)
            self.update_status("Gestion des approvisionnements")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder à cette section")

    def show_clients_credit(self):
        """Affiche la gestion des clients à crédit"""
        if self.current_user and self.current_user.has_permission('client_credit'):
            self.show_frame(ClientsCreditWindow)
            self.update_status("Gestion des clients à crédit")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder à cette section")

    def show_parametres(self):
        """Affiche les paramètres de l'application"""
        if self.current_user and self.current_user.can_manage_users():
            self.show_frame(ParametresWindow)
            self.update_status("Paramètres de l'application")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder aux paramètres")

    def show_financier(self):
        """Affiche la gestion financière"""
        if self.current_user and self.current_user.can_manage_users():
            self.show_frame(FinancierWindow)
            self.update_status("Gestion financière")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder à la gestion financière")

    def show_security(self):
        """Affiche la gestion de la sécurité"""
        if self.current_user and self.current_user.can_manage_users():
            self.show_frame(SecurityWindow)
            self.update_status("Gestion de la sécurité")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder à la gestion de la sécurité")

    def show_audit_logs(self):
        """Affiche les logs d'audit"""
        if self.current_user and self.current_user.can_manage_users():
            self.show_frame(AuditWindow)
            self.update_status("Logs d'audit et sécurité")
        else:
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder aux logs d'audit")

    def show_admin_advanced(self):
        """Affiche l'administration avancée"""
        if self.current_user and self.current_user.role == 'gerant':
            self.show_frame(AdminWindow)
            self.update_status("Administration avancée")
        else:
            messagebox.showerror("Erreur", "Seuls les gérants peuvent accéder à cette section")

    # Méthodes pour les autres fenêtres (à implémenter)
    def show_categories(self): pass
    def show_fournisseurs(self): pass
    def show_mouvements(self): pass
    def show_inventaire(self): pass
    def show_rapports_financier(self): pass
    def show_documentation(self): pass
    def show_about(self): pass
    
    # Méthodes du menu Fichier
    def nouveau_fichier(self): pass
    def ouvrir_fichier(self): pass
    def sauvegarder(self): pass
    def exporter(self): pass
    
    @audit_action("LOGOUT", "Déconnexion utilisateur")
    def logout(self):
        """Déconnexion sécurisée"""
        if self.current_user:
            # Invalider la session
            if hasattr(self.current_user, 'current_session'):
                session_manager.invalidate_session(self.current_user.current_session.session_id)

            # Invalider toutes les sessions de l'utilisateur (optionnel)
            # session_manager.invalidate_user_sessions(self.current_user.id)

            logging.info(f"Déconnexion de {self.current_user.nom_complet}")

        # Nettoyer l'interface
        self.current_user = None
        self.user_label.config(text="Non connecté")
        if self.current_frame:
            self.current_frame.destroy()
            self.current_frame = None

        # Désactiver les menus
        self.update_menu_permissions()

        # Retourner à l'écran de connexion
        self.show_login()
        self.update_status("Déconnecté")
    
    def quitter(self):
        """Quitte l'application"""
        if messagebox.askokcancel("Quitter", "Êtes-vous sûr de vouloir quitter l'application ?"):
            self.root.quit()
    
    def update_status(self, message):
        """Met à jour la barre de statut"""
        self.status_label.config(text=message)

    def start_session_check(self):
        """Démarre la vérification automatique de session"""
        self.check_session_validity()
        # Programmer la prochaine vérification dans 60 secondes
        self.root.after(60000, self.start_session_check)

    def check_session_validity(self):
        """Vérifie la validité de la session actuelle"""
        if self.current_user and hasattr(self.current_user, 'current_session'):
            session = self.current_user.current_session

            # Vérifier si la session existe encore dans le gestionnaire
            active_session = session_manager.get_session(session.session_id)

            if not active_session:
                # Session expirée ou invalidée
                messagebox.showwarning(
                    "Session expirée",
                    "Votre session a expiré. Veuillez vous reconnecter."
                )
                self.logout()
                return False

            # Mettre à jour l'activité
            active_session.update_activity()
            self.current_user.last_activity = active_session.last_activity

            return True

        return False

    def force_logout_if_needed(self):
        """Force la déconnexion si nécessaire"""
        if not self.check_session_validity():
            self.logout()

    def run(self):
        """Lance l'application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
