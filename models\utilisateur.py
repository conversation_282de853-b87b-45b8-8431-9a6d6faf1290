"""
Modèle pour la gestion des utilisateurs avec sécurité avancée
"""
from models.base_model import BaseModel
from database.db_manager import DatabaseManager
import hashlib
from datetime import datetime, timedelta
import json
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from security.security_manager import security_manager
    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False
    security_manager = None

try:
    from security.rbac_manager import rbac_manager, Permission
    RBAC_AVAILABLE = True
except ImportError:
    RBAC_AVAILABLE = False
    rbac_manager = None
    Permission = None

class Utilisateur(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "utilisateurs"
        self.nom_utilisateur = ""
        self.mot_de_passe_hash = ""
        self.nom_complet = ""
        self.role = ""  # gerant, serveur, caissier
        self.actif = True

        # Nouveaux champs de sécurité
        self.derniere_connexion = None
        self.mot_de_passe_expire_le = None
        self.force_changement_mdp = False
        self.tentatives_connexion_echouees = 0
        self.verrouille_jusqu = None
        self.historique_mots_de_passe = ""  # JSON des anciens hashs
        self.session_id = None
        self.permissions_personnalisees = ""  # JSON des permissions spécifiques
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'nom_utilisateur': self.nom_utilisateur,
            'mot_de_passe_hash': self.mot_de_passe_hash,
            'nom_complet': self.nom_complet,
            'role': self.role,
            'actif': self.actif,
            'derniere_connexion': self.derniere_connexion,
            'mot_de_passe_expire_le': self.mot_de_passe_expire_le,
            'force_changement_mdp': self.force_changement_mdp,
            'tentatives_connexion_echouees': self.tentatives_connexion_echouees,
            'verrouille_jusqu': self.verrouille_jusqu,
            'historique_mots_de_passe': self.historique_mots_de_passe,
            'session_id': self.session_id,
            'permissions_personnalisees': self.permissions_personnalisees
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.nom_utilisateur = row[1]
        self.mot_de_passe_hash = row[2]
        self.nom_complet = row[3]
        self.role = row[4]
        self.actif = bool(row[5])
        self.created_at = row[6]

        # Nouveaux champs de sécurité (avec gestion de compatibilité)
        try:
            self.derniere_connexion = row[7] if len(row) > 7 else None
            self.mot_de_passe_expire_le = row[8] if len(row) > 8 else None
            self.force_changement_mdp = bool(row[9]) if len(row) > 9 else False
            self.tentatives_connexion_echouees = row[10] if len(row) > 10 else 0
            self.verrouille_jusqu = row[11] if len(row) > 11 else None
            self.historique_mots_de_passe = row[12] if len(row) > 12 else ""
            self.session_id = row[13] if len(row) > 13 else None
            self.permissions_personnalisees = row[14] if len(row) > 14 else ""
        except IndexError:
            # Compatibilité avec l'ancienne structure
            self.derniere_connexion = None
            self.mot_de_passe_expire_le = None
            self.force_changement_mdp = False
            self.tentatives_connexion_echouees = 0
            self.verrouille_jusqu = None
            self.historique_mots_de_passe = ""
            self.session_id = None
            self.permissions_personnalisees = ""
    
    def set_password(self, password, force_change=False):
        """Définit le mot de passe avec sécurité avancée"""
        if SECURITY_AVAILABLE:
            # Valider la force du mot de passe
            is_valid, errors = security_manager.validate_password_strength(password)
            if not is_valid:
                raise ValueError(f"Mot de passe non conforme: {', '.join(errors)}")

            # Vérifier l'historique des mots de passe
            if self.is_password_in_history(password):
                raise ValueError("Ce mot de passe a déjà été utilisé récemment")

            # Sauvegarder l'ancien mot de passe dans l'historique
            if self.mot_de_passe_hash:
                self.add_password_to_history(self.mot_de_passe_hash)

            # Hasher le nouveau mot de passe
            self.mot_de_passe_hash = security_manager.hash_password(password)

            # Définir la date d'expiration
            expiry_days = security_manager.security_config['force_password_change_days']
            self.mot_de_passe_expire_le = (datetime.now() + timedelta(days=expiry_days)).isoformat()
        else:
            # Fallback vers l'ancienne méthode
            self.mot_de_passe_hash = DatabaseManager.hash_password(password)

        # Réinitialiser les flags de sécurité
        self.force_changement_mdp = force_change
        self.tentatives_connexion_echouees = 0
        self.verrouille_jusqu = None

    def verify_password(self, password):
        """Vérifie le mot de passe avec sécurité avancée"""
        if SECURITY_AVAILABLE:
            return security_manager.verify_password(password, self.mot_de_passe_hash)
        else:
            return DatabaseManager.hash_password(password) == self.mot_de_passe_hash

    def is_password_in_history(self, password):
        """Vérifie si le mot de passe est dans l'historique"""
        if not self.historique_mots_de_passe or not SECURITY_AVAILABLE:
            return False

        try:
            history = json.loads(self.historique_mots_de_passe)
            for old_hash in history:
                if security_manager.verify_password(password, old_hash):
                    return True
        except (json.JSONDecodeError, Exception):
            pass

        return False

    def add_password_to_history(self, password_hash):
        """Ajoute un mot de passe à l'historique"""
        if not password_hash or not SECURITY_AVAILABLE:
            return

        try:
            history = json.loads(self.historique_mots_de_passe) if self.historique_mots_de_passe else []
        except json.JSONDecodeError:
            history = []

        # Ajouter le nouveau hash
        history.append(password_hash)

        # Limiter la taille de l'historique
        max_history = security_manager.security_config['password_history_count']
        if len(history) > max_history:
            history = history[-max_history:]

        self.historique_mots_de_passe = json.dumps(history)

    def is_account_locked(self):
        """Vérifie si le compte est verrouillé"""
        if not self.verrouille_jusqu:
            return False

        try:
            locked_until = datetime.fromisoformat(self.verrouille_jusqu)
            return datetime.now() < locked_until
        except (ValueError, TypeError):
            return False

    def is_password_expired(self):
        """Vérifie si le mot de passe a expiré"""
        if not self.mot_de_passe_expire_le:
            return False

        try:
            expires_at = datetime.fromisoformat(self.mot_de_passe_expire_le)
            return datetime.now() > expires_at
        except (ValueError, TypeError):
            return False

    def lock_account(self, duration_minutes=15):
        """Verrouille le compte pour une durée donnée"""
        locked_until = datetime.now() + timedelta(minutes=duration_minutes)
        self.verrouille_jusqu = locked_until.isoformat()
        self.save()

    def unlock_account(self):
        """Déverrouille le compte"""
        self.verrouille_jusqu = None
        self.tentatives_connexion_echouees = 0
        self.save()

    def record_failed_login(self):
        """Enregistre une tentative de connexion échouée"""
        self.tentatives_connexion_echouees += 1

        # Verrouiller après 5 tentatives
        if self.tentatives_connexion_echouees >= 5:
            self.lock_account()

        self.save()

    def record_successful_login(self):
        """Enregistre une connexion réussie"""
        self.derniere_connexion = datetime.now().isoformat()
        self.tentatives_connexion_echouees = 0
        self.verrouille_jusqu = None

        # Créer une session si le gestionnaire de sécurité est disponible
        if SECURITY_AVAILABLE:
            self.session_id = security_manager.create_session(self.id)

        self.save()

    def logout(self):
        """Déconnecte l'utilisateur"""
        if SECURITY_AVAILABLE and self.session_id:
            security_manager.invalidate_session(self.session_id)

        self.session_id = None
        self.save()

    def get_permissions(self):
        """Retourne les permissions de l'utilisateur"""
        # Permissions de base par rôle
        base_permissions = {
            'gerant': [
                'manage_users', 'manage_products', 'manage_stock', 'manage_suppliers',
                'manage_clients', 'view_reports', 'manage_settings', 'view_audit',
                'sell', 'manage_cash', 'export_data', 'backup_restore'
            ],
            'serveur': [
                'sell', 'view_products', 'view_stock', 'view_clients'
            ],
            'caissier': [
                'sell', 'view_products', 'view_stock', 'view_clients', 'manage_cash'
            ]
        }

        permissions = base_permissions.get(self.role, [])

        # Ajouter les permissions personnalisées
        if self.permissions_personnalisees:
            try:
                custom_perms = json.loads(self.permissions_personnalisees)
                permissions.extend(custom_perms.get('additional', []))

                # Retirer les permissions révoquées
                for revoked in custom_perms.get('revoked', []):
                    if revoked in permissions:
                        permissions.remove(revoked)
            except (json.JSONDecodeError, Exception):
                pass

        return list(set(permissions))  # Supprimer les doublons

    def has_permission(self, permission):
        """Vérifie si l'utilisateur a une permission spécifique"""
        return permission in self.get_permissions()

    def add_permission(self, permission):
        """Ajoute une permission personnalisée"""
        try:
            custom_perms = json.loads(self.permissions_personnalisees) if self.permissions_personnalisees else {}
        except json.JSONDecodeError:
            custom_perms = {}

        if 'additional' not in custom_perms:
            custom_perms['additional'] = []

        if permission not in custom_perms['additional']:
            custom_perms['additional'].append(permission)

        # Retirer de la liste des révoquées si présent
        if 'revoked' in custom_perms and permission in custom_perms['revoked']:
            custom_perms['revoked'].remove(permission)

        self.permissions_personnalisees = json.dumps(custom_perms)

    def revoke_permission(self, permission):
        """Révoque une permission"""
        try:
            custom_perms = json.loads(self.permissions_personnalisees) if self.permissions_personnalisees else {}
        except json.JSONDecodeError:
            custom_perms = {}

        if 'revoked' not in custom_perms:
            custom_perms['revoked'] = []

        if permission not in custom_perms['revoked']:
            custom_perms['revoked'].append(permission)

        # Retirer de la liste des ajoutées si présent
        if 'additional' in custom_perms and permission in custom_perms['additional']:
            custom_perms['additional'].remove(permission)

        self.permissions_personnalisees = json.dumps(custom_perms)
    
    def has_permission(self, action):
        """Vérifie si l'utilisateur a la permission pour une action"""
        permissions = {
            'gerant': ['all'],
            'serveur': ['vente', 'stock_view', 'client_credit'],
            'caissier': ['vente', 'stock_view']
        }
        
        role_permissions = permissions.get(self.role, [])
        return 'all' in role_permissions or action in role_permissions
    
    def can_manage_users(self):
        """Vérifie si l'utilisateur peut gérer les autres utilisateurs"""
        return self.role == 'gerant'
    
    def can_manage_stock(self):
        """Vérifie si l'utilisateur peut gérer le stock"""
        return self.role in ['gerant']
    
    def can_view_reports(self):
        """Vérifie si l'utilisateur peut voir les rapports"""
        return self.role in ['gerant']
    
    def can_manage_suppliers(self):
        """Vérifie si l'utilisateur peut gérer les fournisseurs"""
        return self.role == 'gerant'
    
    @classmethod
    def authenticate(cls, nom_utilisateur, mot_de_passe):
        """Authentifie un utilisateur"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE nom_utilisateur = ? AND actif = 1"
        result = instance.db.execute_query(query, (nom_utilisateur,))
        
        if result:
            instance._load_from_row(result[0])
            if instance.verify_password(mot_de_passe):
                return instance
        return None
    
    @classmethod
    def find_by_username(cls, nom_utilisateur):
        """Trouve un utilisateur par son nom d'utilisateur"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE nom_utilisateur = ?"
        result = instance.db.execute_query(query, (nom_utilisateur,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    def create_session(self):
        """Crée une session pour l'utilisateur"""
        import uuid
        token = str(uuid.uuid4())
        
        query = """
            INSERT INTO sessions (utilisateur_id, token, debut_session, actif)
            VALUES (?, ?, ?, ?)
        """
        self.db.execute_query(query, (self.id, token, datetime.now(), True))
        return token
    
    def end_session(self, token):
        """Termine une session"""
        query = """
            UPDATE sessions 
            SET fin_session = ?, actif = 0 
            WHERE utilisateur_id = ? AND token = ? AND actif = 1
        """
        self.db.execute_query(query, (datetime.now(), self.id, token))
    
    @classmethod
    def find_by_session_token(cls, token):
        """Trouve un utilisateur par son token de session"""
        instance = cls()
        query = """
            SELECT u.* FROM utilisateurs u
            JOIN sessions s ON u.id = s.utilisateur_id
            WHERE s.token = ? AND s.actif = 1 AND u.actif = 1
        """
        result = instance.db.execute_query(query, (token,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    def log_action(self, action, table_affectee=None, enregistrement_id=None, 
                   anciennes_valeurs=None, nouvelles_valeurs=None):
        """Enregistre une action dans les logs d'audit"""
        query = """
            INSERT INTO audit_logs (utilisateur_id, action, table_affectee, 
                                  enregistrement_id, anciennes_valeurs, nouvelles_valeurs)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        self.db.execute_query(query, (
            self.id, action, table_affectee, enregistrement_id,
            str(anciennes_valeurs) if anciennes_valeurs else None,
            str(nouvelles_valeurs) if nouvelles_valeurs else None
        ))
    
    def get_recent_actions(self, limit=10):
        """Récupère les actions récentes de l'utilisateur"""
        query = """
            SELECT action, table_affectee, timestamp 
            FROM audit_logs 
            WHERE utilisateur_id = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
        """
        return self.db.execute_query(query, (self.id, limit))
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom_utilisateur': self.nom_utilisateur,
            'nom_complet': self.nom_complet,
            'role': self.role,
            'actif': self.actif,
            'created_at': self.created_at
        }

    # Méthodes RBAC avancées
    def sync_with_rbac(self):
        """Synchronise l'utilisateur avec le système RBAC"""
        if RBAC_AVAILABLE:
            rbac_manager.sync_with_user_model(self.id, self.role)

    def has_rbac_permission(self, permission):
        """Vérifie une permission RBAC"""
        if RBAC_AVAILABLE and Permission:
            if isinstance(permission, str):
                # Convertir string en Permission enum
                try:
                    permission = Permission(permission)
                except ValueError:
                    return False
            return rbac_manager.user_has_permission(self.id, permission)
        return False

    def get_rbac_permissions(self):
        """Retourne les permissions RBAC de l'utilisateur"""
        if RBAC_AVAILABLE:
            return rbac_manager.get_user_permissions(self.id)
        return []

    def get_rbac_roles(self):
        """Retourne les rôles RBAC de l'utilisateur"""
        if RBAC_AVAILABLE:
            return rbac_manager.get_user_roles(self.id)
        return []

    def assign_rbac_role(self, role_name):
        """Assigne un rôle RBAC"""
        if RBAC_AVAILABLE:
            return rbac_manager.assign_role_to_user(self.id, role_name)
        return False

    def remove_rbac_role(self, role_name):
        """Retire un rôle RBAC"""
        if RBAC_AVAILABLE:
            return rbac_manager.remove_role_from_user(self.id, role_name)
        return False

    def can_sell(self):
        """Vérifie si l'utilisateur peut effectuer des ventes"""
        if RBAC_AVAILABLE and Permission:
            return self.has_rbac_permission(Permission.SALE_CREATE)
        return self.role in ['gerant', 'serveur', 'caissier']
