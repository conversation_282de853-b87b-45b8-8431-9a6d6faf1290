# 🍺 Application de Gestion de Stock pour Bar

Une application desktop complète développée en Python avec tkinter pour la gestion de stock d'un bar, incluant toutes les fonctionnalités avancées demandées dans le cahier des charges.

## 📋 Fonctionnalités Implémentées

### ✅ Modules Principaux

1. **🧾 Module Produits**
   - Gestion complète des produits (CRUD)
   - Catégorisation (bières, spiritueux, sodas, snacks, etc.)
   - Codes-barres, prix d'achat/vente, seuils de stock
   - Recherche et filtrage avancés

2. **👤 Module Gestion des Utilisateurs**
   - Système d'authentification sécurisé
   - Rôles et permissions (gérant, serveur, caissier)
   - Traçabilité des actions

3. **📦 Module Gestion de Stock**
   - Suivi en temps réel du stock
   - Alertes de stock minimum
   - Historique des mouvements
   - Interface d'inventaire

4. **🛒 Module Point de Vente (POS)**
   - Interface de vente intuitive
   - Support de plusieurs modes de paiement
   - Génération de tickets
   - Déduction automatique du stock

5. **📊 Module Rapports et Statistiques**
   - Rapports de ventes détaillés
   - Statistiques par produit et vendeur
   - Analyses de rentabilité
   - Export des données

6. **🏠 Tableau de Bord**
   - Vue d'ensemble en temps réel
   - Statistiques du jour
   - Alertes importantes
   - Actions rapides

7. **💳 Module Clients à Crédit**
   - Gestion des comptes clients
   - Suivi des limites de crédit
   - Historique des paiements
   - Alertes de dépassement

8. **📥 Module Approvisionnements**
   - Gestion des commandes fournisseurs
   - Réception de marchandises
   - Mise à jour automatique des stocks
   - Suivi des factures

9. **⚙️ Module Paramètres et Maintenance**
   - Configuration de l'application
   - Sauvegarde et restauration
   - Export de données (CSV, JSON, SQL)
   - Maintenance de la base de données

### 🔧 Fonctionnalités Techniques

- **Base de données SQLite** intégrée
- **Interface graphique moderne** avec tkinter
- **Architecture MVC** bien structurée
- **Système de logs** et d'audit
- **Gestion des erreurs** robuste
- **Données d'exemple** pré-chargées
- **Système de sauvegarde** automatique
- **Export multi-formats** (CSV, JSON, SQL, Excel)
- **Dialogues modaux** pour toutes les opérations

## 🚀 Installation et Lancement

### Prérequis

- Python 3.7 ou supérieur
- Modules Python standard (tkinter, sqlite3, etc.)

### Installation

1. **Cloner ou télécharger** le projet dans un dossier
2. **Ouvrir un terminal** dans le dossier du projet
3. **Installer les dépendances optionnelles** (recommandé) :

```bash
pip install -r requirements.txt
```

4. **Lancer l'application** :

**Windows :**
```bash
start.bat
```

**Linux/Mac :**
```bash
chmod +x start.sh
./start.sh
```

**Ou directement avec Python :**
```bash
python main.py
```

### Premier Lancement

Au premier lancement, l'application :
- Crée automatiquement la base de données SQLite
- Initialise les données d'exemple (produits, catégories, utilisateurs)
- Affiche la fenêtre de connexion

## 🔐 Connexion

### Comptes par défaut

**Administrateur (Gérant)**
- Nom d'utilisateur : `admin`
- Mot de passe : `admin123`
- Permissions : Accès complet à toutes les fonctionnalités

**Serveur**
- Nom d'utilisateur : `serveur1`
- Mot de passe : `serveur123`
- Permissions : Ventes, consultation stock, gestion crédits clients

**Caissier**
- Nom d'utilisateur : `caissier1`
- Mot de passe : `caissier123`
- Permissions : Ventes, consultation stock

## 📖 Guide d'Utilisation

### 🏠 Tableau de Bord

Le tableau de bord affiche :
- **Statistiques du jour** : CA, nombre de ventes, ticket moyen
- **Alertes** : Produits en stock faible
- **Ventes récentes** : Dernières transactions
- **Actions rapides** : Accès direct aux fonctions principales

### 🛒 Point de Vente

1. **Rechercher un produit** dans la liste ou par code-barres
2. **Double-cliquer** pour ajouter au panier
3. **Saisir la quantité** désirée
4. **Choisir le mode de paiement** (espèces, carte, mobile, crédit)
5. **Finaliser la vente** pour générer le ticket

### 📦 Gestion des Produits

- **Consulter** la liste complète des produits
- **Filtrer** par catégorie ou rechercher par nom
- **Voir les détails** d'un produit (stock, prix, marge)
- **Modifier** les informations produit
- **Ajuster le stock** manuellement

### 📊 Gestion du Stock

- **Onglet État du stock** : Vue d'ensemble avec filtres
- **Onglet Mouvements** : Historique des entrées/sorties
- **Onglet Alertes** : Produits nécessitant une attention

### 📈 Rapports

- **Rapports de ventes** : Évolution, produits populaires, performance vendeurs
- **Rapports de stock** : État, mouvements, valorisation
- **Rapports financiers** : CA, marges, modes de paiement

### 👥 Gestion des Utilisateurs (Gérant uniquement)

- **Créer** de nouveaux utilisateurs
- **Modifier** les informations et rôles
- **Activer/Désactiver** des comptes
- **Consulter** l'activité des utilisateurs

## 🗂️ Structure du Projet

```
bar-stock-app/
├── main.py                 # Point d'entrée de l'application
├── README.md              # Documentation
├── bar_stock.db           # Base de données SQLite (créée automatiquement)
├── bar_stock.log          # Fichier de logs
├── database/
│   └── db_manager.py      # Gestionnaire de base de données
├── models/                # Modèles de données
│   ├── base_model.py
│   ├── produit.py
│   ├── utilisateur.py
│   ├── vente.py
│   ├── detail_vente.py
│   ├── mouvement_stock.py
│   ├── categorie.py
│   ├── fournisseur.py
│   └── client_credit.py
└── gui/                   # Interface graphique
    ├── main_window.py     # Fenêtre principale
    ├── login_window.py    # Fenêtre de connexion
    ├── dashboard.py       # Tableau de bord
    ├── produits_window.py # Gestion des produits
    ├── ventes_window.py   # Point de vente et historique
    ├── stock_window.py    # Gestion du stock
    ├── utilisateurs_window.py # Gestion des utilisateurs
    └── rapports_window.py # Rapports et statistiques
```

## 🎯 Fonctionnalités Avancées à Implémenter

Les bases sont en place pour ajouter facilement :

- **📱 Module Mobile** : Interface simplifiée pour tablettes
- **📥 Module Approvisionnement** : Gestion des commandes fournisseurs
- **💳 Module Crédits Clients** : Suivi des comptes clients
- **🔐 Module Sécurité** : Logs d'audit détaillés
- **📤 Module Export** : Export Excel/PDF des rapports
- **🖨️ Intégration imprimante** : Impression automatique des tickets
- **🌐 Accès distant** : Interface web pour le gérant

## 🛠️ Développement

### Architecture

L'application suit une architecture MVC :
- **Models** : Gestion des données et logique métier
- **Views** : Interface graphique tkinter
- **Controllers** : Logique de contrôle dans les fenêtres

### Base de Données

Structure SQLite avec 12 tables principales :
- Produits, catégories, fournisseurs
- Utilisateurs, sessions, audit_logs
- Ventes, détails_vente, mouvements_stock
- Clients_credit, approvisionnements, paramètres

### Extensibilité

Le code est conçu pour être facilement extensible :
- Modèles de base réutilisables
- Interface modulaire
- Système de permissions flexible
- Configuration centralisée

## 📝 Notes Techniques

- **Python 3.7+** requis
- **SQLite** pour la persistance des données
- **tkinter** pour l'interface graphique (inclus avec Python)
- **Logging** intégré pour le débogage
- **Hashage SHA-256** pour les mots de passe
- **Gestion d'erreurs** complète

## 🤝 Support

Pour toute question ou problème :
1. Consulter les logs dans `bar_stock.log`
2. Vérifier la structure de la base de données
3. Tester avec les comptes par défaut

---

**Développé avec ❤️ en Python pour la gestion moderne des bars**
