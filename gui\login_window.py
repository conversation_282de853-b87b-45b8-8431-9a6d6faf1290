"""
Fenêtre de connexion
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.utilisateur import Utilisateur
from utils.session_manager import session_manager
from utils.security_decorators import security_manager

class LoginWindow:
    def __init__(self, parent, callback):
        self.parent = parent
        self.callback = callback
        
        # Créer la fenêtre de connexion
        self.window = tk.Toplevel(parent)
        self.window.title("Connexion - Gestion de Stock Bar")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Interface
        self.setup_ui()
        
        # Focus sur le champ nom d'utilisateur
        self.username_entry.focus()
        
        # Bind Enter key
        self.window.bind('<Return>', lambda e: self.login())
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (300 // 2)
        self.window.geometry(f"400x300+{x}+{y}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Gestion de Stock - Bar", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="Connexion", 
                                  font=('Arial', 12))
        subtitle_label.pack(pady=(0, 30))
        
        # Frame pour les champs
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Nom d'utilisateur
        ttk.Label(fields_frame, text="Nom d'utilisateur:").pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk.Entry(fields_frame, font=('Arial', 11))
        self.username_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Mot de passe
        ttk.Label(fields_frame, text="Mot de passe:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(fields_frame, show="*", font=('Arial', 11))
        self.password_entry.pack(fill=tk.X, pady=(0, 20))
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        self.login_button = ttk.Button(buttons_frame, text="Se connecter", 
                                      command=self.login)
        self.login_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        cancel_button = ttk.Button(buttons_frame, text="Annuler", 
                                  command=self.cancel)
        cancel_button.pack(side=tk.RIGHT)
        
        # Message d'erreur
        self.error_label = ttk.Label(main_frame, text="", foreground="red")
        self.error_label.pack(pady=(10, 0))
        
        # Informations par défaut
        info_frame = ttk.LabelFrame(main_frame, text="Compte par défaut", padding="10")
        info_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Label(info_frame, text="Utilisateur: admin").pack(anchor=tk.W)
        ttk.Label(info_frame, text="Mot de passe: admin123").pack(anchor=tk.W)
        
        # Bouton pour utiliser les identifiants par défaut
        default_button = ttk.Button(info_frame, text="Utiliser ces identifiants", 
                                   command=self.use_default_credentials)
        default_button.pack(pady=(10, 0))
    
    def use_default_credentials(self):
        """Utilise les identifiants par défaut"""
        self.username_entry.delete(0, tk.END)
        self.username_entry.insert(0, "admin")
        self.password_entry.delete(0, tk.END)
        self.password_entry.insert(0, "admin123")
    
    def login(self):
        """Tente de se connecter"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        # Validation
        if not username:
            self.show_error("Veuillez saisir un nom d'utilisateur")
            self.username_entry.focus()
            return
        
        if not password:
            self.show_error("Veuillez saisir un mot de passe")
            self.password_entry.focus()
            return
        
        # Désactiver le bouton pendant la vérification
        self.login_button.config(state='disabled', text="Connexion...")
        self.window.update()
        
        try:
            # Vérifier si l'utilisateur est bloqué
            if security_manager.check_user_blocked(username):
                self.show_error("Compte temporairement bloqué pour trop de tentatives de connexion")
                return

            # Authentification
            user = Utilisateur.authenticate(username, password)

            if user:
                if user.actif:
                    # Créer une session sécurisée
                    session = session_manager.create_session(
                        user.id,
                        user.nom_utilisateur,
                        user.role,
                        user.get_permissions()
                    )

                    # Ajouter la session à l'utilisateur
                    user.current_session = session
                    user.last_activity = session.last_activity

                    # Enregistrer la connexion réussie
                    security_manager.record_successful_login(username)

                    # Connexion réussie
                    self.window.destroy()
                    self.callback(user)
                else:
                    self.show_error("Ce compte est désactivé")
                    security_manager.record_failed_login(username)
            else:
                self.show_error("Nom d'utilisateur ou mot de passe incorrect")
                security_manager.record_failed_login(username)
        
        except Exception as e:
            self.show_error(f"Erreur de connexion: {str(e)}")
        
        finally:
            # Réactiver le bouton
            self.login_button.config(state='normal', text="Se connecter")
    
    def show_error(self, message):
        """Affiche un message d'erreur"""
        self.error_label.config(text=message)
        self.window.after(5000, lambda: self.error_label.config(text=""))
    
    def cancel(self):
        """Annule la connexion"""
        self.window.destroy()
        self.parent.quit()  # Ferme l'application si on annule la connexion
