"""
Fenêtre de gestion des utilisateurs
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.utilisateur import Utilisateur

class UtilisateursWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        self.selected_user = None
        
        # Vérifier les permissions
        if not self.current_user.can_manage_users():
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour gérer les utilisateurs")
            return
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion des Utilisateurs", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="➕ Nouveau", 
                  command=self.nouveau_utilisateur).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="✏️ Modifier", 
                  command=self.modifier_utilisateur).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔒 Désactiver", 
                  command=self.toggle_user_status).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Frame principal avec deux panneaux
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Panneau gauche - Liste des utilisateurs
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Panneau droit - Détails de l'utilisateur
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_users_list(left_frame)
        self.setup_user_details(right_frame)
    
    def setup_users_list(self, parent):
        """Configure la liste des utilisateurs"""
        # Treeview pour les utilisateurs
        columns = ("ID", "Nom d'utilisateur", "Nom complet", "Rôle", "Statut", "Créé le")
        self.users_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        self.users_tree.heading("ID", text="ID")
        self.users_tree.heading("Nom d'utilisateur", text="Nom d'utilisateur")
        self.users_tree.heading("Nom complet", text="Nom complet")
        self.users_tree.heading("Rôle", text="Rôle")
        self.users_tree.heading("Statut", text="Statut")
        self.users_tree.heading("Créé le", text="Créé le")
        
        # Largeur des colonnes
        self.users_tree.column("ID", width=50)
        self.users_tree.column("Nom d'utilisateur", width=120)
        self.users_tree.column("Nom complet", width=150)
        self.users_tree.column("Rôle", width=100)
        self.users_tree.column("Statut", width=80)
        self.users_tree.column("Créé le", width=120)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind de sélection
        self.users_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        self.users_tree.bind('<Double-1>', self.modifier_utilisateur)
    
    def setup_user_details(self, parent):
        """Configure le panneau des détails de l'utilisateur"""
        details_frame = ttk.LabelFrame(parent, text="Détails de l'utilisateur", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations de base
        info_frame = ttk.LabelFrame(details_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_labels = {}
        
        # Créer les labels d'information
        fields = [
            ("Nom d'utilisateur", "nom_utilisateur"),
            ("Nom complet", "nom_complet"),
            ("Rôle", "role"),
            ("Statut", "statut"),
            ("Créé le", "created_at")
        ]
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(info_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Permissions
        permissions_frame = ttk.LabelFrame(details_frame, text="Permissions", padding="10")
        permissions_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.permissions_text = tk.Text(permissions_frame, height=6, width=30, state=tk.DISABLED)
        self.permissions_text.pack(fill=tk.BOTH, expand=True)
        
        # Actions récentes
        actions_frame = ttk.LabelFrame(details_frame, text="Actions récentes", padding="10")
        actions_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview pour les actions
        action_columns = ("Date", "Action")
        self.actions_tree = ttk.Treeview(actions_frame, columns=action_columns, show="headings", height=6)
        
        for col in action_columns:
            self.actions_tree.heading(col, text=col)
            self.actions_tree.column(col, width=100)
        
        self.actions_tree.pack(fill=tk.BOTH, expand=True)
        
        # Actions sur l'utilisateur sélectionné
        user_actions_frame = ttk.LabelFrame(details_frame, text="Actions", padding="10")
        user_actions_frame.pack(fill=tk.X)
        
        ttk.Button(user_actions_frame, text="📝 Modifier", 
                  command=self.modifier_utilisateur).pack(fill=tk.X, pady=2)
        ttk.Button(user_actions_frame, text="🔑 Changer mot de passe", 
                  command=self.changer_mot_de_passe).pack(fill=tk.X, pady=2)
        ttk.Button(user_actions_frame, text="📊 Voir activité", 
                  command=self.voir_activite).pack(fill=tk.X, pady=2)
    
    def load_data(self):
        """Charge les données des utilisateurs"""
        # Nettoyer le treeview
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        try:
            utilisateurs = Utilisateur.find_all()
            
            for user in utilisateurs:
                statut = "✅ Actif" if user.actif else "❌ Inactif"
                role_icon = {"gerant": "👑", "serveur": "👨‍💼", "caissier": "💰"}.get(user.role, "👤")
                
                self.users_tree.insert("", "end", values=(
                    user.id,
                    user.nom_utilisateur,
                    user.nom_complet,
                    f"{role_icon} {user.role.title()}",
                    statut,
                    user.created_at
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des utilisateurs: {e}")
    
    def on_user_select(self, event):
        """Appelé quand un utilisateur est sélectionné"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            user_id = item['values'][0]
            self.load_user_details(user_id)
    
    def load_user_details(self, user_id):
        """Charge les détails d'un utilisateur"""
        try:
            user = Utilisateur.find_by_id(user_id)
            if user:
                self.selected_user = user
                
                # Mettre à jour les labels
                self.detail_labels['nom_utilisateur'].config(text=user.nom_utilisateur)
                self.detail_labels['nom_complet'].config(text=user.nom_complet)
                self.detail_labels['role'].config(text=user.role.title())
                self.detail_labels['statut'].config(text="Actif" if user.actif else "Inactif")
                self.detail_labels['created_at'].config(text=user.created_at)
                
                # Permissions
                self.load_user_permissions(user)
                
                # Actions récentes
                self.load_user_actions(user)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des détails: {e}")
    
    def load_user_permissions(self, user):
        """Charge les permissions de l'utilisateur"""
        self.permissions_text.config(state=tk.NORMAL)
        self.permissions_text.delete(1.0, tk.END)
        
        permissions = []
        
        if user.role == "gerant":
            permissions = [
                "✅ Gestion complète",
                "✅ Gestion des utilisateurs",
                "✅ Gestion du stock",
                "✅ Rapports et statistiques",
                "✅ Configuration système",
                "✅ Ventes et encaissements"
            ]
        elif user.role == "serveur":
            permissions = [
                "✅ Ventes et encaissements",
                "✅ Consultation du stock",
                "✅ Gestion des crédits clients",
                "❌ Gestion des utilisateurs",
                "❌ Gestion du stock",
                "❌ Rapports complets"
            ]
        elif user.role == "caissier":
            permissions = [
                "✅ Ventes et encaissements",
                "✅ Consultation du stock",
                "❌ Gestion des crédits clients",
                "❌ Gestion des utilisateurs",
                "❌ Gestion du stock",
                "❌ Rapports"
            ]
        
        self.permissions_text.insert(tk.END, "\n".join(permissions))
        self.permissions_text.config(state=tk.DISABLED)
    
    def load_user_actions(self, user):
        """Charge les actions récentes de l'utilisateur"""
        # Nettoyer le treeview
        for item in self.actions_tree.get_children():
            self.actions_tree.delete(item)
        
        try:
            actions = user.get_recent_actions(10)
            
            for action in actions:
                self.actions_tree.insert("", "end", values=(
                    action[2],  # timestamp
                    action[0]   # action
                ))
                
        except Exception as e:
            print(f"Erreur lors du chargement des actions: {e}")
    
    def nouveau_utilisateur(self):
        """Ouvre la fenêtre de création d'un nouvel utilisateur"""
        dialog = UserDialog(self, "Nouvel utilisateur")
        if dialog.result:
            try:
                user = Utilisateur()
                user.nom_utilisateur = dialog.result['nom_utilisateur']
                user.nom_complet = dialog.result['nom_complet']
                user.role = dialog.result['role']
                user.set_password(dialog.result['mot_de_passe'])
                user.save()
                
                self.load_data()
                messagebox.showinfo("Succès", "Utilisateur créé avec succès")
                
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la création: {e}")
    
    def modifier_utilisateur(self):
        """Ouvre la fenêtre de modification de l'utilisateur sélectionné"""
        if not self.selected_user:
            messagebox.showwarning("Attention", "Veuillez sélectionner un utilisateur à modifier")
            return
        
        dialog = UserDialog(self, "Modifier utilisateur", self.selected_user)
        if dialog.result:
            try:
                self.selected_user.nom_utilisateur = dialog.result['nom_utilisateur']
                self.selected_user.nom_complet = dialog.result['nom_complet']
                self.selected_user.role = dialog.result['role']
                
                if dialog.result['mot_de_passe']:
                    self.selected_user.set_password(dialog.result['mot_de_passe'])
                
                self.selected_user.save()
                
                self.load_data()
                self.load_user_details(self.selected_user.id)
                messagebox.showinfo("Succès", "Utilisateur modifié avec succès")
                
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {e}")
    
    def toggle_user_status(self):
        """Active/désactive l'utilisateur sélectionné"""
        if not self.selected_user:
            messagebox.showwarning("Attention", "Veuillez sélectionner un utilisateur")
            return
        
        if self.selected_user.id == self.current_user.id:
            messagebox.showwarning("Attention", "Vous ne pouvez pas désactiver votre propre compte")
            return
        
        action = "désactiver" if self.selected_user.actif else "activer"
        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir {action} cet utilisateur ?"):
            try:
                self.selected_user.actif = not self.selected_user.actif
                self.selected_user.save()
                
                self.load_data()
                self.load_user_details(self.selected_user.id)
                messagebox.showinfo("Succès", f"Utilisateur {action} avec succès")
                
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {e}")
    
    def changer_mot_de_passe(self):
        """Change le mot de passe de l'utilisateur sélectionné"""
        if not self.selected_user:
            messagebox.showwarning("Attention", "Veuillez sélectionner un utilisateur")
            return
        
        messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Changer mot de passe de {self.selected_user.nom_utilisateur}")
    
    def voir_activite(self):
        """Affiche l'activité détaillée de l'utilisateur"""
        if not self.selected_user:
            messagebox.showwarning("Attention", "Veuillez sélectionner un utilisateur")
            return
        
        messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Activité de {self.selected_user.nom_utilisateur}")


class UserDialog:
    def __init__(self, parent, title, user=None):
        self.result = None
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.nom_utilisateur_var = tk.StringVar(value=user.nom_utilisateur if user else "")
        self.nom_complet_var = tk.StringVar(value=user.nom_complet if user else "")
        self.role_var = tk.StringVar(value=user.role if user else "serveur")
        self.mot_de_passe_var = tk.StringVar()
        
        self.setup_ui()
    
    def center_window(self):
        """Centre la fenêtre"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (300 // 2)
        self.window.geometry(f"400x300+{x}+{y}")
    
    def setup_ui(self):
        """Configure l'interface"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Champs
        ttk.Label(main_frame, text="Nom d'utilisateur:").pack(anchor=tk.W, pady=(0, 5))
        ttk.Entry(main_frame, textvariable=self.nom_utilisateur_var).pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(main_frame, text="Nom complet:").pack(anchor=tk.W, pady=(0, 5))
        ttk.Entry(main_frame, textvariable=self.nom_complet_var).pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(main_frame, text="Rôle:").pack(anchor=tk.W, pady=(0, 5))
        role_combo = ttk.Combobox(main_frame, textvariable=self.role_var, 
                                 values=["gerant", "serveur", "caissier"], state="readonly")
        role_combo.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(main_frame, text="Mot de passe (laisser vide pour ne pas changer):").pack(anchor=tk.W, pady=(0, 5))
        ttk.Entry(main_frame, textvariable=self.mot_de_passe_var, show="*").pack(fill=tk.X, pady=(0, 20))
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="OK", command=self.ok).pack(side=tk.RIGHT)
    
    def ok(self):
        """Valide le formulaire"""
        if not self.nom_utilisateur_var.get().strip():
            messagebox.showerror("Erreur", "Le nom d'utilisateur est obligatoire")
            return
        
        if not self.nom_complet_var.get().strip():
            messagebox.showerror("Erreur", "Le nom complet est obligatoire")
            return
        
        self.result = {
            'nom_utilisateur': self.nom_utilisateur_var.get().strip(),
            'nom_complet': self.nom_complet_var.get().strip(),
            'role': self.role_var.get(),
            'mot_de_passe': self.mot_de_passe_var.get()
        }
        
        self.window.destroy()
    
    def cancel(self):
        """Annule le formulaire"""
        self.window.destroy()
