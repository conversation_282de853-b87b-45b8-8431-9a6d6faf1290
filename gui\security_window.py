"""
Fenêtre de gestion de la sécurité
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.utilisateur import Utilisateur
from models.audit_log import AuditLog

try:
    from security.security_manager import security_manager
    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False
    security_manager = None

class SecurityWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        # Vérifier les permissions
        if not self.current_user.can_manage_users():
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour gérer la sécurité")
            return
        
        if not SECURITY_AVAILABLE:
            messagebox.showerror("Erreur", "Module de sécurité non disponible")
            return
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(header_frame, text="🔐 Gestion de la Sécurité", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="⚙️ Configuration", 
                  command=self.show_security_config).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="📊 Rapport", 
                  command=self.generate_security_report).pack(side=tk.LEFT, padx=2)
        
        # Notebook pour les onglets
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Vue d'ensemble
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="Vue d'ensemble")
        
        # Onglet Comptes utilisateurs
        accounts_frame = ttk.Frame(notebook)
        notebook.add(accounts_frame, text="Comptes utilisateurs")
        
        # Onglet Sessions actives
        sessions_frame = ttk.Frame(notebook)
        notebook.add(sessions_frame, text="Sessions actives")
        
        # Onglet Tentatives de connexion
        attempts_frame = ttk.Frame(notebook)
        notebook.add(attempts_frame, text="Tentatives de connexion")

        # Onglet Protection des données
        data_protection_frame = ttk.Frame(notebook)
        notebook.add(data_protection_frame, text="Protection des données")

        self.setup_overview_tab(overview_frame)
        self.setup_accounts_tab(accounts_frame)
        self.setup_sessions_tab(sessions_frame)
        self.setup_attempts_tab(attempts_frame)
        self.setup_data_protection_tab(data_protection_frame)
    
    def setup_overview_tab(self, parent):
        """Configure l'onglet vue d'ensemble"""
        # Statistiques de sécurité
        stats_frame = ttk.LabelFrame(parent, text="Statistiques de sécurité", padding="15")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Grid pour les cartes
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        self.create_info_card(stats_grid, "Sessions actives", "0", 0, 0, "sessions_actives")
        self.create_info_card(stats_grid, "Comptes verrouillés", "0", 0, 1, "comptes_verrouilles")
        self.create_info_card(stats_grid, "Mots de passe expirés", "0", 0, 2, "mdp_expires")
        self.create_info_card(stats_grid, "Tentatives échouées (24h)", "0", 0, 3, "tentatives_echouees")
        
        # Alertes de sécurité
        alerts_frame = ttk.LabelFrame(parent, text="Alertes de sécurité", padding="10")
        alerts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.alerts_text = tk.Text(alerts_frame, height=10, state=tk.DISABLED)
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient=tk.VERTICAL, command=self.alerts_text.yview)
        self.alerts_text.configure(yscrollcommand=alerts_scrollbar.set)
        
        self.alerts_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_accounts_tab(self, parent):
        """Configure l'onglet comptes utilisateurs"""
        # Actions sur les comptes
        actions_frame = ttk.Frame(parent)
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(actions_frame, text="🔓 Déverrouiller compte", 
                  command=self.unlock_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="🔒 Verrouiller compte", 
                  command=self.lock_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="🔑 Forcer changement MDP", 
                  command=self.force_password_change).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_frame, text="🚪 Déconnecter toutes sessions", 
                  command=self.disconnect_all_sessions).pack(side=tk.LEFT, padx=5)
        
        # Liste des comptes
        accounts_frame = ttk.LabelFrame(parent, text="Comptes utilisateurs", padding="5")
        accounts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("Utilisateur", "Rôle", "Statut", "Dernière connexion", "Tentatives échouées", "Verrouillé jusqu'à")
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show="headings")
        
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=120)
        
        accounts_scrollbar = ttk.Scrollbar(accounts_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=accounts_scrollbar.set)
        
        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        accounts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_sessions_tab(self, parent):
        """Configure l'onglet sessions actives"""
        # Actions sur les sessions
        session_actions_frame = ttk.Frame(parent)
        session_actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(session_actions_frame, text="🚪 Déconnecter session", 
                  command=self.disconnect_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(session_actions_frame, text="🧹 Nettoyer sessions expirées", 
                  command=self.cleanup_expired_sessions).pack(side=tk.LEFT, padx=5)
        
        # Liste des sessions
        sessions_list_frame = ttk.LabelFrame(parent, text="Sessions actives", padding="5")
        sessions_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        session_columns = ("Utilisateur", "Session ID", "Créée le", "Dernière activité", "IP", "Durée")
        self.sessions_tree = ttk.Treeview(sessions_list_frame, columns=session_columns, show="headings")
        
        for col in session_columns:
            self.sessions_tree.heading(col, text=col)
            self.sessions_tree.column(col, width=120)
        
        sessions_scrollbar = ttk.Scrollbar(sessions_list_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_attempts_tab(self, parent):
        """Configure l'onglet tentatives de connexion"""
        # Filtres
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(filters_frame, text="Période:").pack(side=tk.LEFT)
        self.period_var = tk.StringVar(value="24h")
        period_combo = ttk.Combobox(filters_frame, textvariable=self.period_var,
                                   values=["1h", "24h", "7j", "30j"], state="readonly", width=10)
        period_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(filters_frame, text="Filtrer", command=self.load_login_attempts).pack(side=tk.LEFT)
        
        # Liste des tentatives
        attempts_list_frame = ttk.LabelFrame(parent, text="Tentatives de connexion", padding="5")
        attempts_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        attempt_columns = ("Date/Heure", "Utilisateur", "Résultat", "IP", "Détails")
        self.attempts_tree = ttk.Treeview(attempts_list_frame, columns=attempt_columns, show="headings")
        
        for col in attempt_columns:
            self.attempts_tree.heading(col, text=col)
            self.attempts_tree.column(col, width=120)
        
        attempts_scrollbar = ttk.Scrollbar(attempts_list_frame, orient=tk.VERTICAL, command=self.attempts_tree.yview)
        self.attempts_tree.configure(yscrollcommand=attempts_scrollbar.set)
        
        self.attempts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        attempts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_info_card(self, parent, title, value, row, col, key):
        """Crée une carte d'information"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(content_frame, text=title, font=('Arial', 10)).pack(anchor=tk.W)
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 12, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"info_{key}", value_label)
    
    def load_data(self):
        """Charge toutes les données"""
        self.load_security_overview()
        self.load_user_accounts()
        self.load_active_sessions()
        self.load_login_attempts()
    
    def load_security_overview(self):
        """Charge la vue d'ensemble de sécurité"""
        try:
            # Sessions actives
            sessions_count = security_manager.get_active_sessions_count()
            self.info_sessions_actives.config(text=str(sessions_count))
            
            # Comptes verrouillés
            users = Utilisateur.find_all()
            locked_count = sum(1 for user in users if user.is_account_locked())
            self.info_comptes_verrouilles.config(text=str(locked_count))
            
            # Mots de passe expirés
            expired_count = sum(1 for user in users if user.is_password_expired())
            self.info_mdp_expires.config(text=str(expired_count))
            
            # Tentatives échouées (24h)
            failed_attempts = AuditLog.find_all(
                "action LIKE '%login%' AND resultat = 'echec' AND created_at > datetime('now', '-24 hours')"
            )
            self.info_tentatives_echouees.config(text=str(len(failed_attempts)))
            
            # Charger les alertes
            self.load_security_alerts()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")
    
    def load_security_alerts(self):
        """Charge les alertes de sécurité"""
        try:
            self.alerts_text.config(state=tk.NORMAL)
            self.alerts_text.delete(1.0, tk.END)
            
            alerts = []
            
            # Comptes verrouillés
            users = Utilisateur.find_all()
            for user in users:
                if user.is_account_locked():
                    alerts.append(f"🔒 Compte verrouillé: {user.nom_complet} ({user.nom_utilisateur})")
                
                if user.is_password_expired():
                    alerts.append(f"🔑 Mot de passe expiré: {user.nom_complet} ({user.nom_utilisateur})")
                
                if user.tentatives_connexion_echouees >= 3:
                    alerts.append(f"⚠️ Tentatives multiples: {user.nom_complet} ({user.tentatives_connexion_echouees} échecs)")
            
            # Activités suspectes récentes
            suspicious_logs = AuditLog.find_all(
                "action = 'suspicious_activity_detected' AND created_at > datetime('now', '-24 hours')"
            )
            for log in suspicious_logs:
                alerts.append(f"🚨 Activité suspecte: {log.get_utilisateur_nom()} - {log.details}")
            
            if not alerts:
                alerts.append("✅ Aucune alerte de sécurité")
            
            self.alerts_text.insert(1.0, "\n".join(alerts))
            self.alerts_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"Erreur lors du chargement des alertes: {e}")
    
    def load_user_accounts(self):
        """Charge la liste des comptes utilisateurs"""
        # Nettoyer le treeview
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)
        
        try:
            users = Utilisateur.find_all()
            
            for user in users:
                # Statut
                if not user.actif:
                    status = "❌ Inactif"
                elif user.is_account_locked():
                    status = "🔒 Verrouillé"
                elif user.is_password_expired():
                    status = "🔑 MDP expiré"
                else:
                    status = "✅ Actif"
                
                # Dernière connexion
                last_login = user.derniere_connexion or "Jamais"
                if last_login != "Jamais":
                    try:
                        dt = datetime.fromisoformat(last_login)
                        last_login = dt.strftime("%d/%m/%Y %H:%M")
                    except:
                        pass
                
                # Verrouillé jusqu'à
                locked_until = ""
                if user.verrouille_jusqu:
                    try:
                        dt = datetime.fromisoformat(user.verrouille_jusqu)
                        locked_until = dt.strftime("%d/%m/%Y %H:%M")
                    except:
                        pass
                
                self.accounts_tree.insert("", "end", values=(
                    user.nom_complet,
                    user.role,
                    status,
                    last_login,
                    user.tentatives_connexion_echouees,
                    locked_until
                ), tags=(str(user.id),))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des comptes: {e}")
    
    def load_active_sessions(self):
        """Charge la liste des sessions actives"""
        # Nettoyer le treeview
        for item in self.sessions_tree.get_children():
            self.sessions_tree.delete(item)
        
        try:
            # Nettoyer d'abord les sessions expirées
            security_manager.cleanup_expired_sessions()
            
            # Charger les sessions actives
            for session_id, session_data in security_manager.active_sessions.items():
                user = Utilisateur.find_by_id(session_data['user_id'])
                if user:
                    created_at = session_data['created_at'].strftime("%d/%m/%Y %H:%M")
                    last_activity = session_data['last_activity'].strftime("%d/%m/%Y %H:%M")
                    
                    # Calculer la durée
                    duration = datetime.now() - session_data['created_at']
                    duration_str = str(duration).split('.')[0]  # Supprimer les microsecondes
                    
                    self.sessions_tree.insert("", "end", values=(
                        user.nom_complet,
                        session_id[:16] + "...",  # Tronquer l'ID
                        created_at,
                        last_activity,
                        session_data['ip'],
                        duration_str
                    ), tags=(session_id,))
                    
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des sessions: {e}")
    
    def load_login_attempts(self):
        """Charge les tentatives de connexion"""
        # Nettoyer le treeview
        for item in self.attempts_tree.get_children():
            self.attempts_tree.delete(item)
        
        try:
            # Déterminer la période
            period = self.period_var.get()
            if period == "1h":
                time_filter = "datetime('now', '-1 hour')"
            elif period == "24h":
                time_filter = "datetime('now', '-24 hours')"
            elif period == "7j":
                time_filter = "datetime('now', '-7 days')"
            else:  # 30j
                time_filter = "datetime('now', '-30 days')"
            
            # Récupérer les logs de connexion
            logs = AuditLog.find_all(
                f"action LIKE '%login%' AND created_at > {time_filter} ORDER BY created_at DESC LIMIT 100"
            )
            
            for log in logs:
                date_str = datetime.fromisoformat(log.created_at).strftime("%d/%m/%Y %H:%M:%S")
                
                result_text = "✅ Succès" if log.resultat == "succes" else "❌ Échec"
                
                self.attempts_tree.insert("", "end", values=(
                    date_str,
                    log.get_utilisateur_nom(),
                    result_text,
                    log.adresse_ip,
                    log.details
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des tentatives: {e}")
    
    def unlock_account(self):
        """Déverrouille un compte sélectionné"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un compte")
            return
        
        try:
            user_id = int(self.accounts_tree.item(selection[0])['tags'][0])
            user = Utilisateur.find_by_id(user_id)
            
            if user:
                user.unlock_account()
                messagebox.showinfo("Succès", f"Compte {user.nom_complet} déverrouillé")
                self.load_data()
                
                # Log de sécurité
                AuditLog.log_action(
                    utilisateur_id=self.current_user.id,
                    action="account_unlocked",
                    module="security",
                    objet_id=user_id,
                    details=f"Compte {user.nom_complet} déverrouillé par {self.current_user.nom_complet}"
                )
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du déverrouillage: {e}")
    
    def lock_account(self):
        """Verrouille un compte sélectionné"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un compte")
            return
        
        try:
            user_id = int(self.accounts_tree.item(selection[0])['tags'][0])
            user = Utilisateur.find_by_id(user_id)
            
            if user and user.id != self.current_user.id:  # Ne pas se verrouiller soi-même
                if messagebox.askyesno("Confirmation", f"Verrouiller le compte {user.nom_complet} ?"):
                    user.lock_account(60)  # Verrouiller pour 1 heure
                    messagebox.showinfo("Succès", f"Compte {user.nom_complet} verrouillé")
                    self.load_data()
                    
                    # Log de sécurité
                    AuditLog.log_action(
                        utilisateur_id=self.current_user.id,
                        action="account_locked_admin",
                        module="security",
                        objet_id=user_id,
                        details=f"Compte {user.nom_complet} verrouillé par {self.current_user.nom_complet}"
                    )
            elif user.id == self.current_user.id:
                messagebox.showwarning("Attention", "Vous ne pouvez pas verrouiller votre propre compte")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du verrouillage: {e}")
    
    def force_password_change(self):
        """Force le changement de mot de passe"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un compte")
            return
        
        try:
            user_id = int(self.accounts_tree.item(selection[0])['tags'][0])
            user = Utilisateur.find_by_id(user_id)
            
            if user:
                if messagebox.askyesno("Confirmation", 
                                     f"Forcer le changement de mot de passe pour {user.nom_complet} ?"):
                    user.force_changement_mdp = True
                    user.save()
                    messagebox.showinfo("Succès", f"Changement de mot de passe forcé pour {user.nom_complet}")
                    self.load_data()
                    
                    # Log de sécurité
                    AuditLog.log_action(
                        utilisateur_id=self.current_user.id,
                        action="password_change_forced",
                        module="security",
                        objet_id=user_id,
                        details=f"Changement de mot de passe forcé pour {user.nom_complet}"
                    )
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def disconnect_session(self):
        """Déconnecte une session sélectionnée"""
        selection = self.sessions_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner une session")
            return
        
        try:
            session_id = self.sessions_tree.item(selection[0])['tags'][0]
            security_manager.invalidate_session(session_id)
            messagebox.showinfo("Succès", "Session déconnectée")
            self.load_active_sessions()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la déconnexion: {e}")
    
    def disconnect_all_sessions(self):
        """Déconnecte toutes les sessions d'un utilisateur"""
        selection = self.accounts_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un compte")
            return
        
        try:
            user_id = int(self.accounts_tree.item(selection[0])['tags'][0])
            user = Utilisateur.find_by_id(user_id)
            
            if user:
                if messagebox.askyesno("Confirmation", 
                                     f"Déconnecter toutes les sessions de {user.nom_complet} ?"):
                    security_manager.invalidate_all_user_sessions(user_id)
                    messagebox.showinfo("Succès", f"Toutes les sessions de {user.nom_complet} ont été déconnectées")
                    self.load_active_sessions()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def cleanup_expired_sessions(self):
        """Nettoie les sessions expirées"""
        try:
            cleaned = security_manager.cleanup_expired_sessions()
            messagebox.showinfo("Succès", f"{cleaned} session(s) expirée(s) nettoyée(s)")
            self.load_active_sessions()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
    
    def show_security_config(self):
        """Affiche la configuration de sécurité"""
        try:
            from gui.dialogs.security_config_dialog import SecurityConfigDialog
            dialog = SecurityConfigDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
        except ImportError:
            messagebox.showinfo("Info", "Dialogue de configuration non disponible")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def generate_security_report(self):
        """Génère un rapport de sécurité"""
        try:
            report = security_manager.generate_security_report()
            
            # Créer une fenêtre pour afficher le rapport
            report_window = tk.Toplevel(self)
            report_window.title("Rapport de Sécurité")
            report_window.geometry("600x400")
            
            report_text = tk.Text(report_window, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=report_text.yview)
            report_text.configure(yscrollcommand=scrollbar.set)
            
            # Formater le rapport
            content = f"RAPPORT DE SÉCURITÉ\n"
            content += f"Généré le: {report['timestamp']}\n"
            content += "=" * 50 + "\n\n"
            
            content += f"SESSIONS ACTIVES: {report['sessions']['active_sessions']}\n"
            content += f"TENTATIVES ÉCHOUÉES: {report['failed_attempts']['failed_attempts_total']}\n"
            content += f"COMPTES VERROUILLÉS: {report['failed_attempts']['locked_accounts']}\n"
            content += f"ÉVÉNEMENTS DE SÉCURITÉ (24h): {report['recent_security_events']}\n\n"
            
            content += "CONFIGURATION DE SÉCURITÉ:\n"
            for key, value in report['config'].items():
                content += f"  {key}: {value}\n"
            
            report_text.insert(1.0, content)
            report_text.config(state=tk.DISABLED)
            
            report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du rapport: {e}")

    def setup_data_protection_tab(self, parent):
        """Configure l'onglet protection des données"""
        # Actions RGPD
        rgpd_actions_frame = ttk.LabelFrame(parent, text="Actions RGPD", padding="10")
        rgpd_actions_frame.pack(fill=tk.X, padx=10, pady=10)

        actions_grid = ttk.Frame(rgpd_actions_frame)
        actions_grid.pack(fill=tk.X)

        ttk.Button(actions_grid, text="📋 Audit des données",
                  command=self.audit_data_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_grid, text="📤 Export utilisateur",
                  command=self.export_user_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_grid, text="🗑️ Suppression RGPD",
                  command=self.gdpr_delete_user).pack(side=tk.LEFT, padx=5)
        ttk.Button(actions_grid, text="🧹 Nettoyage automatique",
                  command=self.cleanup_expired_data).pack(side=tk.LEFT, padx=5)

        # Résultats de l'audit
        audit_frame = ttk.LabelFrame(parent, text="Résultats de l'audit", padding="5")
        audit_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.audit_text = tk.Text(audit_frame, height=15, state=tk.DISABLED)
        audit_scrollbar = ttk.Scrollbar(audit_frame, orient=tk.VERTICAL, command=self.audit_text.yview)
        self.audit_text.configure(yscrollcommand=audit_scrollbar.set)

        self.audit_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        audit_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def audit_data_processing(self):
        """Lance un audit des traitements de données"""
        try:
            from security.data_protection import data_protection_manager

            audit_result = data_protection_manager.audit_data_processing()

            self.audit_text.config(state=tk.NORMAL)
            self.audit_text.delete(1.0, tk.END)

            if 'error' in audit_result:
                content = f"ERREUR LORS DE L'AUDIT\n{audit_result['error']}"
            else:
                content = f"AUDIT DE PROTECTION DES DONNÉES\n"
                content += f"Date: {audit_result['audit_date']}\n"
                content += "=" * 50 + "\n\n"

                content += "CATÉGORIES DE DONNÉES:\n"
                for category, data in audit_result['data_categories'].items():
                    content += f"  {category.upper()}:\n"
                    for key, value in data.items():
                        content += f"    {key}: {value}\n"
                content += "\n"

                content += "CONFORMITÉ DE RÉTENTION:\n"
                for data_type, compliance in audit_result['retention_compliance'].items():
                    content += f"  {data_type}:\n"
                    for key, value in compliance.items():
                        content += f"    {key}: {value}\n"
                content += "\n"

                content += "MESURES DE SÉCURITÉ:\n"
                for measure, status in audit_result['security_measures'].items():
                    status_icon = "✅" if status else "❌"
                    content += f"  {status_icon} {measure}: {status}\n"
                content += "\n"

                if audit_result['recommendations']:
                    content += "RECOMMANDATIONS:\n"
                    for rec in audit_result['recommendations']:
                        content += f"  • {rec}\n"

            self.audit_text.insert(1.0, content)
            self.audit_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'audit: {e}")

    def export_user_data(self):
        """Exporte les données d'un utilisateur (RGPD)"""
        # Dialogue pour sélectionner l'utilisateur
        from tkinter import simpledialog

        user_id = simpledialog.askinteger("Export RGPD",
                                         "ID de l'utilisateur à exporter:")

        if user_id:
            try:
                from security.data_protection import data_protection_manager

                export_data = data_protection_manager.generate_data_export(user_id)

                if 'error' in export_data:
                    messagebox.showerror("Erreur", export_data['error'])
                    return

                # Sauvegarder l'export
                import json
                filename = f"export_rgpd_user_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("Succès", f"Export sauvegardé: {filename}")

                # Log de l'action
                from models.audit_log import AuditLog
                AuditLog.log_action(
                    utilisateur_id=self.current_user.id,
                    action="data_export_gdpr",
                    module="data_protection",
                    objet_id=user_id,
                    details=f"Export RGPD généré pour utilisateur {user_id}"
                )

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export: {e}")

    def gdpr_delete_user(self):
        """Suppression RGPD d'un utilisateur"""
        from tkinter import simpledialog

        user_id = simpledialog.askinteger("Suppression RGPD",
                                         "ID de l'utilisateur à supprimer/anonymiser:")

        if user_id:
            if messagebox.askyesno("Confirmation",
                                  "Cette action va anonymiser les données de l'utilisateur.\n"
                                  "Cette action est irréversible.\n\n"
                                  "Continuer ?"):
                try:
                    from security.data_protection import data_protection_manager

                    result = data_protection_manager.delete_user_data(user_id, anonymize_only=True)

                    if result['success']:
                        actions = '\n'.join(result['actions_performed'])
                        messagebox.showinfo("Succès", f"Suppression RGPD effectuée:\n\n{actions}")
                        self.load_data()  # Recharger les données
                    else:
                        error_msg = result.get('error', 'Erreur inconnue')
                        messagebox.showerror("Erreur", f"Erreur lors de la suppression: {error_msg}")

                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur: {e}")

    def cleanup_expired_data(self):
        """Nettoie les données expirées"""
        if messagebox.askyesno("Confirmation",
                              "Nettoyer automatiquement les données expirées ?\n"
                              "Cette action supprimera définitivement les anciennes données."):
            try:
                from models.audit_log import AuditLog

                # Nettoyer les anciens logs
                deleted_logs = AuditLog.nettoyer_anciens_logs(365)  # 1 an

                # Nettoyer les sessions expirées
                cleaned_sessions = security_manager.cleanup_expired_sessions()

                message = f"Nettoyage effectué:\n"
                message += f"• {deleted_logs} anciens logs supprimés\n"
                message += f"• {cleaned_sessions} sessions expirées nettoyées"

                messagebox.showinfo("Succès", message)

                # Log de l'action
                AuditLog.log_action(
                    utilisateur_id=self.current_user.id,
                    action="data_cleanup",
                    module="data_protection",
                    details=f"Nettoyage automatique: {deleted_logs} logs, {cleaned_sessions} sessions"
                )

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
