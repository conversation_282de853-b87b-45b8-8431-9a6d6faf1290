"""
Dialogue pour la création/modification de catégories
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.categorie import Categorie

class CategorieDialog:
    def __init__(self, parent, title="Nouvelle catégorie", categorie=None):
        self.result = None
        self.categorie = categorie
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("400x250")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.nom_var = tk.StringVar()
        self.description_var = tk.StringVar()
        
        # Interface
        self.setup_ui()
        
        # Charger les données si modification
        if self.categorie:
            self.load_category_data()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (250 // 2)
        self.window.geometry(f"400x250+{x}+{y}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Nom de la catégorie
        ttk.Label(main_frame, text="Nom de la catégorie *:").pack(anchor=tk.W, pady=(0, 5))
        nom_entry = ttk.Entry(main_frame, textvariable=self.nom_var, font=('Arial', 11))
        nom_entry.pack(fill=tk.X, pady=(0, 15))
        nom_entry.focus()
        
        # Description
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W, pady=(0, 5))
        description_entry = ttk.Entry(main_frame, textvariable=self.description_var, font=('Arial', 11))
        description_entry.pack(fill=tk.X, pady=(0, 20))
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Enregistrer", command=self.save).pack(side=tk.RIGHT)
        
        # Bind Enter key
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def load_category_data(self):
        """Charge les données de la catégorie pour modification"""
        if not self.categorie:
            return
        
        self.nom_var.set(self.categorie.nom)
        self.description_var.set(self.categorie.description or "")
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Nom obligatoire
        nom = self.nom_var.get().strip()
        if not nom:
            errors.append("Le nom de la catégorie est obligatoire")
        elif len(nom) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")
        
        # Vérifier l'unicité du nom
        if nom:
            existing = Categorie.find_all("nom = ?", (nom,))
            if existing and (not self.categorie or existing[0].id != self.categorie.id):
                errors.append("Une catégorie avec ce nom existe déjà")
        
        return errors
    
    def save(self):
        """Sauvegarde la catégorie"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            # Créer ou modifier la catégorie
            if self.categorie:
                categorie = self.categorie
            else:
                categorie = Categorie()
            
            # Remplir les données
            categorie.nom = self.nom_var.get().strip()
            categorie.description = self.description_var.get().strip() or None
            
            # Sauvegarder
            categorie.save()
            
            self.result = categorie.to_dict()
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
