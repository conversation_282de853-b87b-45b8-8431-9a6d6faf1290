"""
Gestionnaire de notifications pour l'application
"""
import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime, timedelta
import logging

class NotificationManager:
    def __init__(self):
        self.notifications = []
        self.notification_window = None
        self.auto_check_enabled = True
        self.check_interval = 300  # 5 minutes
        self.last_check = None
        
        # Démarrer le thread de vérification automatique
        self.start_auto_check()
    
    def add_notification(self, title, message, type_notif="info", priority="normal"):
        """Ajoute une notification"""
        notification = {
            'id': len(self.notifications) + 1,
            'title': title,
            'message': message,
            'type': type_notif,  # info, warning, error, success
            'priority': priority,  # low, normal, high, urgent
            'timestamp': datetime.now(),
            'read': False,
            'dismissed': False
        }
        
        self.notifications.append(notification)
        
        # Afficher immédiatement si priorité élevée
        if priority in ['high', 'urgent']:
            self.show_notification(notification)
        
        logging.info(f"Notification ajoutée: {title}")
        return notification['id']
    
    def show_notification(self, notification):
        """Affiche une notification à l'écran"""
        try:
            if notification['type'] == 'error':
                messagebox.showerror(notification['title'], notification['message'])
            elif notification['type'] == 'warning':
                messagebox.showwarning(notification['title'], notification['message'])
            elif notification['type'] == 'success':
                messagebox.showinfo(notification['title'], notification['message'])
            else:
                messagebox.showinfo(notification['title'], notification['message'])
            
            notification['read'] = True
            
        except Exception as e:
            logging.error(f"Erreur lors de l'affichage de la notification: {e}")
    
    def check_stock_alerts(self):
        """Vérifie les alertes de stock"""
        try:
            from models.produit import Produit
            
            # Récupérer les produits en stock faible
            produits_faible_stock = Produit.find_produits_stock_faible()
            
            if produits_faible_stock:
                # Grouper par niveau d'urgence
                ruptures = [p for p in produits_faible_stock if p.stock_actuel == 0]
                stock_critique = [p for p in produits_faible_stock if 0 < p.stock_actuel <= p.seuil_minimum / 2]
                stock_faible = [p for p in produits_faible_stock if p.stock_actuel > p.seuil_minimum / 2]
                
                # Notifications pour ruptures
                if ruptures:
                    message = f"{len(ruptures)} produit(s) en rupture de stock:\n"
                    message += "\n".join([f"• {p.nom}" for p in ruptures[:5]])
                    if len(ruptures) > 5:
                        message += f"\n... et {len(ruptures) - 5} autres"
                    
                    self.add_notification(
                        "RUPTURE DE STOCK",
                        message,
                        "error",
                        "urgent"
                    )
                
                # Notifications pour stock critique
                if stock_critique:
                    message = f"{len(stock_critique)} produit(s) en stock critique:\n"
                    message += "\n".join([f"• {p.nom} ({p.stock_actuel} {p.unite})" for p in stock_critique[:5]])
                    if len(stock_critique) > 5:
                        message += f"\n... et {len(stock_critique) - 5} autres"
                    
                    self.add_notification(
                        "⚠️ Stock Critique",
                        message,
                        "warning",
                        "high"
                    )
                
                # Notifications pour stock faible (moins prioritaire)
                if stock_faible:
                    message = f"{len(stock_faible)} produit(s) en stock faible"
                    
                    self.add_notification(
                        "📋 Stock Faible",
                        message,
                        "warning",
                        "normal"
                    )
            
        except Exception as e:
            logging.error(f"Erreur lors de la vérification des alertes de stock: {e}")
    
    def check_sales_alerts(self):
        """Vérifie les alertes de ventes"""
        try:
            from models.vente import Vente
            from datetime import date
            
            # Vérifier les ventes du jour
            today = date.today()
            ventes_jour = Vente.find_ventes_periode(today, today)

            if not ventes_jour:
                self.add_notification(
                    "Aucune Vente",
                    "Aucune vente enregistrée aujourd'hui",
                    "warning",
                    "normal"
                )
            else:
                ca_jour = sum(v.total_ttc for v in ventes_jour)
                
                # Objectif quotidien (configurable)
                objectif_quotidien = 500.0  # À configurer selon le bar
                
                if ca_jour < objectif_quotidien * 0.5:
                    self.add_notification(
                        "📉 Objectif Non Atteint",
                        f"CA du jour: {ca_jour:.2f}€ (Objectif: {objectif_quotidien:.2f}€)",
                        "warning",
                        "normal"
                    )
                elif ca_jour >= objectif_quotidien:
                    self.add_notification(
                        "🎉 Objectif Atteint",
                        f"Félicitations! CA du jour: {ca_jour:.2f}€",
                        "success",
                        "normal"
                    )
            
        except Exception as e:
            logging.error(f"Erreur lors de la vérification des alertes de ventes: {e}")
    
    def check_system_alerts(self):
        """Vérifie les alertes système"""
        try:
            import os
            
            # Vérifier l'espace disque
            disk_usage = self.get_disk_usage()
            if disk_usage > 90:
                self.add_notification(
                    "💾 Espace Disque",
                    f"Espace disque faible: {disk_usage:.1f}% utilisé",
                    "warning",
                    "high"
                )
            
            # Vérifier la taille de la base de données
            db_size = self.get_database_size()
            if db_size > 100:  # 100 MB
                self.add_notification(
                    "🗄️ Base de Données",
                    f"Base de données volumineuse: {db_size:.1f} MB",
                    "info",
                    "normal"
                )
            
        except Exception as e:
            logging.error(f"Erreur lors de la vérification des alertes système: {e}")
    
    def get_disk_usage(self):
        """Retourne le pourcentage d'utilisation du disque"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            return (used / total) * 100
        except:
            return 0
    
    def get_database_size(self):
        """Retourne la taille de la base de données en MB"""
        try:
            import os
            if os.path.exists("bar_stock.db"):
                size_bytes = os.path.getsize("bar_stock.db")
                return size_bytes / (1024 * 1024)  # Convertir en MB
        except:
            return 0
    
    def auto_check_alerts(self):
        """Vérification automatique des alertes"""
        while self.auto_check_enabled:
            try:
                current_time = datetime.now()
                
                # Vérifier seulement pendant les heures d'ouverture (8h-23h)
                if 8 <= current_time.hour <= 23:
                    if not self.last_check or (current_time - self.last_check).seconds >= self.check_interval:
                        self.check_stock_alerts()
                        self.check_sales_alerts()
                        self.check_system_alerts()
                        self.last_check = current_time
                
                time.sleep(60)  # Vérifier chaque minute
                
            except Exception as e:
                logging.error(f"Erreur dans la vérification automatique: {e}")
                time.sleep(60)
    
    def start_auto_check(self):
        """Démarre la vérification automatique en arrière-plan"""
        if self.auto_check_enabled:
            thread = threading.Thread(target=self.auto_check_alerts, daemon=True)
            thread.start()
            logging.info("Vérification automatique des alertes démarrée")
    
    def stop_auto_check(self):
        """Arrête la vérification automatique"""
        self.auto_check_enabled = False
        logging.info("Vérification automatique des alertes arrêtée")
    
    def get_unread_notifications(self):
        """Retourne les notifications non lues"""
        return [n for n in self.notifications if not n['read']]
    
    def get_notifications_by_priority(self, priority):
        """Retourne les notifications par priorité"""
        return [n for n in self.notifications if n['priority'] == priority and not n['dismissed']]
    
    def mark_as_read(self, notification_id):
        """Marque une notification comme lue"""
        for notification in self.notifications:
            if notification['id'] == notification_id:
                notification['read'] = True
                break
    
    def dismiss_notification(self, notification_id):
        """Supprime une notification"""
        for notification in self.notifications:
            if notification['id'] == notification_id:
                notification['dismissed'] = True
                break
    
    def clear_old_notifications(self, days=7):
        """Supprime les anciennes notifications"""
        cutoff_date = datetime.now() - timedelta(days=days)
        self.notifications = [
            n for n in self.notifications 
            if n['timestamp'] > cutoff_date or not n['dismissed']
        ]
    
    def get_notification_summary(self):
        """Retourne un résumé des notifications"""
        total = len([n for n in self.notifications if not n['dismissed']])
        unread = len(self.get_unread_notifications())
        urgent = len(self.get_notifications_by_priority('urgent'))
        high = len(self.get_notifications_by_priority('high'))
        
        return {
            'total': total,
            'unread': unread,
            'urgent': urgent,
            'high': high
        }
    
    def show_notification_center(self, parent=None):
        """Affiche le centre de notifications"""
        if self.notification_window and self.notification_window.winfo_exists():
            self.notification_window.lift()
            return
        
        self.notification_window = tk.Toplevel(parent)
        self.notification_window.title("Centre de Notifications")
        self.notification_window.geometry("600x400")
        
        # Liste des notifications
        frame = tk.Frame(self.notification_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # En-tête
        header = tk.Label(frame, text="Notifications", font=('Arial', 14, 'bold'))
        header.pack(anchor=tk.W, pady=(0, 10))
        
        # Liste scrollable
        import tkinter.ttk as ttk
        
        # Treeview pour les notifications
        columns = ("Heure", "Titre", "Type", "Priorité")
        tree = ttk.Treeview(frame, columns=columns, show="headings")
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Remplir avec les notifications
        for notification in reversed(self.notifications):
            if not notification['dismissed']:
                tree.insert("", "end", values=(
                    notification['timestamp'].strftime('%H:%M'),
                    notification['title'],
                    notification['type'].upper(),
                    notification['priority'].upper()
                ))
        
        # Boutons
        button_frame = tk.Frame(self.notification_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(button_frame, text="Marquer tout comme lu", 
                 command=self.mark_all_as_read).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Effacer anciennes", 
                 command=lambda: self.clear_old_notifications(1)).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Fermer", 
                 command=self.notification_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def mark_all_as_read(self):
        """Marque toutes les notifications comme lues"""
        for notification in self.notifications:
            notification['read'] = True

# Instance globale
notification_manager = NotificationManager()
