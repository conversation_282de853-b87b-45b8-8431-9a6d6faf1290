"""
Générateur de tickets de caisse
"""
import os
from datetime import datetime
from config import get_config

class TicketGenerator:
    def __init__(self):
        try:
            # Récupérer la configuration sans paramètres par défaut problématiques
            self.config = get_config('ui') or {}
            export_config = get_config('export') or {}
            self.company_info = export_config.get('company_info', {})
        except Exception as e:
            # Configuration par défaut si erreur
            self.config = {}
            self.company_info = {
                'name': 'MON BAR',
                'address': '',
                'phone': ''
            }
    
    def generate_ticket_text(self, vente):
        """Génère le contenu textuel du ticket"""
        lines = []
        
        # En-tête
        lines.append("=" * 40)
        lines.append(f"{self.company_info.get('name', 'MON BAR'):^40}")
        if self.company_info.get('address'):
            lines.append(f"{self.company_info.get('address'):^40}")
        if self.company_info.get('phone'):
            lines.append(f"Tel: {self.company_info.get('phone'):^34}")
        lines.append("=" * 40)
        lines.append("")
        
        # Informations de la vente
        lines.append(f"Ticket N°: {vente.numero_ticket}")
        lines.append(f"Date: {vente.date_vente}")
        lines.append(f"Vendeur: {vente.get_utilisateur_nom()}")
        if vente.client_credit_id:
            lines.append(f"Client: {vente.get_client_credit_nom()}")
        lines.append("")
        lines.append("-" * 40)
        
        # Détails des produits
        lines.append(f"{'PRODUIT':<20} {'QTE':<4} {'P.U.':<6} {'TOTAL':<8}")
        lines.append("-" * 40)
        
        for detail in vente.details:
            nom = detail.get_produit_nom()[:19]  # Limiter à 19 caractères
            lines.append(f"{nom:<20} {detail.quantite:<4} {detail.prix_unitaire:<6.2f} {detail.sous_total:<8.2f}")
        
        lines.append("-" * 40)
        
        # Totaux
        lines.append(f"{'SOUS-TOTAL:':<32} {vente.total_ht:>7.2f}")
        if vente.total_ttc != vente.total_ht:
            tva = vente.total_ttc - vente.total_ht
            lines.append(f"{'TVA:':<32} {tva:>7.2f}")
        lines.append(f"{'TOTAL TTC:':<32} {vente.total_ttc:>7.2f}")
        lines.append("")
        
        # Mode de paiement
        mode_paiement_text = {
            'cash': 'ESPECES',
            'carte': 'CARTE BANCAIRE',
            'mobile': 'PAIEMENT MOBILE',
            'credit': 'CREDIT CLIENT',
            'cheque': 'CHEQUE',
            'ticket_restaurant': 'TICKET RESTAURANT'
        }.get(vente.mode_paiement, vente.mode_paiement.upper())
        
        lines.append(f"Mode de paiement: {mode_paiement_text}")
        lines.append("")
        
        # Pied de page
        lines.append("=" * 40)
        lines.append("MERCI DE VOTRE VISITE")
        lines.append("A BIENTOT !")
        lines.append("=" * 40)
        lines.append("")
        lines.append(f"Imprime le {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        
        return "\n".join(lines)
    
    def save_ticket_to_file(self, vente, directory="tickets"):
        """Sauvegarde le ticket dans un fichier"""
        try:
            # Créer le répertoire s'il n'existe pas
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            # Nom du fichier
            filename = f"ticket_{vente.numero_ticket}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            filepath = os.path.join(directory, filename)
            
            # Générer et sauvegarder le contenu
            content = self.generate_ticket_text(vente)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"Erreur lors de la sauvegarde du ticket: {e}")
    
    def print_ticket(self, vente, printer_name=None):
        """Imprime le ticket (nécessite une imprimante configurée)"""
        try:
            # Sauvegarder d'abord le ticket
            ticket_file = self.save_ticket_to_file(vente)
            
            # Tentative d'impression selon l'OS
            import platform
            system = platform.system()
            
            if system == "Windows":
                # Windows - utiliser notepad pour imprimer
                os.system(f'notepad /p "{ticket_file}"')
            elif system == "Linux":
                # Linux - utiliser lp
                if printer_name:
                    os.system(f'lp -d {printer_name} "{ticket_file}"')
                else:
                    os.system(f'lp "{ticket_file}"')
            elif system == "Darwin":  # macOS
                # macOS - utiliser lpr
                if printer_name:
                    os.system(f'lpr -P {printer_name} "{ticket_file}"')
                else:
                    os.system(f'lpr "{ticket_file}"')
            
            return True
            
        except Exception as e:
            raise Exception(f"Erreur lors de l'impression: {e}")
    
    def generate_ticket_html(self, vente):
        """Génère le ticket en format HTML pour impression web"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Ticket {vente.numero_ticket}</title>
            <style>
                body {{
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    margin: 0;
                    padding: 20px;
                    width: 300px;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }}
                .company-name {{
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                .info-line {{
                    margin: 3px 0;
                }}
                .products-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }}
                .products-table th,
                .products-table td {{
                    text-align: left;
                    padding: 2px;
                    border-bottom: 1px solid #ccc;
                }}
                .products-table th {{
                    border-bottom: 2px solid #000;
                }}
                .total-section {{
                    border-top: 2px solid #000;
                    padding-top: 10px;
                    margin-top: 15px;
                }}
                .total-line {{
                    display: flex;
                    justify-content: space-between;
                    margin: 3px 0;
                }}
                .total-final {{
                    font-weight: bold;
                    font-size: 14px;
                }}
                .footer {{
                    text-align: center;
                    border-top: 2px solid #000;
                    padding-top: 10px;
                    margin-top: 20px;
                }}
                @media print {{
                    body {{ margin: 0; padding: 10px; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">{self.company_info.get('name', 'MON BAR')}</div>
                {f'<div>{self.company_info.get("address")}</div>' if self.company_info.get('address') else ''}
                {f'<div>Tél: {self.company_info.get("phone")}</div>' if self.company_info.get('phone') else ''}
            </div>
            
            <div class="info-line"><strong>Ticket N°:</strong> {vente.numero_ticket}</div>
            <div class="info-line"><strong>Date:</strong> {vente.date_vente}</div>
            <div class="info-line"><strong>Vendeur:</strong> {vente.get_utilisateur_nom()}</div>
            {f'<div class="info-line"><strong>Client:</strong> {vente.get_client_credit_nom()}</div>' if vente.client_credit_id else ''}
            
            <table class="products-table">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Qté</th>
                        <th>P.U.</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for detail in vente.details:
            html += f"""
                    <tr>
                        <td>{detail.get_produit_nom()}</td>
                        <td>{detail.quantite}</td>
                        <td>{detail.prix_unitaire:.2f}€</td>
                        <td>{detail.sous_total:.2f}€</td>
                    </tr>
            """
        
        mode_paiement_text = {
            'cash': 'Espèces',
            'carte': 'Carte bancaire',
            'mobile': 'Paiement mobile',
            'credit': 'Crédit client',
            'cheque': 'Chèque',
            'ticket_restaurant': 'Ticket restaurant'
        }.get(vente.mode_paiement, vente.mode_paiement)
        
        html += f"""
                </tbody>
            </table>
            
            <div class="total-section">
                <div class="total-line">
                    <span>Sous-total:</span>
                    <span>{vente.total_ht:.2f}€</span>
                </div>
        """
        
        if vente.total_ttc != vente.total_ht:
            tva = vente.total_ttc - vente.total_ht
            html += f"""
                <div class="total-line">
                    <span>TVA:</span>
                    <span>{tva:.2f}€</span>
                </div>
            """
        
        html += f"""
                <div class="total-line total-final">
                    <span>TOTAL TTC:</span>
                    <span>{vente.total_ttc:.2f}€</span>
                </div>
                <div class="total-line">
                    <span>Mode de paiement:</span>
                    <span>{mode_paiement_text}</span>
                </div>
            </div>
            
            <div class="footer">
                <div>MERCI DE VOTRE VISITE</div>
                <div>A BIENTÔT !</div>
                <div style="margin-top: 10px; font-size: 10px;">
                    Imprimé le {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def save_ticket_html(self, vente, directory="tickets"):
        """Sauvegarde le ticket en HTML"""
        try:
            # Créer le répertoire s'il n'existe pas
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            # Nom du fichier
            filename = f"ticket_{vente.numero_ticket}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            filepath = os.path.join(directory, filename)
            
            # Générer et sauvegarder le contenu
            content = self.generate_ticket_html(vente)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"Erreur lors de la sauvegarde du ticket HTML: {e}")
    
    def open_ticket_for_print(self, vente):
        """Ouvre le ticket HTML dans le navigateur pour impression"""
        try:
            ticket_file = self.save_ticket_html(vente)
            
            # Ouvrir dans le navigateur par défaut
            import webbrowser
            webbrowser.open(f'file://{os.path.abspath(ticket_file)}')
            
            return ticket_file
            
        except Exception as e:
            raise Exception(f"Erreur lors de l'ouverture du ticket: {e}")
