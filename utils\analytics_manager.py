"""
Gestionnaire d'analyses avancées pour l'application
"""
from datetime import datetime, timedelta, date
import logging
from collections import defaultdict
import json

class AnalyticsManager:
    def __init__(self):
        self.cache = {}
        self.cache_duration = 300  # 5 minutes
    
    def get_cached_result(self, key):
        """Récupère un résultat du cache s'il est encore valide"""
        if key in self.cache:
            result, timestamp = self.cache[key]
            if (datetime.now() - timestamp).seconds < self.cache_duration:
                return result
        return None
    
    def set_cached_result(self, key, result):
        """Met en cache un résultat"""
        self.cache[key] = (result, datetime.now())
    
    def analyze_sales_trends(self, days=30):
        """Analyse les tendances de ventes"""
        cache_key = f"sales_trends_{days}"
        cached = self.get_cached_result(cache_key)
        if cached:
            return cached
        
        try:
            from models.vente import Vente
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            ventes = Vente.find_ventes_periode(start_date, end_date)
            
            # Analyser par jour
            daily_sales = defaultdict(lambda: {'count': 0, 'total': 0})
            
            for vente in ventes:
                vente_date = datetime.fromisoformat(vente.date_vente).date()
                daily_sales[vente_date]['count'] += 1
                daily_sales[vente_date]['total'] += vente.total_ttc
            
            # Calculer les tendances
            dates = sorted(daily_sales.keys())
            if len(dates) < 2:
                trend = "insufficient_data"
            else:
                # Comparer première et dernière semaine
                first_week = dates[:7] if len(dates) >= 7 else dates[:len(dates)//2]
                last_week = dates[-7:] if len(dates) >= 7 else dates[len(dates)//2:]
                
                first_week_avg = sum(daily_sales[d]['total'] for d in first_week) / len(first_week)
                last_week_avg = sum(daily_sales[d]['total'] for d in last_week) / len(last_week)
                
                if last_week_avg > first_week_avg * 1.1:
                    trend = "increasing"
                elif last_week_avg < first_week_avg * 0.9:
                    trend = "decreasing"
                else:
                    trend = "stable"
            
            result = {
                'period': f"{start_date} to {end_date}",
                'total_sales': len(ventes),
                'total_revenue': sum(v.total_ttc for v in ventes),
                'daily_average': sum(daily_sales[d]['total'] for d in daily_sales) / len(daily_sales) if daily_sales else 0,
                'trend': trend,
                'daily_data': dict(daily_sales),
                'best_day': max(daily_sales.items(), key=lambda x: x[1]['total']) if daily_sales else None,
                'worst_day': min(daily_sales.items(), key=lambda x: x[1]['total']) if daily_sales else None
            }
            
            self.set_cached_result(cache_key, result)
            return result
            
        except Exception as e:
            logging.error(f"Erreur lors de l'analyse des tendances: {e}")
            return None
    
    def analyze_product_performance(self, days=30):
        """Analyse la performance des produits"""
        cache_key = f"product_performance_{days}"
        cached = self.get_cached_result(cache_key)
        if cached:
            return cached
        
        try:
            from models.vente import Vente
            from models.produit import Produit
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            ventes = Vente.find_ventes_periode(start_date, end_date)
            
            # Analyser par produit
            product_stats = defaultdict(lambda: {
                'quantity_sold': 0,
                'revenue': 0,
                'profit': 0,
                'sales_count': 0
            })
            
            for vente in ventes:
                for detail in vente.details:
                    produit = Produit.find_by_id(detail.produit_id)
                    if produit:
                        product_stats[produit.nom]['quantity_sold'] += detail.quantite
                        product_stats[produit.nom]['revenue'] += detail.sous_total
                        product_stats[produit.nom]['profit'] += (detail.prix_unitaire - produit.prix_achat) * detail.quantite
                        product_stats[produit.nom]['sales_count'] += 1
            
            # Trier par revenus
            sorted_products = sorted(
                product_stats.items(),
                key=lambda x: x[1]['revenue'],
                reverse=True
            )
            
            result = {
                'period': f"{start_date} to {end_date}",
                'total_products_sold': len(product_stats),
                'top_products': sorted_products[:10],
                'low_performers': sorted_products[-5:] if len(sorted_products) > 5 else [],
                'total_profit': sum(stats['profit'] for stats in product_stats.values()),
                'average_profit_margin': sum(
                    (stats['profit'] / stats['revenue'] * 100) if stats['revenue'] > 0 else 0
                    for stats in product_stats.values()
                ) / len(product_stats) if product_stats else 0
            }
            
            self.set_cached_result(cache_key, result)
            return result
            
        except Exception as e:
            logging.error(f"Erreur lors de l'analyse des produits: {e}")
            return None
    
    def analyze_customer_behavior(self, days=30):
        """Analyse le comportement des clients"""
        cache_key = f"customer_behavior_{days}"
        cached = self.get_cached_result(cache_key)
        if cached:
            return cached
        
        try:
            from models.vente import Vente
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            ventes = Vente.find_ventes_periode(start_date, end_date)
            
            # Analyser les heures de pointe
            hourly_sales = defaultdict(lambda: {'count': 0, 'total': 0})
            payment_methods = defaultdict(lambda: {'count': 0, 'total': 0})
            ticket_sizes = []
            
            for vente in ventes:
                # Heure de la vente
                vente_hour = datetime.fromisoformat(vente.date_vente).hour
                hourly_sales[vente_hour]['count'] += 1
                hourly_sales[vente_hour]['total'] += vente.total_ttc
                
                # Mode de paiement
                payment_methods[vente.mode_paiement]['count'] += 1
                payment_methods[vente.mode_paiement]['total'] += vente.total_ttc
                
                # Taille du ticket
                ticket_sizes.append(vente.total_ttc)
            
            # Trouver les heures de pointe
            peak_hours = sorted(
                hourly_sales.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )[:3]
            
            result = {
                'period': f"{start_date} to {end_date}",
                'total_transactions': len(ventes),
                'average_ticket_size': sum(ticket_sizes) / len(ticket_sizes) if ticket_sizes else 0,
                'median_ticket_size': sorted(ticket_sizes)[len(ticket_sizes)//2] if ticket_sizes else 0,
                'peak_hours': peak_hours,
                'payment_preferences': dict(payment_methods),
                'hourly_distribution': dict(hourly_sales)
            }
            
            self.set_cached_result(cache_key, result)
            return result
            
        except Exception as e:
            logging.error(f"Erreur lors de l'analyse du comportement client: {e}")
            return None
    
    def analyze_inventory_efficiency(self):
        """Analyse l'efficacité de l'inventaire"""
        cache_key = "inventory_efficiency"
        cached = self.get_cached_result(cache_key)
        if cached:
            return cached
        
        try:
            from models.produit import Produit
            from models.mouvement_stock import MouvementStock
            
            produits = Produit.find_all("actif = 1")
            
            inventory_stats = {}
            total_value = 0
            slow_movers = []
            fast_movers = []
            
            for produit in produits:
                # Valeur du stock
                stock_value = produit.stock_actuel * produit.prix_achat
                total_value += stock_value
                
                # Mouvements des 30 derniers jours
                end_date = date.today()
                start_date = end_date - timedelta(days=30)
                
                mouvements = MouvementStock.find_all(
                    "produit_id = ? AND type_mouvement = 'sortie' AND date_mouvement >= ?",
                    (produit.id, start_date)
                )
                
                total_sold = sum(m.quantite for m in mouvements)
                turnover_rate = total_sold / produit.stock_actuel if produit.stock_actuel > 0 else 0
                
                product_stats = {
                    'name': produit.nom,
                    'stock_value': stock_value,
                    'current_stock': produit.stock_actuel,
                    'sold_last_30_days': total_sold,
                    'turnover_rate': turnover_rate,
                    'days_of_stock': produit.stock_actuel / (total_sold / 30) if total_sold > 0 else float('inf')
                }
                
                inventory_stats[produit.id] = product_stats
                
                # Classifier les produits
                if turnover_rate < 0.1:  # Moins de 10% du stock vendu en 30 jours
                    slow_movers.append(product_stats)
                elif turnover_rate > 1.0:  # Plus de 100% du stock vendu en 30 jours
                    fast_movers.append(product_stats)
            
            result = {
                'total_inventory_value': total_value,
                'total_products': len(produits),
                'slow_movers': sorted(slow_movers, key=lambda x: x['turnover_rate'])[:10],
                'fast_movers': sorted(fast_movers, key=lambda x: x['turnover_rate'], reverse=True)[:10],
                'average_turnover': sum(stats['turnover_rate'] for stats in inventory_stats.values()) / len(inventory_stats) if inventory_stats else 0,
                'products_needing_reorder': len([p for p in produits if p.is_stock_faible()]),
                'overstocked_products': len(slow_movers)
            }
            
            self.set_cached_result(cache_key, result)
            return result
            
        except Exception as e:
            logging.error(f"Erreur lors de l'analyse de l'inventaire: {e}")
            return None
    
    def generate_business_insights(self):
        """Génère des insights business"""
        try:
            sales_trends = self.analyze_sales_trends()
            product_performance = self.analyze_product_performance()
            customer_behavior = self.analyze_customer_behavior()
            inventory_efficiency = self.analyze_inventory_efficiency()
            
            insights = []
            
            # Insights sur les ventes
            if sales_trends:
                if sales_trends['trend'] == 'increasing':
                    insights.append({
                        'type': 'positive',
                        'category': 'sales',
                        'message': f"📈 Excellente nouvelle ! Vos ventes sont en hausse avec une moyenne de {sales_trends['daily_average']:.2f}€ par jour."
                    })
                elif sales_trends['trend'] == 'decreasing':
                    insights.append({
                        'type': 'warning',
                        'category': 'sales',
                        'message': f"📉 Attention : vos ventes sont en baisse. Moyenne actuelle : {sales_trends['daily_average']:.2f}€ par jour."
                    })
            
            # Insights sur les produits
            if product_performance and product_performance['top_products']:
                top_product = product_performance['top_products'][0]
                insights.append({
                    'type': 'info',
                    'category': 'products',
                    'message': f"🏆 Votre produit star : {top_product[0]} avec {top_product[1]['revenue']:.2f}€ de revenus."
                })
            
            # Insights sur l'inventaire
            if inventory_efficiency:
                if inventory_efficiency['overstocked_products'] > 5:
                    insights.append({
                        'type': 'warning',
                        'category': 'inventory',
                        'message': f"📦 {inventory_efficiency['overstocked_products']} produits semblent en surstock. Considérez des promotions."
                    })
                
                if inventory_efficiency['products_needing_reorder'] > 0:
                    insights.append({
                        'type': 'urgent',
                        'category': 'inventory',
                        'message': f"🚨 {inventory_efficiency['products_needing_reorder']} produits nécessitent un réapprovisionnement urgent."
                    })
            
            # Insights sur les clients
            if customer_behavior and customer_behavior['peak_hours']:
                peak_hour = customer_behavior['peak_hours'][0][0]
                insights.append({
                    'type': 'info',
                    'category': 'customers',
                    'message': f"⏰ Votre heure de pointe : {peak_hour}h avec {customer_behavior['peak_hours'][0][1]['count']} ventes."
                })
            
            return {
                'generated_at': datetime.now().isoformat(),
                'insights': insights,
                'data_sources': {
                    'sales_trends': sales_trends,
                    'product_performance': product_performance,
                    'customer_behavior': customer_behavior,
                    'inventory_efficiency': inventory_efficiency
                }
            }
            
        except Exception as e:
            logging.error(f"Erreur lors de la génération des insights: {e}")
            return None
    
    def export_analytics_report(self, filename=None):
        """Exporte un rapport d'analyse complet"""
        try:
            insights = self.generate_business_insights()
            
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"analytics_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(insights, f, indent=2, ensure_ascii=False, default=str)
            
            logging.info(f"Rapport d'analyse exporté: {filename}")
            return filename
            
        except Exception as e:
            logging.error(f"Erreur lors de l'export du rapport: {e}")
            return None
    
    def clear_cache(self):
        """Vide le cache"""
        self.cache.clear()
        logging.info("Cache d'analyses vidé")

# Instance globale
analytics_manager = AnalyticsManager()
