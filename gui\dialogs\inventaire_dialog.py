"""
Dialogue d'inventaire avec support des codes-barres selon le scénario
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.produit import Produit
from models.mouvement_stock import MouvementStock

class InventaireDialog:
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.result = None
        self.inventaire_data = {}  # {produit_id: {'theorique': x, 'reel': y, 'ecart': z}}
        
        self.setup_dialog()
        self.load_products()
    
    def setup_dialog(self):
        """Configure le dialogue"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("📊 Inventaire du Stock")
        self.dialog.geometry("900x700")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Centrer la fenêtre
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
    
    def create_widgets(self):
        """Crée les widgets du dialogue"""
        # Titre
        title_frame = ttk.Frame(self.dialog)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(title_frame, text="📊 Inventaire du Stock", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Date de l'inventaire
        date_label = ttk.Label(title_frame, text=f"Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
        date_label.pack(side=tk.RIGHT)
        
        # Notebook pour organiser les sections
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Onglet Saisie rapide (codes-barres)
        saisie_frame = ttk.Frame(notebook)
        notebook.add(saisie_frame, text="🔍 Saisie rapide")
        
        # Onglet Inventaire complet
        inventaire_frame = ttk.Frame(notebook)
        notebook.add(inventaire_frame, text="📋 Inventaire complet")
        
        # Onglet Résultats
        resultats_frame = ttk.Frame(notebook)
        notebook.add(resultats_frame, text="📊 Résultats")
        
        self.create_saisie_tab(saisie_frame)
        self.create_inventaire_tab(inventaire_frame)
        self.create_resultats_tab(resultats_frame)
        
        # Boutons
        buttons_frame = ttk.Frame(self.dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(buttons_frame, text="Annuler", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="✅ Finaliser inventaire", 
                  command=self.finaliser_inventaire).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="🧮 Calculer écarts", 
                  command=self.calculer_ecarts).pack(side=tk.RIGHT, padx=(0, 10))
    
    def create_saisie_tab(self, parent):
        """Crée l'onglet de saisie rapide avec codes-barres"""
        # Instructions
        instructions_frame = ttk.LabelFrame(parent, text="📖 Instructions", padding="10")
        instructions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        instructions_text = """
🔍 Scannez le code-barres du produit ou saisissez-le manuellement
📝 Entrez la quantité réelle comptée
⏎ Appuyez sur Entrée ou cliquez sur Ajouter pour valider
📊 Les écarts seront calculés automatiquement
        """
        ttk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # Saisie
        saisie_frame = ttk.LabelFrame(parent, text="⚡ Saisie des quantités", padding="10")
        saisie_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Variables
        self.code_barre_var = tk.StringVar()
        self.quantite_reelle_var = tk.StringVar()
        self.produit_trouve_var = tk.StringVar()
        
        # Code-barres
        ttk.Label(saisie_frame, text="Code-barres:").grid(row=0, column=0, sticky=tk.W, pady=5)
        code_entry = ttk.Entry(saisie_frame, textvariable=self.code_barre_var, width=20, font=('Arial', 12))
        code_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        code_entry.bind('<Return>', self.on_code_barre_enter)
        code_entry.bind('<KeyRelease>', self.on_code_barre_change)
        code_entry.focus()
        
        # Produit trouvé
        ttk.Label(saisie_frame, text="Produit:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        ttk.Label(saisie_frame, textvariable=self.produit_trouve_var, 
                 foreground="blue", font=('Arial', 10, 'bold')).grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Quantité réelle
        ttk.Label(saisie_frame, text="Quantité réelle:").grid(row=1, column=0, sticky=tk.W, pady=5)
        quantite_entry = ttk.Entry(saisie_frame, textvariable=self.quantite_reelle_var, width=15, font=('Arial', 12))
        quantite_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        quantite_entry.bind('<Return>', self.ajouter_inventaire_item)
        
        # Bouton ajouter
        ttk.Button(saisie_frame, text="➕ Ajouter", 
                  command=self.ajouter_inventaire_item).grid(row=1, column=2, padx=(20, 0), pady=5)
        
        # Liste des saisies récentes
        recent_frame = ttk.LabelFrame(parent, text="📝 Saisies récentes", padding="5")
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("Produit", "Stock théorique", "Quantité réelle", "Écart", "Heure")
        self.recent_tree = ttk.Treeview(recent_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.recent_tree.heading(col, text=col)
            self.recent_tree.column(col, width=120)
        
        # Scrollbar
        recent_scrollbar = ttk.Scrollbar(recent_frame, orient=tk.VERTICAL, command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=recent_scrollbar.set)
        
        self.recent_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        recent_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_inventaire_tab(self, parent):
        """Crée l'onglet d'inventaire complet"""
        # Filtres
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(filters_frame, text="Filtrer par:").pack(side=tk.LEFT)
        
        self.filter_var = tk.StringVar(value="tous")
        filter_combo = ttk.Combobox(filters_frame, textvariable=self.filter_var,
                                   values=["tous", "non_inventorie", "avec_ecart", "rupture"],
                                   state="readonly", width=15)
        filter_combo.pack(side=tk.LEFT, padx=(5, 0))
        filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        ttk.Button(filters_frame, text="🔄 Actualiser", 
                  command=self.load_products).pack(side=tk.RIGHT)
        
        # Liste des produits
        products_frame = ttk.LabelFrame(parent, text="📦 Produits à inventorier", padding="5")
        products_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("Produit", "Code-barres", "Stock théorique", "Quantité réelle", "Écart", "Statut")
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show="headings")
        
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=120)
        
        # Permettre l'édition de la quantité réelle
        self.products_tree.bind('<Double-1>', self.edit_quantite_reelle)
        
        # Scrollbar
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_resultats_tab(self, parent):
        """Crée l'onglet des résultats"""
        # Statistiques
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistiques de l'inventaire", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Grid pour les statistiques
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        # Cartes de statistiques
        self.create_stat_card(stats_grid, "Produits inventoriés", "0", 0, 0, "inventories")
        self.create_stat_card(stats_grid, "Écarts positifs", "0", 0, 1, "positifs")
        self.create_stat_card(stats_grid, "Écarts négatifs", "0", 0, 2, "negatifs")
        self.create_stat_card(stats_grid, "Valeur des écarts", "0.00 €", 0, 3, "valeur_ecarts")
        
        # Détail des écarts
        ecarts_frame = ttk.LabelFrame(parent, text="📋 Détail des écarts", padding="5")
        ecarts_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        columns = ("Produit", "Stock théorique", "Stock réel", "Écart", "Valeur écart", "Type")
        self.ecarts_tree = ttk.Treeview(ecarts_frame, columns=columns, show="headings")
        
        for col in columns:
            self.ecarts_tree.heading(col, text=col)
            self.ecarts_tree.column(col, width=120)
        
        # Scrollbar
        ecarts_scrollbar = ttk.Scrollbar(ecarts_frame, orient=tk.VERTICAL, command=self.ecarts_tree.yview)
        self.ecarts_tree.configure(yscrollcommand=ecarts_scrollbar.set)
        
        self.ecarts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ecarts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_stat_card(self, parent, title, value, row, col, key):
        """Crée une carte de statistique"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(content_frame, text=title, font=('Arial', 10)).pack(anchor=tk.W)
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 12, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"stat_{key}", value_label)
    
    def load_products(self):
        """Charge les produits pour l'inventaire"""
        try:
            # Nettoyer le treeview
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
            
            # Charger tous les produits actifs
            produits = Produit.find_all("actif = 1")
            
            for produit in produits:
                # Vérifier si le produit a déjà été inventorié
                if produit.id in self.inventaire_data:
                    data = self.inventaire_data[produit.id]
                    quantite_reelle = data['reel']
                    ecart = data['ecart']
                    statut = "✅ Inventorié"
                else:
                    quantite_reelle = ""
                    ecart = ""
                    statut = "⏳ En attente"
                
                self.products_tree.insert("", "end", values=(
                    produit.nom,
                    produit.code_barre or "",
                    produit.stock_actuel,
                    quantite_reelle,
                    ecart,
                    statut
                ), tags=(str(produit.id),))
            
            self.apply_filter()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")
    
    def on_code_barre_change(self, event):
        """Appelé quand le code-barres change"""
        code_barre = self.code_barre_var.get()
        if len(code_barre) >= 3:  # Commencer la recherche après 3 caractères
            self.rechercher_produit_par_code(code_barre)
    
    def on_code_barre_enter(self, event):
        """Appelé quand Entrée est pressée dans le champ code-barres"""
        self.ajouter_inventaire_item()
    
    def rechercher_produit_par_code(self, code_barre):
        """Recherche un produit par son code-barres"""
        try:
            produit = Produit.find_by_field("code_barre", code_barre)
            if produit:
                self.produit_trouve_var.set(f"{produit.nom} (Stock: {produit.stock_actuel})")
                # Mettre le focus sur la quantité
                self.dialog.after(100, lambda: self.dialog.focus_set())
            else:
                self.produit_trouve_var.set("❌ Produit non trouvé")
        except Exception as e:
            self.produit_trouve_var.set("⚠️ Erreur de recherche")

    def ajouter_inventaire_item(self):
        """Ajoute un item à l'inventaire selon le scénario"""
        try:
            code_barre = self.code_barre_var.get().strip()
            quantite_str = self.quantite_reelle_var.get().strip()

            if not code_barre or not quantite_str:
                messagebox.showwarning("Attention", "Veuillez saisir le code-barres et la quantité")
                return

            quantite_reelle = float(quantite_str)

            # Trouver le produit
            produit = Produit.find_by_field("code_barre", code_barre)
            if not produit:
                messagebox.showerror("Erreur", "Produit non trouvé")
                return

            # Calculer l'écart
            ecart = quantite_reelle - produit.stock_actuel

            # Stocker dans les données d'inventaire
            self.inventaire_data[produit.id] = {
                'theorique': produit.stock_actuel,
                'reel': quantite_reelle,
                'ecart': ecart
            }

            # Ajouter à la liste récente
            heure = datetime.now().strftime("%H:%M:%S")
            self.recent_tree.insert("", 0, values=(  # Insérer en haut
                produit.nom,
                produit.stock_actuel,
                quantite_reelle,
                f"{ecart:+.1f}",
                heure
            ))

            # Réinitialiser les champs
            self.code_barre_var.set("")
            self.quantite_reelle_var.set("")
            self.produit_trouve_var.set("")

            # Mettre à jour l'affichage
            self.load_products()

            # Son de confirmation (optionnel)
            self.dialog.bell()

        except ValueError:
            messagebox.showerror("Erreur", "Quantité invalide")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")

    def edit_quantite_reelle(self, event):
        """Permet d'éditer la quantité réelle en double-cliquant"""
        selection = self.products_tree.selection()
        if not selection:
            return

        item = selection[0]
        produit_id = int(self.products_tree.item(item)['tags'][0])
        produit = Produit.find_by_id(produit_id)

        if not produit:
            return

        # Dialogue simple pour saisir la quantité
        quantite = simpledialog.askfloat(
            "Quantité réelle",
            f"Quantité réelle pour {produit.nom}:",
            initialvalue=self.inventaire_data.get(produit_id, {}).get('reel', produit.stock_actuel)
        )

        if quantite is not None:
            ecart = quantite - produit.stock_actuel

            self.inventaire_data[produit_id] = {
                'theorique': produit.stock_actuel,
                'reel': quantite,
                'ecart': ecart
            }

            self.load_products()

    def on_filter_change(self, event):
        """Applique le filtre sélectionné"""
        self.apply_filter()

    def apply_filter(self):
        """Applique le filtre sur la liste des produits"""
        filter_value = self.filter_var.get()

        # Récupérer tous les items
        all_items = list(self.products_tree.get_children())

        # Masquer tous les items d'abord
        for item in all_items:
            self.products_tree.detach(item)

        # Réafficher selon le filtre
        for item in all_items:
            produit_id = int(self.products_tree.item(item)['tags'][0])
            values = self.products_tree.item(item)['values']

            show_item = False

            if filter_value == "tous":
                show_item = True
            elif filter_value == "non_inventorie":
                show_item = produit_id not in self.inventaire_data
            elif filter_value == "avec_ecart":
                show_item = produit_id in self.inventaire_data and self.inventaire_data[produit_id]['ecart'] != 0
            elif filter_value == "rupture":
                show_item = float(values[2]) == 0  # Stock théorique = 0

            if show_item:
                self.products_tree.reattach(item, '', 'end')

    def calculer_ecarts(self):
        """Calcule et affiche les écarts selon le scénario"""
        try:
            # Nettoyer le treeview des écarts
            for item in self.ecarts_tree.get_children():
                self.ecarts_tree.delete(item)

            # Statistiques
            inventories = 0
            positifs = 0
            negatifs = 0
            valeur_ecarts = 0.0

            for produit_id, data in self.inventaire_data.items():
                produit = Produit.find_by_id(produit_id)
                if not produit:
                    continue

                inventories += 1
                ecart = data['ecart']
                valeur_ecart = ecart * produit.prix_achat
                valeur_ecarts += abs(valeur_ecart)

                if ecart > 0:
                    positifs += 1
                    type_ecart = "📈 Surplus"
                elif ecart < 0:
                    negatifs += 1
                    type_ecart = "📉 Manquant"
                else:
                    type_ecart = "✅ Conforme"

                self.ecarts_tree.insert("", "end", values=(
                    produit.nom,
                    data['theorique'],
                    data['reel'],
                    f"{ecart:+.1f}",
                    f"{valeur_ecart:+.2f} €",
                    type_ecart
                ))

            # Mettre à jour les statistiques
            self.stat_inventories.config(text=str(inventories))
            self.stat_positifs.config(text=str(positifs))
            self.stat_negatifs.config(text=str(negatifs))
            self.stat_valeur_ecarts.config(text=f"{valeur_ecarts:.2f} €")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul: {e}")

    def finaliser_inventaire(self):
        """Finalise l'inventaire et met à jour les stocks selon le scénario"""
        if not self.inventaire_data:
            messagebox.showwarning("Attention", "Aucun produit inventorié")
            return

        if not messagebox.askyesno("Confirmation",
                                  f"Finaliser l'inventaire de {len(self.inventaire_data)} produit(s) ?\n"
                                  "Cette action mettra à jour les stocks et ne pourra pas être annulée."):
            return

        try:
            mouvements_crees = 0

            for produit_id, data in self.inventaire_data.items():
                if data['ecart'] != 0:
                    # Mettre à jour le stock du produit
                    produit = Produit.find_by_id(produit_id)
                    if produit:
                        ancien_stock = produit.stock_actuel
                        produit.stock_actuel = data['reel']
                        produit.save()

                        # Créer un mouvement de stock
                        mouvement = MouvementStock()
                        mouvement.produit_id = produit_id
                        mouvement.type_mouvement = "ajustement"
                        mouvement.quantite = abs(data['ecart'])
                        mouvement.prix_unitaire = produit.prix_achat
                        mouvement.reference = f"Inventaire {datetime.now().strftime('%Y%m%d_%H%M')}"
                        mouvement.utilisateur_id = self.current_user.id
                        mouvement.notes = f"Ajustement inventaire: {ancien_stock} → {data['reel']}"
                        mouvement.save()

                        mouvements_crees += 1

            self.result = True
            messagebox.showinfo("Succès",
                              f"✅ Inventaire finalisé avec succès!\n"
                              f"📦 {len(self.inventaire_data)} produits inventoriés\n"
                              f"🔄 {mouvements_crees} ajustements de stock effectués")

            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la finalisation: {e}")

    def cancel(self):
        """Annule le dialogue"""
        if self.inventaire_data and not messagebox.askyesno("Confirmation",
                                                           "Annuler l'inventaire ?\n"
                                                           "Toutes les données saisies seront perdues."):
            return

        self.result = False
        self.dialog.destroy()
