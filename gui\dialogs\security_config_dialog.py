"""
Dialogue de configuration de la sécurité
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from security.security_manager import security_manager
    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False
    security_manager = None

class SecurityConfigDialog:
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.result = None
        
        if not SECURITY_AVAILABLE:
            messagebox.showerror("Erreur", "Module de sécurité non disponible")
            return
        
        self.setup_dialog()
    
    def setup_dialog(self):
        """Configure le dialogue"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Configuration de la Sécurité")
        self.dialog.geometry("600x500")
        self.dialog.resizable(<PERSON><PERSON><PERSON>, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Centrer la fenêtre
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # Variables pour les paramètres
        self.vars = {}
        config = security_manager.security_config
        
        # Créer les variables
        self.vars['max_failed_attempts'] = tk.IntVar(value=config['max_failed_attempts'])
        self.vars['lockout_duration'] = tk.IntVar(value=config['lockout_duration'] // 60)  # En minutes
        self.vars['session_timeout'] = tk.IntVar(value=config['session_timeout'] // 60)  # En minutes
        self.vars['password_min_length'] = tk.IntVar(value=config['password_min_length'])
        self.vars['password_require_special'] = tk.BooleanVar(value=config['password_require_special'])
        self.vars['password_require_numbers'] = tk.BooleanVar(value=config['password_require_numbers'])
        self.vars['password_require_uppercase'] = tk.BooleanVar(value=config['password_require_uppercase'])
        self.vars['password_history_count'] = tk.IntVar(value=config['password_history_count'])
        self.vars['force_password_change_days'] = tk.IntVar(value=config['force_password_change_days'])
        self.vars['audit_retention_days'] = tk.IntVar(value=config['audit_retention_days'])
        
        self.create_widgets()
    
    def create_widgets(self):
        """Crée les widgets du dialogue"""
        # Titre
        title_frame = ttk.Frame(self.dialog)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(title_frame, text="🔐 Configuration de la Sécurité", 
                 font=('Arial', 14, 'bold')).pack()
        
        # Notebook pour organiser les paramètres
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Onglet Authentification
        auth_frame = ttk.Frame(notebook)
        notebook.add(auth_frame, text="Authentification")
        
        # Onglet Mots de passe
        password_frame = ttk.Frame(notebook)
        notebook.add(password_frame, text="Mots de passe")
        
        # Onglet Sessions
        session_frame = ttk.Frame(notebook)
        notebook.add(session_frame, text="Sessions")
        
        # Onglet Audit
        audit_frame = ttk.Frame(notebook)
        notebook.add(audit_frame, text="Audit")
        
        self.create_auth_tab(auth_frame)
        self.create_password_tab(password_frame)
        self.create_session_tab(session_frame)
        self.create_audit_tab(audit_frame)
        
        # Boutons
        buttons_frame = ttk.Frame(self.dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(buttons_frame, text="Annuler", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="Appliquer", 
                  command=self.apply_config).pack(side=tk.RIGHT)
        ttk.Button(buttons_frame, text="Réinitialiser", 
                  command=self.reset_defaults).pack(side=tk.LEFT)
    
    def create_auth_tab(self, parent):
        """Crée l'onglet authentification"""
        # Tentatives de connexion
        attempts_frame = ttk.LabelFrame(parent, text="Tentatives de connexion", padding="10")
        attempts_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(attempts_frame, text="Nombre maximum de tentatives échouées:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(attempts_frame, from_=3, to=10, width=10, 
                   textvariable=self.vars['max_failed_attempts']).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(attempts_frame, text="Durée de verrouillage (minutes):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(attempts_frame, from_=5, to=1440, width=10, 
                   textvariable=self.vars['lockout_duration']).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Informations
        info_frame = ttk.LabelFrame(parent, text="Informations", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """
• Les tentatives échouées sont comptabilisées par utilisateur
• Le verrouillage est automatique après le nombre maximum de tentatives
• Un administrateur peut déverrouiller manuellement un compte
• Les tentatives sont remises à zéro après une connexion réussie
        """
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def create_password_tab(self, parent):
        """Crée l'onglet mots de passe"""
        # Complexité
        complexity_frame = ttk.LabelFrame(parent, text="Complexité des mots de passe", padding="10")
        complexity_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(complexity_frame, text="Longueur minimale:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(complexity_frame, from_=6, to=20, width=10, 
                   textvariable=self.vars['password_min_length']).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Checkbutton(complexity_frame, text="Exiger des caractères spéciaux", 
                       variable=self.vars['password_require_special']).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        ttk.Checkbutton(complexity_frame, text="Exiger des chiffres", 
                       variable=self.vars['password_require_numbers']).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        ttk.Checkbutton(complexity_frame, text="Exiger des majuscules", 
                       variable=self.vars['password_require_uppercase']).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Historique et expiration
        history_frame = ttk.LabelFrame(parent, text="Historique et expiration", padding="10")
        history_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(history_frame, text="Nombre de mots de passe à retenir:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(history_frame, from_=0, to=10, width=10, 
                   textvariable=self.vars['password_history_count']).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(history_frame, text="Forcer le changement après (jours):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(history_frame, from_=30, to=365, width=10, 
                   textvariable=self.vars['force_password_change_days']).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
    
    def create_session_tab(self, parent):
        """Crée l'onglet sessions"""
        # Timeout de session
        timeout_frame = ttk.LabelFrame(parent, text="Timeout de session", padding="10")
        timeout_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(timeout_frame, text="Timeout d'inactivité (minutes):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(timeout_frame, from_=15, to=480, width=10, 
                   textvariable=self.vars['session_timeout']).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Informations sur les sessions
        session_info_frame = ttk.LabelFrame(parent, text="Informations", padding="10")
        session_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        session_info_text = """
• Les sessions expirent automatiquement après la période d'inactivité
• Une session est prolongée à chaque action de l'utilisateur
• Les sessions expirées sont nettoyées automatiquement
• Un utilisateur peut avoir plusieurs sessions actives
        """
        ttk.Label(session_info_frame, text=session_info_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def create_audit_tab(self, parent):
        """Crée l'onglet audit"""
        # Rétention des logs
        retention_frame = ttk.LabelFrame(parent, text="Rétention des logs", padding="10")
        retention_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(retention_frame, text="Conserver les logs pendant (jours):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(retention_frame, from_=30, to=2555, width=10, 
                   textvariable=self.vars['audit_retention_days']).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Actions d'audit
        actions_frame = ttk.LabelFrame(parent, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(actions_frame, text="Nettoyer les anciens logs maintenant", 
                  command=self.cleanup_old_logs).pack(pady=5)
        
        # Informations sur l'audit
        audit_info_frame = ttk.LabelFrame(parent, text="Informations", padding="10")
        audit_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        audit_info_text = """
• Toutes les actions importantes sont enregistrées
• Les logs incluent l'utilisateur, l'action, et l'horodatage
• Les anciens logs sont automatiquement supprimés
• Les logs peuvent être exportés pour analyse externe
        """
        ttk.Label(audit_info_frame, text=audit_info_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def apply_config(self):
        """Applique la configuration"""
        try:
            # Valider les valeurs
            if self.vars['max_failed_attempts'].get() < 3:
                messagebox.showerror("Erreur", "Le nombre minimum de tentatives doit être au moins 3")
                return
            
            if self.vars['password_min_length'].get() < 6:
                messagebox.showerror("Erreur", "La longueur minimale du mot de passe doit être au moins 6")
                return
            
            # Mettre à jour la configuration
            security_manager.security_config.update({
                'max_failed_attempts': self.vars['max_failed_attempts'].get(),
                'lockout_duration': self.vars['lockout_duration'].get() * 60,  # Convertir en secondes
                'session_timeout': self.vars['session_timeout'].get() * 60,  # Convertir en secondes
                'password_min_length': self.vars['password_min_length'].get(),
                'password_require_special': self.vars['password_require_special'].get(),
                'password_require_numbers': self.vars['password_require_numbers'].get(),
                'password_require_uppercase': self.vars['password_require_uppercase'].get(),
                'password_history_count': self.vars['password_history_count'].get(),
                'force_password_change_days': self.vars['force_password_change_days'].get(),
                'audit_retention_days': self.vars['audit_retention_days'].get()
            })
            
            # Sauvegarder la configuration
            security_manager.save_security_config()
            
            # Log de l'action
            from models.audit_log import AuditLog
            AuditLog.log_action(
                utilisateur_id=self.current_user.id,
                action="security_config_updated",
                module="security",
                details="Configuration de sécurité mise à jour"
            )
            
            self.result = True
            messagebox.showinfo("Succès", "Configuration de sécurité mise à jour")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application de la configuration: {e}")
    
    def reset_defaults(self):
        """Remet les valeurs par défaut"""
        if messagebox.askyesno("Confirmation", "Remettre les valeurs par défaut ?"):
            defaults = {
                'max_failed_attempts': 5,
                'lockout_duration': 15,  # En minutes
                'session_timeout': 60,  # En minutes
                'password_min_length': 8,
                'password_require_special': True,
                'password_require_numbers': True,
                'password_require_uppercase': True,
                'password_history_count': 5,
                'force_password_change_days': 90,
                'audit_retention_days': 365
            }
            
            for key, value in defaults.items():
                if key in self.vars:
                    self.vars[key].set(value)
    
    def cleanup_old_logs(self):
        """Nettoie les anciens logs"""
        try:
            from models.audit_log import AuditLog
            retention_days = self.vars['audit_retention_days'].get()
            deleted_count = AuditLog.nettoyer_anciens_logs(retention_days)
            
            messagebox.showinfo("Succès", f"{deleted_count} anciens logs supprimés")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.result = False
        self.dialog.destroy()
