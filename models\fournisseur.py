"""
Modèle pour les fournisseurs
"""
from models.base_model import BaseModel

class Fournisseur(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "fournisseurs"
        self.nom = ""
        self.contact = ""
        self.telephone = ""
        self.email = ""
        self.adresse = ""
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'nom': self.nom,
            'contact': self.contact,
            'telephone': self.telephone,
            'email': self.email,
            'adresse': self.adresse
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.nom = row[1]
        self.contact = row[2]
        self.telephone = row[3]
        self.email = row[4]
        self.adresse = row[5]
        self.created_at = row[6]
    
    def get_produits_count(self):
        """Retourne le nombre de produits de ce fournisseur"""
        query = "SELECT COUNT(*) FROM produits WHERE fournisseur_id = ? AND actif = 1"
        result = self.db.execute_query(query, (self.id,))
        return result[0][0] if result else 0
    
    def get_produits(self):
        """Retourne tous les produits de ce fournisseur"""
        from models.produit import Produit
        return Produit.find_all("fournisseur_id = ? AND actif = 1", (self.id,))
    
    def get_total_achats(self, date_debut=None, date_fin=None):
        """Retourne le total des achats chez ce fournisseur"""
        where_clause = "fournisseur_id = ?"
        params = [self.id]
        
        if date_debut and date_fin:
            where_clause += " AND date_livraison BETWEEN ? AND ?"
            params.extend([date_debut, date_fin])
        
        query = f"SELECT SUM(total_ttc) FROM approvisionnements WHERE {where_clause}"
        result = self.db.execute_query(query, params)
        return float(result[0][0]) if result and result[0][0] else 0.0
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom': self.nom,
            'contact': self.contact,
            'telephone': self.telephone,
            'email': self.email,
            'adresse': self.adresse,
            'produits_count': self.get_produits_count(),
            'created_at': self.created_at
        }
