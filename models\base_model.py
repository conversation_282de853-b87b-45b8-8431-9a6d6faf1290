"""
Modèle de base pour tous les modèles de données
"""
from datetime import datetime
from database.db_manager import DatabaseManager

class BaseModel:
    def __init__(self):
        self.db = DatabaseManager()
        self.table_name = ""
        self.id = None
        self.created_at = None
    
    def save(self):
        """Sauvegarde l'objet en base de données"""
        if self.id is None:
            return self._insert()
        else:
            return self._update()
    
    def delete(self):
        """Supprime l'objet de la base de données"""
        if self.id is None:
            raise ValueError("Impossible de supprimer un objet non sauvegardé")
        
        query = f"DELETE FROM {self.table_name} WHERE id = ?"
        self.db.execute_query(query, (self.id,))
        return True
    
    def _insert(self):
        """Insère un nouvel enregistrement"""
        fields = self._get_fields()
        placeholders = ", ".join(["?" for _ in fields])
        field_names = ", ".join(fields.keys())
        
        query = f"INSERT INTO {self.table_name} ({field_names}) VALUES ({placeholders})"
        self.id = self.db.execute_query(query, list(fields.values()))
        return self.id
    
    def _update(self):
        """Met à jour un enregistrement existant"""
        fields = self._get_fields()
        set_clause = ", ".join([f"{field} = ?" for field in fields.keys()])
        
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE id = ?"
        params = list(fields.values()) + [self.id]
        self.db.execute_query(query, params)
        return self.id
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder (à implémenter dans les classes filles)"""
        raise NotImplementedError("Cette méthode doit être implémentée dans les classes filles")
    
    @classmethod
    def find_by_id(cls, id):
        """Trouve un enregistrement par son ID"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE id = ?"
        result = instance.db.execute_query(query, (id,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    @classmethod
    def find_all(cls, where_clause="", params=None):
        """Trouve tous les enregistrements"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        results = instance.db.execute_query(query, params)
        objects = []
        for row in results:
            obj = cls()
            obj._load_from_row(row)
            objects.append(obj)
        return objects
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat (à implémenter dans les classes filles)"""
        raise NotImplementedError("Cette méthode doit être implémentée dans les classes filles")
    
    @classmethod
    def count(cls, where_clause="", params=None):
        """Compte le nombre d'enregistrements"""
        instance = cls()
        query = f"SELECT COUNT(*) FROM {instance.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = instance.db.execute_query(query, params)
        return result[0][0] if result else 0
