"""
Modèle pour la gestion des ventes
"""
from models.base_model import BaseModel
from datetime import datetime
import uuid

class Vente(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "ventes"
        self.numero_ticket = ""
        self.utilisateur_id = None
        self.total_ht = 0.0
        self.total_ttc = 0.0
        self.mode_paiement = ""  # cash, carte, mobile, credit
        self.client_credit_id = None
        self.date_vente = None
        self.statut = "en_cours"  # en_cours, termine, annule
        self.details = []  # Liste des détails de vente
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'numero_ticket': self.numero_ticket,
            'utilisateur_id': self.utilisateur_id,
            'total_ht': self.total_ht,
            'total_ttc': self.total_ttc,
            'mode_paiement': self.mode_paiement,
            'client_credit_id': self.client_credit_id,
            'date_vente': self.date_vente or datetime.now(),
            'statut': self.statut
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.numero_ticket = row[1]
        self.utilisateur_id = row[2]
        self.total_ht = float(row[3]) if row[3] else 0.0
        self.total_ttc = float(row[4]) if row[4] else 0.0
        self.mode_paiement = row[5]
        self.client_credit_id = row[6]
        self.date_vente = row[7]
        self.statut = row[8]
        
        # Charger les détails
        self.load_details()
    
    def generate_numero_ticket(self):
        """Génère un numéro de ticket unique"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_part = str(uuid.uuid4())[:8].upper()
        self.numero_ticket = f"TK{timestamp}{random_part}"
    
    def ajouter_produit(self, produit_id, quantite, prix_unitaire=None):
        """Ajoute un produit à la vente"""
        from models.produit import Produit
        from models.detail_vente import DetailVente
        
        produit = Produit.find_by_id(produit_id)
        if not produit:
            raise ValueError("Produit introuvable")
        
        if produit.stock_actuel < quantite:
            raise ValueError("Stock insuffisant")
        
        prix = prix_unitaire or produit.prix_vente
        sous_total = quantite * prix
        
        detail = DetailVente()
        detail.vente_id = self.id
        detail.produit_id = produit_id
        detail.quantite = quantite
        detail.prix_unitaire = prix
        detail.sous_total = sous_total
        
        self.details.append(detail)
        self.recalculer_totaux()
        
        return detail
    
    def supprimer_produit(self, produit_id):
        """Supprime un produit de la vente"""
        self.details = [d for d in self.details if d.produit_id != produit_id]
        self.recalculer_totaux()
    
    def recalculer_totaux(self):
        """Recalcule les totaux de la vente"""
        self.total_ht = sum(detail.sous_total for detail in self.details)
        # Supposons une TVA de 20%
        tva_taux = 0.20
        self.total_ttc = self.total_ht * (1 + tva_taux)
    
    def finaliser_vente(self):
        """Finalise la vente et met à jour les stocks"""
        if self.statut != "en_cours":
            raise ValueError("Cette vente ne peut pas être finalisée")
        
        if not self.numero_ticket:
            self.generate_numero_ticket()
        
        # Sauvegarder la vente
        vente_id = self.save()
        
        # Sauvegarder les détails et mettre à jour les stocks
        for detail in self.details:
            detail.vente_id = vente_id
            detail.save()
            
            # Mettre à jour le stock
            from models.produit import Produit
            produit = Produit.find_by_id(detail.produit_id)
            if produit:
                produit.ajuster_stock(
                    detail.quantite, 
                    "sortie", 
                    self.utilisateur_id,
                    f"Vente {self.numero_ticket}",
                    f"Vente de {detail.quantite} {produit.unite}"
                )
        
        self.statut = "termine"
        self.save()

        # Enregistrer dans le journal de caisse (pour les paiements en espèces)
        try:
            from models.journal_caisse import JournalCaisse
            JournalCaisse.enregistrer_vente(self, self.utilisateur_id)
        except Exception as e:
            # Ne pas faire échouer la vente si l'enregistrement en caisse échoue
            import logging
            logging.warning(f"Erreur lors de l'enregistrement en caisse: {e}")

        return vente_id
    
    def annuler_vente(self):
        """Annule la vente et remet les stocks"""
        if self.statut != "termine":
            raise ValueError("Seules les ventes terminées peuvent être annulées")
        
        # Remettre les stocks
        for detail in self.details:
            from models.produit import Produit
            produit = Produit.find_by_id(detail.produit_id)
            if produit:
                produit.ajuster_stock(
                    detail.quantite, 
                    "entree", 
                    self.utilisateur_id,
                    f"Annulation vente {self.numero_ticket}",
                    f"Remise en stock suite à annulation"
                )
        
        self.statut = "annule"
        self.save()
    
    def load_details(self):
        """Charge les détails de la vente"""
        if self.id:
            from models.detail_vente import DetailVente
            self.details = DetailVente.find_all("vente_id = ?", (self.id,))
    
    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Inconnu"
        return "Inconnu"
    
    def get_client_credit_nom(self):
        """Retourne le nom du client à crédit"""
        if self.client_credit_id:
            from models.client_credit import ClientCredit
            client = ClientCredit.find_by_id(self.client_credit_id)
            return client.nom if client else "Inconnu"
        return None
    
    @classmethod
    def find_by_numero_ticket(cls, numero_ticket):
        """Trouve une vente par son numéro de ticket"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE numero_ticket = ?"
        result = instance.db.execute_query(query, (numero_ticket,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    @classmethod
    def find_ventes_periode(cls, date_debut, date_fin):
        """Trouve les ventes d'une période"""
        return cls.find_all(
            "date_vente BETWEEN ? AND ? AND statut = 'termine'",
            (date_debut, date_fin)
        )
    
    @classmethod
    def find_ventes_utilisateur(cls, utilisateur_id, date_debut=None, date_fin=None):
        """Trouve les ventes d'un utilisateur"""
        where_clause = "utilisateur_id = ? AND statut = 'termine'"
        params = [utilisateur_id]
        
        if date_debut and date_fin:
            where_clause += " AND date_vente BETWEEN ? AND ?"
            params.extend([date_debut, date_fin])
        
        return cls.find_all(where_clause, params)
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'numero_ticket': self.numero_ticket,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'total_ht': self.total_ht,
            'total_ttc': self.total_ttc,
            'mode_paiement': self.mode_paiement,
            'client_credit_id': self.client_credit_id,
            'client_credit_nom': self.get_client_credit_nom(),
            'date_vente': self.date_vente,
            'statut': self.statut,
            'details': [detail.to_dict() for detail in self.details]
        }
