@echo off
echo ========================================
echo   Application de Gestion de Stock - Bar
echo ========================================
echo.
echo Demarrage de l'application...
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou n'est pas dans le PATH
    echo Veuillez installer Python 3.7 ou superieur
    pause
    exit /b 1
)

REM Lancer l'application
python main.py

REM Si l'application se ferme avec une erreur, afficher un message
if errorlevel 1 (
    echo.
    echo L'application s'est fermee avec une erreur.
    echo Consultez le fichier bar_stock.log pour plus de details.
    pause
)
