"""
Tableau de bord principal
"""
import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.produit import Produit
from models.vente import Vente
from models.mouvement_stock import MouvementStock
from utils.notification_manager import notification_manager

class Dashboard(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
        
        # Actualisation automatique toutes les 30 secondes
        self.after(30000, self.refresh_data)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(title_frame, text="Tableau de Bord", 
                 font=('Arial', 20, 'bold')).pack(side=tk.LEFT)
        
        # Bouton d'actualisation
        ttk.Button(title_frame, text="🔄 Actualiser", 
                  command=self.refresh_data).pack(side=tk.RIGHT)
        
        # Frame principal avec scroll
        canvas = tk.Canvas(self)
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.main_frame = scrollable_frame
        
        # Créer les sections
        self.create_stats_section()
        self.create_alerts_section()
        self.create_recent_sales_section()
        self.create_stock_section()
        self.create_quick_actions_section()
    
    def create_stats_section(self):
        """Crée la section des statistiques"""
        stats_frame = ttk.LabelFrame(self.main_frame, text="Statistiques du jour", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Grid pour les statistiques
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # Configurer les colonnes
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        # Cartes de statistiques
        self.create_stat_card(stats_grid, "Ventes du jour", "0 €", "💰", 0, 0)
        self.create_stat_card(stats_grid, "Nombre de ventes", "0", "🛒", 0, 1)
        self.create_stat_card(stats_grid, "Produits vendus", "0", "📦", 0, 2)
        self.create_stat_card(stats_grid, "Ticket moyen", "0 €", "🧾", 0, 3)
    
    def create_stat_card(self, parent, title, value, icon, row, col):
        """Crée une carte de statistique"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        # Padding interne
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Icône et titre
        header_frame = ttk.Frame(content_frame)
        header_frame.pack(fill=tk.X)
        
        ttk.Label(header_frame, text=icon, font=('Arial', 16)).pack(side=tk.LEFT)
        ttk.Label(header_frame, text=title, font=('Arial', 10)).pack(side=tk.LEFT, padx=(5, 0))
        
        # Valeur
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 14, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"stat_{title.lower().replace(' ', '_').replace('é', 'e')}", value_label)
    
    def create_alerts_section(self):
        """Crée la section des alertes"""
        alerts_frame = ttk.LabelFrame(self.main_frame, text="Alertes", padding="10")
        alerts_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Frame pour les alertes
        self.alerts_content = ttk.Frame(alerts_frame)
        self.alerts_content.pack(fill=tk.X)
    
    def create_recent_sales_section(self):
        """Crée la section des ventes récentes"""
        sales_frame = ttk.LabelFrame(self.main_frame, text="Ventes récentes", padding="10")
        sales_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Treeview pour les ventes
        columns = ("Heure", "Ticket", "Montant", "Mode paiement", "Vendeur")
        self.sales_tree = ttk.Treeview(sales_frame, columns=columns, show="headings", height=6)
        
        # Configuration des colonnes
        for col in columns:
            self.sales_tree.heading(col, text=col)
            self.sales_tree.column(col, width=100)
        
        # Scrollbar
        sales_scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)
        
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_stock_section(self):
        """Crée la section du stock"""
        stock_frame = ttk.LabelFrame(self.main_frame, text="Stock faible", padding="10")
        stock_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Treeview pour le stock
        columns = ("Produit", "Stock actuel", "Seuil minimum", "Catégorie")
        self.stock_tree = ttk.Treeview(stock_frame, columns=columns, show="headings", height=6)
        
        # Configuration des colonnes
        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=120)
        
        # Scrollbar
        stock_scrollbar = ttk.Scrollbar(stock_frame, orient=tk.VERTICAL, command=self.stock_tree.yview)
        self.stock_tree.configure(yscrollcommand=stock_scrollbar.set)
        
        self.stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stock_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_quick_actions_section(self):
        """Crée la section des actions rapides"""
        actions_frame = ttk.LabelFrame(self.main_frame, text="Actions rapides", padding="10")
        actions_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Grid pour les boutons
        buttons_grid = ttk.Frame(actions_frame)
        buttons_grid.pack(fill=tk.X)
        
        # Configurer les colonnes
        for i in range(4):
            buttons_grid.columnconfigure(i, weight=1)
        
        # Boutons d'actions
        ttk.Button(buttons_grid, text="🛒 Nouvelle vente", 
                  command=self.nouvelle_vente).grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        ttk.Button(buttons_grid, text="📦 Ajouter produit", 
                  command=self.ajouter_produit).grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        ttk.Button(buttons_grid, text="📥 Approvisionnement", 
                  command=self.approvisionnement).grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        ttk.Button(buttons_grid, text="📊 Voir rapports", 
                  command=self.voir_rapports).grid(row=0, column=3, padx=5, pady=5, sticky="ew")
    
    def load_data(self):
        """Charge les données du tableau de bord"""
        self.load_stats()
        self.load_alerts()
        self.load_recent_sales()
        self.load_low_stock()
    
    def load_stats(self):
        """Charge les statistiques du jour"""
        today = datetime.now().date()
        tomorrow = today + timedelta(days=1)
        
        try:
            # Ventes du jour
            ventes_jour = Vente.find_ventes_periode(today, tomorrow)
            
            total_ventes = sum(vente.total_ttc for vente in ventes_jour)
            nombre_ventes = len(ventes_jour)
            
            # Produits vendus
            total_produits = 0
            for vente in ventes_jour:
                total_produits += sum(detail.quantite for detail in vente.details)
            
            # Ticket moyen
            ticket_moyen = total_ventes / nombre_ventes if nombre_ventes > 0 else 0
            
            # Mise à jour des labels
            self.stat_ventes_du_jour.config(text=f"{total_ventes:.2f} €")
            self.stat_nombre_de_ventes.config(text=str(nombre_ventes))
            self.stat_produits_vendus.config(text=str(total_produits))
            self.stat_ticket_moyen.config(text=f"{ticket_moyen:.2f} €")
            
        except Exception as e:
            print(f"Erreur lors du chargement des statistiques: {e}")
    
    def load_alerts(self):
        """Charge les alertes"""
        # Nettoyer les alertes existantes
        for widget in self.alerts_content.winfo_children():
            widget.destroy()
        
        try:
            # Produits en stock faible
            produits_faibles = Produit.find_produits_stock_faible()
            
            if produits_faibles:
                alert_frame = ttk.Frame(self.alerts_content)
                alert_frame.pack(fill=tk.X, pady=2)
                
                ttk.Label(alert_frame, text="⚠️", font=('Arial', 12)).pack(side=tk.LEFT)
                ttk.Label(alert_frame, text=f"{len(produits_faibles)} produit(s) en stock faible", 
                         foreground="orange").pack(side=tk.LEFT, padx=(5, 0))
            
            # Autres alertes peuvent être ajoutées ici
            
            if not self.alerts_content.winfo_children():
                ttk.Label(self.alerts_content, text="✅ Aucune alerte", 
                         foreground="green").pack()
                
        except Exception as e:
            print(f"Erreur lors du chargement des alertes: {e}")
    
    def load_recent_sales(self):
        """Charge les ventes récentes"""
        # Nettoyer le treeview
        for item in self.sales_tree.get_children():
            self.sales_tree.delete(item)
        
        try:
            today = datetime.now().date()
            tomorrow = today + timedelta(days=1)
            
            ventes = Vente.find_ventes_periode(today, tomorrow)
            ventes = sorted(ventes, key=lambda x: x.date_vente, reverse=True)[:10]
            
            for vente in ventes:
                heure = datetime.fromisoformat(vente.date_vente).strftime("%H:%M")
                self.sales_tree.insert("", "end", values=(
                    heure,
                    vente.numero_ticket,
                    f"{vente.total_ttc:.2f} €",
                    vente.mode_paiement,
                    vente.get_utilisateur_nom()
                ))
                
        except Exception as e:
            print(f"Erreur lors du chargement des ventes récentes: {e}")
    
    def load_low_stock(self):
        """Charge les produits en stock faible"""
        # Nettoyer le treeview
        for item in self.stock_tree.get_children():
            self.stock_tree.delete(item)
        
        try:
            produits_faibles = Produit.find_produits_stock_faible()
            
            for produit in produits_faibles:
                self.stock_tree.insert("", "end", values=(
                    produit.nom,
                    produit.stock_actuel,
                    produit.seuil_minimum,
                    produit.get_categorie_nom()
                ))
                
        except Exception as e:
            print(f"Erreur lors du chargement du stock faible: {e}")
    
    def refresh_data(self):
        """Actualise toutes les données"""
        self.load_data()
        # Programmer la prochaine actualisation
        self.after(30000, self.refresh_data)
    
    # Méthodes pour les actions rapides
    def nouvelle_vente(self):
        """Ouvre le point de vente"""
        # Cette méthode sera appelée par la fenêtre principale
        pass
    
    def ajouter_produit(self):
        """Ouvre la gestion des produits"""
        pass
    
    def approvisionnement(self):
        """Ouvre la gestion des approvisionnements"""
        pass
    
    def voir_rapports(self):
        """Ouvre les rapports"""
        pass
