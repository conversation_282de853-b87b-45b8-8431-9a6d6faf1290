"""
Fenêtre de gestion du stock
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.produit import Produit
from models.mouvement_stock import MouvementStock

class StockWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion du Stock", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="📥 Entrée stock", 
                  command=self.entree_stock).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="📤 Sortie stock", 
                  command=self.sortie_stock).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="📊 Inventaire", 
                  command=self.inventaire).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Notebook pour les onglets
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet État du stock
        stock_frame = ttk.Frame(notebook)
        notebook.add(stock_frame, text="État du stock")
        
        # Onglet Mouvements
        movements_frame = ttk.Frame(notebook)
        notebook.add(movements_frame, text="Mouvements")
        
        # Onglet Alertes
        alerts_frame = ttk.Frame(notebook)
        notebook.add(alerts_frame, text="Alertes")
        
        self.setup_stock_tab(stock_frame)
        self.setup_movements_tab(movements_frame)
        self.setup_alerts_tab(alerts_frame)
    
    def setup_stock_tab(self, parent):
        """Configure l'onglet état du stock"""
        # Filtres
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filters_frame, text="Filtrer:").pack(side=tk.LEFT)
        
        self.filter_var = tk.StringVar(value="Tous")
        filter_combo = ttk.Combobox(filters_frame, textvariable=self.filter_var, 
                                   values=["Tous", "Stock faible", "Stock OK", "Rupture"], 
                                   state="readonly")
        filter_combo.pack(side=tk.LEFT, padx=(5, 0))
        filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Treeview pour le stock
        columns = ("Produit", "Catégorie", "Stock actuel", "Seuil min", "Unité", "Valeur stock", "Statut")
        self.stock_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.stock_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.stock_tree.xview)
        self.stock_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind double-clic
        self.stock_tree.bind('<Double-1>', self.ajuster_stock_produit)
    
    def setup_movements_tab(self, parent):
        """Configure l'onglet mouvements"""
        # Filtres
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filters_frame, text="Type:").pack(side=tk.LEFT)
        
        self.movement_filter_var = tk.StringVar(value="Tous")
        movement_filter_combo = ttk.Combobox(filters_frame, textvariable=self.movement_filter_var,
                                           values=["Tous", "Entrée", "Sortie", "Inventaire"],
                                           state="readonly")
        movement_filter_combo.pack(side=tk.LEFT, padx=(5, 0))
        movement_filter_combo.bind('<<ComboboxSelected>>', self.on_movement_filter_change)
        
        # Treeview pour les mouvements
        columns = ("Date", "Produit", "Type", "Quantité", "Référence", "Utilisateur")
        self.movements_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        for col in columns:
            self.movements_tree.heading(col, text=col)
            self.movements_tree.column(col, width=120)
        
        # Scrollbars
        movements_v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.movements_tree.yview)
        movements_h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.movements_tree.xview)
        self.movements_tree.configure(yscrollcommand=movements_v_scrollbar.set, 
                                     xscrollcommand=movements_h_scrollbar.set)
        
        # Pack
        self.movements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        movements_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        movements_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_alerts_tab(self, parent):
        """Configure l'onglet alertes"""
        # Titre
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="Alertes de Stock", 
                 font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        
        # Statistiques
        stats_frame = ttk.LabelFrame(parent, text="Statistiques", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_labels = {}
        
        # Créer les labels de statistiques
        stats_info = [
            ("Produits en stock faible", "stock_faible"),
            ("Produits en rupture", "rupture"),
            ("Valeur totale du stock", "valeur_totale")
        ]
        
        for i, (label, key) in enumerate(stats_info):
            ttk.Label(stats_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.stats_labels[key] = ttk.Label(stats_frame, text="-", font=('Arial', 10, 'bold'))
            self.stats_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Liste des produits en alerte
        alerts_list_frame = ttk.LabelFrame(parent, text="Produits nécessitant une attention", padding="5")
        alerts_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview pour les alertes
        alert_columns = ("Produit", "Stock actuel", "Seuil minimum", "Statut", "Action recommandée")
        self.alerts_tree = ttk.Treeview(alerts_list_frame, columns=alert_columns, show="headings")
        
        # Configuration des colonnes
        for col in alert_columns:
            self.alerts_tree.heading(col, text=col)
            self.alerts_tree.column(col, width=120)
        
        # Scrollbar
        alerts_scrollbar = ttk.Scrollbar(alerts_list_frame, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)
        
        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_data(self):
        """Charge toutes les données"""
        self.load_stock_data()
        self.load_movements_data()
        self.load_alerts_data()
    
    def load_stock_data(self):
        """Charge les données de stock"""
        # Nettoyer le treeview
        for item in self.stock_tree.get_children():
            self.stock_tree.delete(item)
        
        try:
            produits = Produit.find_all("actif = 1")
            
            # Appliquer le filtre
            filter_value = self.filter_var.get()
            if filter_value == "Stock faible":
                produits = [p for p in produits if p.is_stock_faible() and p.stock_actuel > 0]
            elif filter_value == "Stock OK":
                produits = [p for p in produits if not p.is_stock_faible()]
            elif filter_value == "Rupture":
                produits = [p for p in produits if p.stock_actuel == 0]
            
            for produit in produits:
                valeur_stock = produit.stock_actuel * produit.prix_achat
                
                if produit.stock_actuel == 0:
                    statut = "🔴 Rupture"
                elif produit.is_stock_faible():
                    statut = "🟡 Stock faible"
                else:
                    statut = "✅ OK"
                
                self.stock_tree.insert("", "end", values=(
                    produit.nom,
                    produit.get_categorie_nom(),
                    produit.stock_actuel,
                    produit.seuil_minimum,
                    produit.unite,
                    f"{valeur_stock:.2f} €",
                    statut
                ), tags=(str(produit.id),))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement du stock: {e}")
    
    def load_movements_data(self):
        """Charge les données des mouvements"""
        # Nettoyer le treeview
        for item in self.movements_tree.get_children():
            self.movements_tree.delete(item)
        
        try:
            # Charger les mouvements récents (100 derniers)
            mouvements = MouvementStock.find_all("1=1 ORDER BY date_mouvement DESC LIMIT 100")
            
            # Appliquer le filtre
            filter_value = self.movement_filter_var.get()
            if filter_value != "Tous":
                type_mapping = {"Entrée": "entree", "Sortie": "sortie", "Inventaire": "inventaire"}
                type_filter = type_mapping.get(filter_value)
                if type_filter:
                    mouvements = [m for m in mouvements if m.type_mouvement == type_filter]
            
            for mouvement in mouvements:
                type_icon = {"entree": "📥", "sortie": "📤", "inventaire": "📊"}.get(mouvement.type_mouvement, "")
                
                self.movements_tree.insert("", "end", values=(
                    mouvement.date_mouvement,
                    mouvement.get_produit_nom(),
                    f"{type_icon} {mouvement.type_mouvement.title()}",
                    mouvement.quantite,
                    mouvement.reference or "-",
                    mouvement.get_utilisateur_nom()
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des mouvements: {e}")
    
    def load_alerts_data(self):
        """Charge les données d'alertes"""
        try:
            # Statistiques
            tous_produits = Produit.find_all("actif = 1")
            produits_faibles = [p for p in tous_produits if p.is_stock_faible() and p.stock_actuel > 0]
            produits_rupture = [p for p in tous_produits if p.stock_actuel == 0]
            valeur_totale = sum(p.stock_actuel * p.prix_achat for p in tous_produits)
            
            self.stats_labels['stock_faible'].config(text=str(len(produits_faibles)))
            self.stats_labels['rupture'].config(text=str(len(produits_rupture)))
            self.stats_labels['valeur_totale'].config(text=f"{valeur_totale:.2f} €")
            
            # Liste des alertes
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)
            
            # Produits en rupture
            for produit in produits_rupture:
                self.alerts_tree.insert("", "end", values=(
                    produit.nom,
                    produit.stock_actuel,
                    produit.seuil_minimum,
                    "🔴 Rupture",
                    "Approvisionnement urgent"
                ))
            
            # Produits en stock faible
            for produit in produits_faibles:
                self.alerts_tree.insert("", "end", values=(
                    produit.nom,
                    produit.stock_actuel,
                    produit.seuil_minimum,
                    "🟡 Stock faible",
                    "Prévoir approvisionnement"
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des alertes: {e}")
    
    def on_filter_change(self, event):
        """Appelé quand le filtre change"""
        self.load_stock_data()
    
    def on_movement_filter_change(self, event):
        """Appelé quand le filtre des mouvements change"""
        self.load_movements_data()
    
    def entree_stock(self):
        """Ouvre la fenêtre d'entrée de stock"""
        try:
            from gui.dialogs.mouvement_stock_dialog import MouvementStockDialog
            dialog = MouvementStockDialog(self, "Entrée de stock", "entree", self.current_user)
            if dialog.result:
                self.load_data()
                messagebox.showinfo("Succès", "Entrée de stock enregistrée avec succès")
        except ImportError:
            # Fallback si le dialogue n'existe pas encore
            self.entree_stock_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'entrée de stock: {e}")

    def sortie_stock(self):
        """Ouvre la fenêtre de sortie de stock"""
        try:
            from gui.dialogs.mouvement_stock_dialog import MouvementStockDialog
            dialog = MouvementStockDialog(self, "Sortie de stock", "sortie", self.current_user)
            if dialog.result:
                self.load_data()
                messagebox.showinfo("Succès", "Sortie de stock enregistrée avec succès")
        except ImportError:
            # Fallback si le dialogue n'existe pas encore
            self.sortie_stock_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sortie de stock: {e}")

    def inventaire(self):
        """Ouvre la fenêtre d'inventaire"""
        try:
            from gui.dialogs.inventaire_dialog import InventaireDialog
            dialog = InventaireDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
                messagebox.showinfo("Succès", "Inventaire effectué avec succès")
        except ImportError:
            # Fallback si le dialogue n'existe pas encore
            self.inventaire_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'inventaire: {e}")

    def ajuster_stock_produit(self, event=None):
        """Ajuste le stock du produit sélectionné"""
        selection = self.stock_tree.selection()
        if not selection:
            return

        try:
            item = self.stock_tree.item(selection[0])
            produit_id = int(item['tags'][0])

            produit = Produit.find_by_id(produit_id)
            if not produit:
                messagebox.showerror("Erreur", "Produit introuvable")
                return

            from gui.dialogs.ajustement_stock_dialog import AjustementStockDialog
            dialog = AjustementStockDialog(self, produit, self.current_user)
            if dialog.result:
                self.load_data()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajustement: {e}")

    def entree_stock_simple(self):
        """Dialogue simple pour l'entrée de stock"""
        dialog = tk.Toplevel(self)
        dialog.title("Entrée de stock")
        dialog.geometry("500x400")
        dialog.resizable(False, False)

        # Centrer la fenêtre
        dialog.transient(self)
        dialog.grab_set()

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Sélection du produit
        ttk.Label(main_frame, text="Produit:").pack(anchor=tk.W, pady=(0, 5))
        produit_var = tk.StringVar()
        produit_combo = ttk.Combobox(main_frame, textvariable=produit_var, state="readonly")
        produit_combo.pack(fill=tk.X, pady=(0, 15))

        # Charger les produits
        try:
            produits = Produit.find_all("actif = 1")
            produit_values = [f"{p.nom} (Stock: {p.stock_actuel})" for p in produits]
            produit_combo['values'] = produit_values
        except:
            pass

        # Quantité
        ttk.Label(main_frame, text="Quantité:").pack(anchor=tk.W, pady=(0, 5))
        quantite_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=quantite_var).pack(fill=tk.X, pady=(0, 15))

        # Référence
        ttk.Label(main_frame, text="Référence:").pack(anchor=tk.W, pady=(0, 5))
        reference_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=reference_var).pack(fill=tk.X, pady=(0, 15))

        # Commentaire
        ttk.Label(main_frame, text="Commentaire:").pack(anchor=tk.W, pady=(0, 5))
        commentaire_text = tk.Text(main_frame, height=4)
        commentaire_text.pack(fill=tk.X, pady=(0, 20))

        def valider():
            try:
                if not produit_var.get():
                    messagebox.showerror("Erreur", "Veuillez sélectionner un produit")
                    return

                quantite = int(quantite_var.get())
                if quantite <= 0:
                    messagebox.showerror("Erreur", "La quantité doit être positive")
                    return

                # Trouver le produit sélectionné
                produit_index = produit_combo.current()
                if produit_index >= 0:
                    produit = produits[produit_index]

                    # Ajuster le stock
                    produit.ajuster_stock(
                        quantite=quantite,
                        type_mouvement="entree",
                        utilisateur_id=self.current_user.id,
                        reference=reference_var.get().strip() or "Entrée manuelle",
                        commentaire=commentaire_text.get(1.0, tk.END).strip()
                    )

                    dialog.destroy()
                    self.load_data()
                    messagebox.showinfo("Succès", f"Entrée de {quantite} unités enregistrée")

            except ValueError:
                messagebox.showerror("Erreur", "Quantité invalide")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur: {e}")

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Valider", command=valider).pack(side=tk.RIGHT)

    def sortie_stock_simple(self):
        """Dialogue simple pour la sortie de stock"""
        dialog = tk.Toplevel(self)
        dialog.title("Sortie de stock")
        dialog.geometry("500x400")
        dialog.resizable(False, False)

        # Centrer la fenêtre
        dialog.transient(self)
        dialog.grab_set()

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Sélection du produit
        ttk.Label(main_frame, text="Produit:").pack(anchor=tk.W, pady=(0, 5))
        produit_var = tk.StringVar()
        produit_combo = ttk.Combobox(main_frame, textvariable=produit_var, state="readonly")
        produit_combo.pack(fill=tk.X, pady=(0, 15))

        # Charger les produits avec stock
        try:
            produits = Produit.find_all("actif = 1 AND stock_actuel > 0")
            produit_values = [f"{p.nom} (Stock: {p.stock_actuel})" for p in produits]
            produit_combo['values'] = produit_values
        except:
            pass

        # Quantité
        ttk.Label(main_frame, text="Quantité:").pack(anchor=tk.W, pady=(0, 5))
        quantite_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=quantite_var).pack(fill=tk.X, pady=(0, 15))

        # Motif
        ttk.Label(main_frame, text="Motif:").pack(anchor=tk.W, pady=(0, 5))
        motif_var = tk.StringVar()
        motif_combo = ttk.Combobox(main_frame, textvariable=motif_var,
                                  values=["Perte", "Casse", "Péremption", "Don", "Vol", "Autre"])
        motif_combo.pack(fill=tk.X, pady=(0, 15))

        # Commentaire
        ttk.Label(main_frame, text="Commentaire:").pack(anchor=tk.W, pady=(0, 5))
        commentaire_text = tk.Text(main_frame, height=4)
        commentaire_text.pack(fill=tk.X, pady=(0, 20))

        def valider():
            try:
                if not produit_var.get():
                    messagebox.showerror("Erreur", "Veuillez sélectionner un produit")
                    return

                quantite = int(quantite_var.get())
                if quantite <= 0:
                    messagebox.showerror("Erreur", "La quantité doit être positive")
                    return

                # Trouver le produit sélectionné
                produit_index = produit_combo.current()
                if produit_index >= 0:
                    produit = produits[produit_index]

                    if quantite > produit.stock_actuel:
                        messagebox.showerror("Erreur", "Quantité supérieure au stock disponible")
                        return

                    # Ajuster le stock
                    produit.ajuster_stock(
                        quantite=quantite,
                        type_mouvement="sortie",
                        utilisateur_id=self.current_user.id,
                        reference=f"Sortie - {motif_var.get() or 'Non spécifié'}",
                        commentaire=commentaire_text.get(1.0, tk.END).strip()
                    )

                    dialog.destroy()
                    self.load_data()
                    messagebox.showinfo("Succès", f"Sortie de {quantite} unités enregistrée")

            except ValueError:
                messagebox.showerror("Erreur", "Quantité invalide")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur: {e}")

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Valider", command=valider).pack(side=tk.RIGHT)

    def inventaire_simple(self):
        """Dialogue simple pour l'inventaire"""
        dialog = tk.Toplevel(self)
        dialog.title("Inventaire")
        dialog.geometry("600x500")
        dialog.resizable(True, True)

        # Centrer la fenêtre
        dialog.transient(self)
        dialog.grab_set()

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Inventaire des produits",
                 font=('Arial', 14, 'bold')).pack(pady=(0, 20))

        # Treeview pour l'inventaire
        columns = ("Produit", "Stock système", "Stock réel", "Écart")
        tree = ttk.Treeview(main_frame, columns=columns, show="headings")

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Charger les produits
        try:
            produits = Produit.find_all("actif = 1")
            for produit in produits:
                tree.insert("", "end", values=(
                    produit.nom,
                    produit.stock_actuel,
                    "",  # À saisir
                    ""   # Calculé automatiquement
                ), tags=(str(produit.id),))
        except:
            pass

        def on_double_click(event):
            """Permet de modifier le stock réel"""
            item = tree.selection()[0]
            column = tree.identify_column(event.x)

            if column == "#3":  # Colonne "Stock réel"
                # Créer un Entry pour la saisie
                bbox = tree.bbox(item, column)
                if bbox:
                    entry = tk.Entry(tree)
                    entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])

                    current_value = tree.item(item)['values'][2]
                    entry.insert(0, str(current_value) if current_value else "")
                    entry.focus()

                    def save_value(event=None):
                        try:
                            new_value = int(entry.get())
                            values = list(tree.item(item)['values'])
                            values[2] = new_value

                            # Calculer l'écart
                            stock_systeme = int(values[1])
                            ecart = new_value - stock_systeme
                            values[3] = f"{ecart:+d}" if ecart != 0 else "0"

                            tree.item(item, values=values)
                            entry.destroy()
                        except ValueError:
                            entry.destroy()

                    entry.bind('<Return>', save_value)
                    entry.bind('<FocusOut>', save_value)

        tree.bind('<Double-1>', on_double_click)

        def finaliser_inventaire():
            """Finalise l'inventaire et met à jour les stocks"""
            try:
                modifications = []

                for item in tree.get_children():
                    values = tree.item(item)['values']
                    produit_id = int(tree.item(item)['tags'][0])

                    if values[2]:  # Si stock réel saisi
                        stock_reel = int(values[2])
                        stock_systeme = int(values[1])

                        if stock_reel != stock_systeme:
                            modifications.append({
                                'produit_id': produit_id,
                                'stock_systeme': stock_systeme,
                                'stock_reel': stock_reel,
                                'ecart': stock_reel - stock_systeme
                            })

                if not modifications:
                    messagebox.showinfo("Info", "Aucune modification à apporter")
                    return

                # Confirmer les modifications
                message = f"Appliquer {len(modifications)} modification(s) de stock ?"
                if messagebox.askyesno("Confirmation", message):

                    for modif in modifications:
                        produit = Produit.find_by_id(modif['produit_id'])
                        if produit:
                            # Utiliser l'inventaire pour ajuster
                            produit.ajuster_stock(
                                quantite=modif['stock_reel'],
                                type_mouvement="inventaire",
                                utilisateur_id=self.current_user.id,
                                reference=f"Inventaire {datetime.now().strftime('%Y%m%d')}",
                                commentaire=f"Écart: {modif['ecart']:+d}"
                            )

                    dialog.destroy()
                    self.load_data()
                    messagebox.showinfo("Succès", f"Inventaire terminé - {len(modifications)} produit(s) mis à jour")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'inventaire: {e}")

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Label(buttons_frame, text="Double-cliquez sur la colonne 'Stock réel' pour saisir").pack(side=tk.LEFT)

        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Finaliser l'inventaire", command=finaliser_inventaire).pack(side=tk.RIGHT)
