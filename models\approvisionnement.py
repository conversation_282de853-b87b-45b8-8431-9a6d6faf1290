"""
Modèle pour les approvisionnements
"""
from models.base_model import BaseModel
from datetime import datetime

class Approvisionnement(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "approvisionnements"
        self.fournisseur_id = None
        self.numero_facture = ""
        self.date_livraison = None
        self.total_ht = 0.0
        self.total_ttc = 0.0
        self.utilisateur_id = None
        self.statut = "recu"  # commande, recu, facture
        self.details = []  # Liste des détails d'approvisionnement
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'fournisseur_id': self.fournisseur_id,
            'numero_facture': self.numero_facture,
            'date_livraison': self.date_livraison or datetime.now().date(),
            'total_ht': self.total_ht,
            'total_ttc': self.total_ttc,
            'utilisateur_id': self.utilisateur_id,
            'statut': self.statut
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.fournisseur_id = row[1]
        self.numero_facture = row[2]
        self.date_livraison = row[3]
        self.total_ht = float(row[4]) if row[4] else 0.0
        self.total_ttc = float(row[5]) if row[5] else 0.0
        self.utilisateur_id = row[6]
        self.statut = row[7]
        self.created_at = row[8]
        
        # Charger les détails
        self.load_details()
    
    def generate_numero_facture(self):
        """Génère un numéro de facture unique"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        self.numero_facture = f"APP{timestamp}"
    
    def ajouter_produit(self, produit_id, quantite, prix_unitaire):
        """Ajoute un produit à l'approvisionnement"""
        from models.detail_approvisionnement import DetailApprovisionnement
        from models.produit import Produit
        
        produit = Produit.find_by_id(produit_id)
        if not produit:
            raise ValueError("Produit introuvable")
        
        sous_total = quantite * prix_unitaire
        
        detail = DetailApprovisionnement()
        detail.approvisionnement_id = self.id
        detail.produit_id = produit_id
        detail.quantite = quantite
        detail.prix_unitaire = prix_unitaire
        detail.sous_total = sous_total
        
        self.details.append(detail)
        self.recalculer_totaux()
        
        return detail
    
    def supprimer_produit(self, produit_id):
        """Supprime un produit de l'approvisionnement"""
        self.details = [d for d in self.details if d.produit_id != produit_id]
        self.recalculer_totaux()
    
    def recalculer_totaux(self):
        """Recalcule les totaux de l'approvisionnement"""
        self.total_ht = sum(detail.sous_total for detail in self.details)
        # Supposons une TVA de 20%
        tva_taux = 0.20
        self.total_ttc = self.total_ht * (1 + tva_taux)
    
    def finaliser_approvisionnement(self):
        """Finalise l'approvisionnement et met à jour les stocks"""
        if self.statut == "facture":
            raise ValueError("Cet approvisionnement est déjà finalisé")
        
        if not self.numero_facture:
            self.generate_numero_facture()
        
        # Sauvegarder l'approvisionnement
        appro_id = self.save()
        
        # Sauvegarder les détails et mettre à jour les stocks
        for detail in self.details:
            detail.approvisionnement_id = appro_id
            detail.save()
            
            # Mettre à jour le stock
            from models.produit import Produit
            produit = Produit.find_by_id(detail.produit_id)
            if produit:
                produit.ajuster_stock(
                    detail.quantite, 
                    "entree", 
                    self.utilisateur_id,
                    f"Approvisionnement {self.numero_facture}",
                    f"Réception de {detail.quantite} {produit.unite}"
                )
        
        self.statut = "facture"
        self.save()
        
        return appro_id
    
    def annuler_approvisionnement(self):
        """Annule l'approvisionnement et remet les stocks"""
        if self.statut != "facture":
            raise ValueError("Seuls les approvisionnements finalisés peuvent être annulés")
        
        # Remettre les stocks
        for detail in self.details:
            from models.produit import Produit
            produit = Produit.find_by_id(detail.produit_id)
            if produit:
                produit.ajuster_stock(
                    detail.quantite, 
                    "sortie", 
                    self.utilisateur_id,
                    f"Annulation approvisionnement {self.numero_facture}",
                    f"Annulation réception"
                )
        
        self.statut = "annule"
        self.save()
    
    def load_details(self):
        """Charge les détails de l'approvisionnement"""
        if self.id:
            from models.detail_approvisionnement import DetailApprovisionnement
            self.details = DetailApprovisionnement.find_all("approvisionnement_id = ?", (self.id,))
    
    def get_fournisseur_nom(self):
        """Retourne le nom du fournisseur"""
        if self.fournisseur_id:
            from models.fournisseur import Fournisseur
            fournisseur = Fournisseur.find_by_id(self.fournisseur_id)
            return fournisseur.nom if fournisseur else "Inconnu"
        return "Inconnu"
    
    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Inconnu"
        return "Inconnu"
    
    @classmethod
    def find_by_numero_facture(cls, numero_facture):
        """Trouve un approvisionnement par son numéro de facture"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE numero_facture = ?"
        result = instance.db.execute_query(query, (numero_facture,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    @classmethod
    def find_approvisionnements_periode(cls, date_debut, date_fin):
        """Trouve les approvisionnements d'une période"""
        return cls.find_all(
            "date_livraison BETWEEN ? AND ?",
            (date_debut, date_fin)
        )
    
    @classmethod
    def find_approvisionnements_fournisseur(cls, fournisseur_id, date_debut=None, date_fin=None):
        """Trouve les approvisionnements d'un fournisseur"""
        where_clause = "fournisseur_id = ?"
        params = [fournisseur_id]
        
        if date_debut and date_fin:
            where_clause += " AND date_livraison BETWEEN ? AND ?"
            params.extend([date_debut, date_fin])
        
        return cls.find_all(where_clause, params)
    
    @classmethod
    def find_approvisionnements_en_attente(cls):
        """Trouve les approvisionnements en attente"""
        return cls.find_all("statut IN ('commande', 'recu')")
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'fournisseur_id': self.fournisseur_id,
            'fournisseur_nom': self.get_fournisseur_nom(),
            'numero_facture': self.numero_facture,
            'date_livraison': self.date_livraison,
            'total_ht': self.total_ht,
            'total_ttc': self.total_ttc,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'statut': self.statut,
            'created_at': self.created_at,
            'details': [detail.to_dict() for detail in self.details]
        }
