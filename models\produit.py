"""
Modèle pour la gestion des produits
"""
from models.base_model import BaseModel
from datetime import datetime

class Produit(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "produits"
        self.nom = ""
        self.code_barre = ""
        self.categorie_id = None
        self.unite = ""
        self.prix_achat = 0.0
        self.prix_vente = 0.0
        self.stock_actuel = 0
        self.seuil_minimum = 5
        self.fournisseur_id = None
        self.actif = True
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'nom': self.nom,
            'code_barre': self.code_barre,
            'categorie_id': self.categorie_id,
            'unite': self.unite,
            'prix_achat': self.prix_achat,
            'prix_vente': self.prix_vente,
            'stock_actuel': self.stock_actuel,
            'seuil_minimum': self.seuil_minimum,
            'fournisseur_id': self.fournisseur_id,
            'actif': self.actif
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.nom = row[1]
        self.code_barre = row[2]
        self.categorie_id = row[3]
        self.unite = row[4]
        self.prix_achat = float(row[5]) if row[5] else 0.0
        self.prix_vente = float(row[6]) if row[6] else 0.0
        self.stock_actuel = row[7] if row[7] else 0
        self.seuil_minimum = row[8] if row[8] else 5
        self.fournisseur_id = row[9]
        self.actif = bool(row[10])
        self.created_at = row[11]
    
    def get_categorie_nom(self):
        """Retourne le nom de la catégorie"""
        if self.categorie_id:
            query = "SELECT nom FROM categories WHERE id = ?"
            result = self.db.execute_query(query, (self.categorie_id,))
            return result[0][0] if result else "Sans catégorie"
        return "Sans catégorie"
    
    def get_fournisseur_nom(self):
        """Retourne le nom du fournisseur"""
        if self.fournisseur_id:
            query = "SELECT nom FROM fournisseurs WHERE id = ?"
            result = self.db.execute_query(query, (self.fournisseur_id,))
            return result[0][0] if result else "Sans fournisseur"
        return "Sans fournisseur"
    
    def is_stock_faible(self):
        """Vérifie si le stock est en dessous du seuil minimum"""
        return self.stock_actuel <= self.seuil_minimum
    
    def calculer_marge(self):
        """Calcule la marge bénéficiaire"""
        if self.prix_achat > 0:
            return ((self.prix_vente - self.prix_achat) / self.prix_achat) * 100
        return 0
    
    def ajuster_stock(self, quantite, type_mouvement, utilisateur_id=None, reference="", commentaire=""):
        """Ajuste le stock et enregistre le mouvement"""
        if type_mouvement == "entree":
            self.stock_actuel += quantite
        elif type_mouvement == "sortie":
            if self.stock_actuel >= quantite:
                self.stock_actuel -= quantite
            else:
                raise ValueError("Stock insuffisant")
        elif type_mouvement == "inventaire":
            self.stock_actuel = quantite
        
        # Sauvegarder le produit
        self.save()
        
        # Enregistrer le mouvement
        from models.mouvement_stock import MouvementStock
        mouvement = MouvementStock()
        mouvement.produit_id = self.id
        mouvement.type_mouvement = type_mouvement
        mouvement.quantite = quantite
        mouvement.prix_unitaire = self.prix_achat if type_mouvement == "entree" else self.prix_vente
        mouvement.reference = reference
        mouvement.utilisateur_id = utilisateur_id
        mouvement.commentaire = commentaire
        mouvement.save()
        
        return True
    
    @classmethod
    def find_by_code_barre(cls, code_barre):
        """Trouve un produit par son code-barres"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE code_barre = ? AND actif = 1"
        result = instance.db.execute_query(query, (code_barre,))
        
        if result:
            instance._load_from_row(result[0])
            return instance
        return None
    
    @classmethod
    def find_produits_stock_faible(cls):
        """Trouve tous les produits avec un stock faible"""
        instance = cls()
        query = f"SELECT * FROM {instance.table_name} WHERE stock_actuel <= seuil_minimum AND actif = 1"
        results = instance.db.execute_query(query)
        
        produits = []
        for row in results:
            produit = cls()
            produit._load_from_row(row)
            produits.append(produit)
        return produits
    
    @classmethod
    def find_by_categorie(cls, categorie_id):
        """Trouve tous les produits d'une catégorie"""
        return cls.find_all("categorie_id = ? AND actif = 1", (categorie_id,))
    
    @classmethod
    def search(cls, terme_recherche):
        """Recherche des produits par nom ou code-barres"""
        instance = cls()
        query = f"""
            SELECT * FROM {instance.table_name} 
            WHERE (nom LIKE ? OR code_barre LIKE ?) AND actif = 1
            ORDER BY nom
        """
        terme = f"%{terme_recherche}%"
        results = instance.db.execute_query(query, (terme, terme))
        
        produits = []
        for row in results:
            produit = cls()
            produit._load_from_row(row)
            produits.append(produit)
        return produits
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom': self.nom,
            'code_barre': self.code_barre,
            'categorie_id': self.categorie_id,
            'categorie_nom': self.get_categorie_nom(),
            'unite': self.unite,
            'prix_achat': self.prix_achat,
            'prix_vente': self.prix_vente,
            'stock_actuel': self.stock_actuel,
            'seuil_minimum': self.seuil_minimum,
            'fournisseur_id': self.fournisseur_id,
            'fournisseur_nom': self.get_fournisseur_nom(),
            'actif': self.actif,
            'stock_faible': self.is_stock_faible(),
            'marge': self.calculer_marge(),
            'created_at': self.created_at
        }
