"""
Modèle pour les mouvements de stock
"""
from models.base_model import BaseModel
from datetime import datetime

class MouvementStock(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "mouvements_stock"
        self.produit_id = None
        self.type_mouvement = ""  # entree, sortie, inventaire
        self.quantite = 0
        self.prix_unitaire = 0.0
        self.reference = ""
        self.utilisateur_id = None
        self.date_mouvement = None
        self.commentaire = ""
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'produit_id': self.produit_id,
            'type_mouvement': self.type_mouvement,
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'reference': self.reference,
            'utilisateur_id': self.utilisateur_id,
            'date_mouvement': self.date_mouvement or datetime.now(),
            'commentaire': self.commentaire
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.produit_id = row[1]
        self.type_mouvement = row[2]
        self.quantite = row[3]
        self.prix_unitaire = float(row[4]) if row[4] else 0.0
        self.reference = row[5]
        self.utilisateur_id = row[6]
        self.date_mouvement = row[7]
        self.commentaire = row[8]
    
    def get_produit_nom(self):
        """Retourne le nom du produit"""
        if self.produit_id:
            from models.produit import Produit
            produit = Produit.find_by_id(self.produit_id)
            return produit.nom if produit else "Produit inconnu"
        return "Produit inconnu"

    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Utilisateur inconnu"
        return "Système"

    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'produit_id': self.produit_id,
            'produit_nom': self.get_produit_nom(),
            'type_mouvement': self.type_mouvement,
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'reference': self.reference,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'date_mouvement': self.date_mouvement,
            'commentaire': self.commentaire
        }
    
    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Inconnu"
        return "Système"
    
    @classmethod
    def find_by_produit(cls, produit_id, limit=None):
        """Trouve les mouvements d'un produit"""
        where_clause = "produit_id = ? ORDER BY date_mouvement DESC"
        if limit:
            where_clause += f" LIMIT {limit}"
        return cls.find_all(where_clause, (produit_id,))
    
    @classmethod
    def find_by_periode(cls, date_debut, date_fin, type_mouvement=None):
        """Trouve les mouvements d'une période"""
        where_clause = "date_mouvement BETWEEN ? AND ?"
        params = [date_debut, date_fin]
        
        if type_mouvement:
            where_clause += " AND type_mouvement = ?"
            params.append(type_mouvement)
        
        where_clause += " ORDER BY date_mouvement DESC"
        return cls.find_all(where_clause, params)
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'produit_id': self.produit_id,
            'produit_nom': self.get_produit_nom(),
            'type_mouvement': self.type_mouvement,
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'reference': self.reference,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'date_mouvement': self.date_mouvement,
            'commentaire': self.commentaire
        }
