"""
Configuration de l'application de gestion de stock du bar
"""

# Configuration de la base de données
DATABASE_CONFIG = {
    'db_path': 'bar_stock.db',
    'backup_path': 'backups/',
    'auto_backup': True,
    'backup_interval_hours': 24
}

# Configuration de l'interface
UI_CONFIG = {
    'window_title': 'Gestion de Stock - Bar',
    'window_size': '1200x800',
    'theme': 'clam',
    'colors': {
        'primary': '#2c3e50',
        'secondary': '#3498db',
        'success': '#27ae60',
        'warning': '#f39c12',
        'danger': '#e74c3c',
        'light': '#ecf0f1',
        'dark': '#34495e'
    }
}

# Configuration des logs
LOGGING_CONFIG = {
    'log_file': 'bar_stock.log',
    'log_level': 'INFO',
    'max_log_size_mb': 10,
    'backup_count': 5
}

# Configuration de sécurité
SECURITY_CONFIG = {
    'password_min_length': 6,
    'session_timeout_minutes': 480,  # 8 heures
    'max_login_attempts': 5,
    'lockout_duration_minutes': 30
}

# Configuration des alertes
ALERTS_CONFIG = {
    'stock_alert_enabled': True,
    'stock_alert_threshold_days': 7,  # Alerter X jours avant rupture
    'email_alerts': False,
    'sound_alerts': True
}

# Configuration des rapports
REPORTS_CONFIG = {
    'default_period': 'month',
    'export_formats': ['excel', 'pdf', 'csv'],
    'auto_save_reports': True,
    'reports_path': 'reports/'
}

# Configuration du point de vente
POS_CONFIG = {
    'auto_print_receipt': False,
    'receipt_printer': None,
    'barcode_scanner': False,
    'cash_drawer': False,
    'customer_display': False
}

# Configuration des sauvegardes
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_schedule': 'daily',  # daily, weekly, monthly
    'backup_time': '02:00',  # HH:MM
    'max_backups': 30,
    'backup_location': 'backups/'
}

# Messages de l'application
MESSAGES = {
    'welcome': 'Bienvenue dans l\'application de gestion de stock',
    'login_required': 'Veuillez vous connecter pour continuer',
    'access_denied': 'Accès refusé - Permissions insuffisantes',
    'data_saved': 'Données sauvegardées avec succès',
    'data_error': 'Erreur lors de la sauvegarde des données',
    'stock_low': 'Attention: Stock faible pour certains produits',
    'backup_success': 'Sauvegarde effectuée avec succès',
    'backup_error': 'Erreur lors de la sauvegarde'
}

# Configuration des unités
UNITS = [
    'bouteille',
    'canette', 
    'verre',
    'dose',
    'sachet',
    'portion',
    'litre',
    'kg',
    'unité'
]

# Configuration des modes de paiement
PAYMENT_MODES = [
    ('cash', 'Espèces'),
    ('carte', 'Carte bancaire'),
    ('mobile', 'Paiement mobile'),
    ('credit', 'Crédit client'),
    ('cheque', 'Chèque'),
    ('ticket_restaurant', 'Ticket restaurant')
]

# Configuration des rôles utilisateur
USER_ROLES = {
    'gerant': {
        'name': 'Gérant',
        'permissions': ['all'],
        'description': 'Accès complet à toutes les fonctionnalités'
    },
    'serveur': {
        'name': 'Serveur',
        'permissions': ['vente', 'stock_view', 'client_credit'],
        'description': 'Ventes, consultation stock, gestion crédits'
    },
    'caissier': {
        'name': 'Caissier', 
        'permissions': ['vente', 'stock_view'],
        'description': 'Ventes et consultation du stock uniquement'
    }
}

# Configuration des catégories par défaut
DEFAULT_CATEGORIES = [
    ('Bières', 'Toutes les bières'),
    ('Spiritueux', 'Alcools forts'),
    ('Vins', 'Vins rouges, blancs, rosés'),
    ('Sodas', 'Boissons non alcoolisées'),
    ('Snacks', 'Grignotages et petite restauration'),
    ('Autres', 'Produits divers')
]

# Configuration des paramètres par défaut
DEFAULT_SETTINGS = [
    ('nom_bar', 'Mon Bar', 'Nom du bar'),
    ('adresse_bar', '', 'Adresse du bar'),
    ('telephone_bar', '', 'Téléphone du bar'),
    ('email_bar', '', 'Email du bar'),
    ('tva_taux', '20', 'Taux de TVA en pourcentage'),
    ('devise', 'EUR', 'Devise utilisée'),
    ('backup_auto', '1', 'Sauvegarde automatique activée'),
    ('alerte_stock', '1', 'Alertes de stock activées'),
    ('impression_auto', '0', 'Impression automatique des tickets'),
    ('mode_demo', '0', 'Mode démonstration')
]

# Configuration de l'export
EXPORT_CONFIG = {
    'excel_template': 'templates/rapport_template.xlsx',
    'pdf_template': 'templates/rapport_template.html',
    'logo_path': 'assets/logo.png',
    'company_info': {
        'name': 'Mon Bar',
        'address': '',
        'phone': '',
        'email': ''
    }
}

# Configuration des raccourcis clavier
KEYBOARD_SHORTCUTS = {
    'new_sale': 'F1',
    'search_product': 'F2', 
    'stock_view': 'F3',
    'reports': 'F4',
    'logout': 'Ctrl+Q',
    'save': 'Ctrl+S',
    'refresh': 'F5'
}

# Configuration de la validation
VALIDATION_RULES = {
    'product_name_min_length': 2,
    'product_name_max_length': 200,
    'price_min': 0.01,
    'price_max': 9999.99,
    'stock_min': 0,
    'stock_max': 999999,
    'barcode_length': [8, 13]  # EAN-8 ou EAN-13
}

# Configuration des notifications
NOTIFICATION_CONFIG = {
    'show_notifications': True,
    'notification_duration': 3000,  # millisecondes
    'notification_position': 'bottom_right',
    'sound_enabled': True,
    'sound_file': 'assets/notification.wav'
}

def get_config(section, key=None, default=None):
    """
    Récupère une valeur de configuration
    
    Args:
        section (str): Section de configuration
        key (str, optional): Clé spécifique
        default: Valeur par défaut si non trouvée
    
    Returns:
        Configuration demandée ou valeur par défaut
    """
    config_sections = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'logging': LOGGING_CONFIG,
        'security': SECURITY_CONFIG,
        'alerts': ALERTS_CONFIG,
        'reports': REPORTS_CONFIG,
        'pos': POS_CONFIG,
        'backup': BACKUP_CONFIG,
        'export': EXPORT_CONFIG,
        'notification': NOTIFICATION_CONFIG
    }
    
    section_config = config_sections.get(section, {})
    
    if key is None:
        return section_config
    
    return section_config.get(key, default)

def update_config(section, key, value):
    """
    Met à jour une valeur de configuration
    
    Args:
        section (str): Section de configuration
        key (str): Clé à mettre à jour
        value: Nouvelle valeur
    """
    config_sections = {
        'database': DATABASE_CONFIG,
        'ui': UI_CONFIG,
        'logging': LOGGING_CONFIG,
        'security': SECURITY_CONFIG,
        'alerts': ALERTS_CONFIG,
        'reports': REPORTS_CONFIG,
        'pos': POS_CONFIG,
        'backup': BACKUP_CONFIG,
        'export': EXPORT_CONFIG,
        'notification': NOTIFICATION_CONFIG
    }
    
    if section in config_sections:
        config_sections[section][key] = value
        return True
    
    return False
