"""
Gestionnaire RBAC (Role-Based Access Control) pour les permissions granulaires
"""
import json
from typing import Dict, List, Set, Optional
from enum import Enum
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class Permission(Enum):
    """Énumération des permissions disponibles"""
    # Gestion des utilisateurs
    USER_CREATE = "user_create"
    USER_READ = "user_read"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"
    USER_MANAGE_PERMISSIONS = "user_manage_permissions"
    USER_RESET_PASSWORD = "user_reset_password"
    
    # Gestion des produits
    PRODUCT_CREATE = "product_create"
    PRODUCT_READ = "product_read"
    PRODUCT_UPDATE = "product_update"
    PRODUCT_DELETE = "product_delete"
    PRODUCT_MANAGE_CATEGORIES = "product_manage_categories"
    
    # Gestion du stock
    STOCK_READ = "stock_read"
    STOCK_ADJUST = "stock_adjust"
    STOCK_INVENTORY = "stock_inventory"
    STOCK_MOVEMENTS = "stock_movements"
    
    # Ventes
    SALE_CREATE = "sale_create"
    SALE_READ = "sale_read"
    SALE_CANCEL = "sale_cancel"
    SALE_REFUND = "sale_refund"
    
    # Approvisionnements
    SUPPLY_CREATE = "supply_create"
    SUPPLY_READ = "supply_read"
    SUPPLY_UPDATE = "supply_update"
    SUPPLY_DELETE = "supply_delete"
    SUPPLY_VALIDATE = "supply_validate"
    
    # Fournisseurs
    SUPPLIER_CREATE = "supplier_create"
    SUPPLIER_READ = "supplier_read"
    SUPPLIER_UPDATE = "supplier_update"
    SUPPLIER_DELETE = "supplier_delete"
    
    # Clients à crédit
    CLIENT_CREATE = "client_create"
    CLIENT_READ = "client_read"
    CLIENT_UPDATE = "client_update"
    CLIENT_DELETE = "client_delete"
    CLIENT_MANAGE_CREDIT = "client_manage_credit"
    
    # Gestion financière
    CASH_OPEN = "cash_open"
    CASH_CLOSE = "cash_close"
    CASH_READ = "cash_read"
    CASH_MANAGE_EXPENSES = "cash_manage_expenses"
    
    # Rapports
    REPORT_SALES = "report_sales"
    REPORT_STOCK = "report_stock"
    REPORT_FINANCIAL = "report_financial"
    REPORT_AUDIT = "report_audit"
    
    # Administration
    ADMIN_SETTINGS = "admin_settings"
    ADMIN_BACKUP = "admin_backup"
    ADMIN_EXPORT = "admin_export"
    ADMIN_SECURITY = "admin_security"
    ADMIN_AUDIT = "admin_audit"
    
    # Permissions spéciales
    SYSTEM_ADMIN = "system_admin"  # Accès complet
    READ_ONLY = "read_only"  # Lecture seule

class Role:
    """Classe représentant un rôle avec ses permissions"""
    
    def __init__(self, name: str, description: str, permissions: List[Permission]):
        self.name = name
        self.description = description
        self.permissions = set(permissions)
    
    def has_permission(self, permission: Permission) -> bool:
        """Vérifie si le rôle a une permission spécifique"""
        return permission in self.permissions or Permission.SYSTEM_ADMIN in self.permissions
    
    def add_permission(self, permission: Permission):
        """Ajoute une permission au rôle"""
        self.permissions.add(permission)
    
    def remove_permission(self, permission: Permission):
        """Retire une permission du rôle"""
        self.permissions.discard(permission)
    
    def to_dict(self) -> Dict:
        """Convertit le rôle en dictionnaire"""
        return {
            'name': self.name,
            'description': self.description,
            'permissions': [p.value for p in self.permissions]
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Role':
        """Crée un rôle depuis un dictionnaire"""
        permissions = [Permission(p) for p in data.get('permissions', [])]
        return cls(data['name'], data['description'], permissions)

class RBACManager:
    """Gestionnaire RBAC pour les permissions"""
    
    def __init__(self):
        self.roles: Dict[str, Role] = {}
        self.user_roles: Dict[int, Set[str]] = {}  # user_id -> set of role names
        self.user_permissions: Dict[int, Set[Permission]] = {}  # user_id -> set of permissions
        
        # Initialiser les rôles par défaut
        self._initialize_default_roles()
        
        # Charger la configuration si elle existe
        self._load_rbac_config()
    
    def _initialize_default_roles(self):
        """Initialise les rôles par défaut"""
        # Rôle Gérant (accès complet)
        gerant_permissions = [
            # Utilisateurs
            Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE, 
            Permission.USER_DELETE, Permission.USER_MANAGE_PERMISSIONS, Permission.USER_RESET_PASSWORD,
            
            # Produits
            Permission.PRODUCT_CREATE, Permission.PRODUCT_READ, Permission.PRODUCT_UPDATE, 
            Permission.PRODUCT_DELETE, Permission.PRODUCT_MANAGE_CATEGORIES,
            
            # Stock
            Permission.STOCK_READ, Permission.STOCK_ADJUST, Permission.STOCK_INVENTORY, 
            Permission.STOCK_MOVEMENTS,
            
            # Ventes
            Permission.SALE_CREATE, Permission.SALE_READ, Permission.SALE_CANCEL, Permission.SALE_REFUND,
            
            # Approvisionnements
            Permission.SUPPLY_CREATE, Permission.SUPPLY_READ, Permission.SUPPLY_UPDATE, 
            Permission.SUPPLY_DELETE, Permission.SUPPLY_VALIDATE,
            
            # Fournisseurs
            Permission.SUPPLIER_CREATE, Permission.SUPPLIER_READ, Permission.SUPPLIER_UPDATE, 
            Permission.SUPPLIER_DELETE,
            
            # Clients
            Permission.CLIENT_CREATE, Permission.CLIENT_READ, Permission.CLIENT_UPDATE, 
            Permission.CLIENT_DELETE, Permission.CLIENT_MANAGE_CREDIT,
            
            # Financier
            Permission.CASH_OPEN, Permission.CASH_CLOSE, Permission.CASH_READ, 
            Permission.CASH_MANAGE_EXPENSES,
            
            # Rapports
            Permission.REPORT_SALES, Permission.REPORT_STOCK, Permission.REPORT_FINANCIAL, 
            Permission.REPORT_AUDIT,
            
            # Administration
            Permission.ADMIN_SETTINGS, Permission.ADMIN_BACKUP, Permission.ADMIN_EXPORT, 
            Permission.ADMIN_SECURITY, Permission.ADMIN_AUDIT
        ]
        
        self.roles['gerant'] = Role(
            'gerant', 
            'Gérant - Accès complet à toutes les fonctionnalités', 
            gerant_permissions
        )
        
        # Rôle Serveur (ventes et consultation)
        serveur_permissions = [
            Permission.PRODUCT_READ,
            Permission.STOCK_READ,
            Permission.SALE_CREATE, Permission.SALE_READ,
            Permission.CLIENT_READ, Permission.CLIENT_MANAGE_CREDIT,
            Permission.CASH_READ,
            Permission.REPORT_SALES
        ]
        
        self.roles['serveur'] = Role(
            'serveur',
            'Serveur - Ventes et consultation des données',
            serveur_permissions
        )
        
        # Rôle Caissier (ventes et caisse)
        caissier_permissions = [
            Permission.PRODUCT_READ,
            Permission.STOCK_READ,
            Permission.SALE_CREATE, Permission.SALE_READ, Permission.SALE_CANCEL,
            Permission.CLIENT_READ, Permission.CLIENT_MANAGE_CREDIT,
            Permission.CASH_OPEN, Permission.CASH_CLOSE, Permission.CASH_READ, 
            Permission.CASH_MANAGE_EXPENSES,
            Permission.REPORT_SALES, Permission.REPORT_FINANCIAL
        ]
        
        self.roles['caissier'] = Role(
            'caissier',
            'Caissier - Ventes et gestion de la caisse',
            caissier_permissions
        )
        
        # Rôle Lecture seule
        readonly_permissions = [
            Permission.PRODUCT_READ, Permission.STOCK_READ, Permission.SALE_READ,
            Permission.SUPPLIER_READ, Permission.CLIENT_READ, Permission.CASH_READ,
            Permission.REPORT_SALES, Permission.REPORT_STOCK
        ]
        
        self.roles['readonly'] = Role(
            'readonly',
            'Lecture seule - Consultation uniquement',
            readonly_permissions
        )
    
    def assign_role_to_user(self, user_id: int, role_name: str) -> bool:
        """Assigne un rôle à un utilisateur"""
        if role_name not in self.roles:
            return False
        
        if user_id not in self.user_roles:
            self.user_roles[user_id] = set()
        
        self.user_roles[user_id].add(role_name)
        self._update_user_permissions(user_id)
        return True
    
    def remove_role_from_user(self, user_id: int, role_name: str) -> bool:
        """Retire un rôle d'un utilisateur"""
        if user_id not in self.user_roles:
            return False
        
        self.user_roles[user_id].discard(role_name)
        self._update_user_permissions(user_id)
        return True
    
    def _update_user_permissions(self, user_id: int):
        """Met à jour les permissions d'un utilisateur basées sur ses rôles"""
        permissions = set()
        
        user_roles = self.user_roles.get(user_id, set())
        for role_name in user_roles:
            if role_name in self.roles:
                permissions.update(self.roles[role_name].permissions)
        
        self.user_permissions[user_id] = permissions
    
    def user_has_permission(self, user_id: int, permission: Permission) -> bool:
        """Vérifie si un utilisateur a une permission spécifique"""
        user_permissions = self.user_permissions.get(user_id, set())
        return permission in user_permissions or Permission.SYSTEM_ADMIN in user_permissions
    
    def user_has_any_permission(self, user_id: int, permissions: List[Permission]) -> bool:
        """Vérifie si un utilisateur a au moins une des permissions listées"""
        return any(self.user_has_permission(user_id, perm) for perm in permissions)
    
    def user_has_all_permissions(self, user_id: int, permissions: List[Permission]) -> bool:
        """Vérifie si un utilisateur a toutes les permissions listées"""
        return all(self.user_has_permission(user_id, perm) for perm in permissions)
    
    def get_user_roles(self, user_id: int) -> List[str]:
        """Retourne la liste des rôles d'un utilisateur"""
        return list(self.user_roles.get(user_id, set()))
    
    def get_user_permissions(self, user_id: int) -> List[Permission]:
        """Retourne la liste des permissions d'un utilisateur"""
        return list(self.user_permissions.get(user_id, set()))
    
    def create_custom_role(self, name: str, description: str, permissions: List[Permission]) -> bool:
        """Crée un rôle personnalisé"""
        if name in self.roles:
            return False
        
        self.roles[name] = Role(name, description, permissions)
        self._save_rbac_config()
        return True
    
    def update_role(self, name: str, description: Optional[str] = None, 
                   permissions: Optional[List[Permission]] = None) -> bool:
        """Met à jour un rôle existant"""
        if name not in self.roles:
            return False
        
        role = self.roles[name]
        
        if description is not None:
            role.description = description
        
        if permissions is not None:
            role.permissions = set(permissions)
            # Mettre à jour les permissions de tous les utilisateurs ayant ce rôle
            for user_id, user_roles in self.user_roles.items():
                if name in user_roles:
                    self._update_user_permissions(user_id)
        
        self._save_rbac_config()
        return True
    
    def delete_role(self, name: str) -> bool:
        """Supprime un rôle (sauf les rôles par défaut)"""
        if name in ['gerant', 'serveur', 'caissier', 'readonly']:
            return False  # Ne pas supprimer les rôles par défaut
        
        if name not in self.roles:
            return False
        
        # Retirer le rôle de tous les utilisateurs
        for user_id in list(self.user_roles.keys()):
            self.remove_role_from_user(user_id, name)
        
        del self.roles[name]
        self._save_rbac_config()
        return True
    
    def get_all_roles(self) -> Dict[str, Role]:
        """Retourne tous les rôles disponibles"""
        return self.roles.copy()
    
    def get_all_permissions(self) -> List[Permission]:
        """Retourne toutes les permissions disponibles"""
        return list(Permission)
    
    def _save_rbac_config(self):
        """Sauvegarde la configuration RBAC"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), 'rbac_config.json')
            
            config = {
                'roles': {name: role.to_dict() for name, role in self.roles.items()},
                'user_roles': {str(user_id): list(roles) for user_id, roles in self.user_roles.items()}
            }
            
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
                
        except Exception as e:
            print(f"Erreur lors de la sauvegarde RBAC: {e}")
    
    def _load_rbac_config(self):
        """Charge la configuration RBAC"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), 'rbac_config.json')
            
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Charger les rôles personnalisés (ne pas écraser les rôles par défaut)
                for name, role_data in config.get('roles', {}).items():
                    if name not in ['gerant', 'serveur', 'caissier', 'readonly']:
                        self.roles[name] = Role.from_dict(role_data)
                
                # Charger les assignations utilisateur-rôle
                for user_id_str, roles in config.get('user_roles', {}).items():
                    user_id = int(user_id_str)
                    self.user_roles[user_id] = set(roles)
                    self._update_user_permissions(user_id)
                    
        except Exception as e:
            print(f"Erreur lors du chargement RBAC: {e}")
    
    def sync_with_user_model(self, user_id: int, user_role: str):
        """Synchronise avec le modèle utilisateur existant"""
        # Mapper les anciens rôles vers les nouveaux
        role_mapping = {
            'gerant': 'gerant',
            'serveur': 'serveur',
            'caissier': 'caissier'
        }
        
        new_role = role_mapping.get(user_role, 'readonly')
        
        # Nettoyer les anciens rôles
        self.user_roles[user_id] = set()
        
        # Assigner le nouveau rôle
        self.assign_role_to_user(user_id, new_role)
    
    def check_permission_decorator(self, required_permission: Permission):
        """Décorateur pour vérifier les permissions"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Récupérer l'utilisateur depuis les arguments (convention)
                user_id = None
                if 'current_user' in kwargs:
                    user_id = kwargs['current_user'].id
                elif len(args) > 0 and hasattr(args[0], 'current_user'):
                    user_id = args[0].current_user.id
                
                if user_id and self.user_has_permission(user_id, required_permission):
                    return func(*args, **kwargs)
                else:
                    raise PermissionError(f"Permission requise: {required_permission.value}")
            
            return wrapper
        return decorator

# Instance globale du gestionnaire RBAC
rbac_manager = RBACManager()
