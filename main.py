"""
Application principale de gestion de stock pour bar
"""
import sys
import os
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bar_stock.log'),
        logging.StreamHandler()
    ]
)

# Ajouter le répertoire courant au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.main_window import MainWindow
    from database.db_manager import DatabaseManager
    from models.produit import Produit
    from models.categorie import Categorie
    from models.fournisseur import Fournisseur
    from models.utilisateur import Utilisateur
except ImportError as e:
    print(f"Erreur d'import: {e}")
    print("Assurez-vous que tous les modules sont présents")
    sys.exit(1)

def init_sample_data():
    """Initialise des données d'exemple si la base est vide"""
    try:
        # Vérifier si des produits existent déjà
        produits_existants = Produit.count()
        if produits_existants > 0:
            logging.info(f"Base de données déjà initialisée avec {produits_existants} produits")
            return
        
        logging.info("Initialisation des données d'exemple...")
        
        # Récupérer les catégories existantes
        categories = Categorie.find_all()
        cat_bieres = next((c for c in categories if c.nom == "Bières"), None)
        cat_spiritueux = next((c for c in categories if c.nom == "Spiritueux"), None)
        cat_sodas = next((c for c in categories if c.nom == "Sodas"), None)
        cat_snacks = next((c for c in categories if c.nom == "Snacks"), None)
        
        # Créer un fournisseur d'exemple
        fournisseur = Fournisseur()
        fournisseur.nom = "Distributeur Boissons SA"
        fournisseur.contact = "Jean Dupont"
        fournisseur.telephone = "01 23 45 67 89"
        fournisseur.email = "<EMAIL>"
        fournisseur.adresse = "123 Rue de la Distribution, 75001 Paris"
        fournisseur.save()
        
        # Produits d'exemple
        produits_exemple = [
            # Bières
            {
                "nom": "Bière Blonde 33cl",
                "code_barre": "3456789012345",
                "categorie_id": cat_bieres.id if cat_bieres else None,
                "unite": "bouteille",
                "prix_achat": 1.20,
                "prix_vente": 3.50,
                "stock_actuel": 48,
                "seuil_minimum": 12,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Bière Brune 33cl",
                "code_barre": "3456789012346",
                "categorie_id": cat_bieres.id if cat_bieres else None,
                "unite": "bouteille",
                "prix_achat": 1.30,
                "prix_vente": 3.80,
                "stock_actuel": 24,
                "seuil_minimum": 12,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Bière Pression 50cl",
                "code_barre": "3456789012347",
                "categorie_id": cat_bieres.id if cat_bieres else None,
                "unite": "verre",
                "prix_achat": 0.80,
                "prix_vente": 4.50,
                "stock_actuel": 100,
                "seuil_minimum": 20,
                "fournisseur_id": fournisseur.id
            },
            # Spiritueux
            {
                "nom": "Whisky 4cl",
                "code_barre": "3456789012348",
                "categorie_id": cat_spiritueux.id if cat_spiritueux else None,
                "unite": "dose",
                "prix_achat": 1.50,
                "prix_vente": 6.00,
                "stock_actuel": 80,
                "seuil_minimum": 15,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Vodka 4cl",
                "code_barre": "3456789012349",
                "categorie_id": cat_spiritueux.id if cat_spiritueux else None,
                "unite": "dose",
                "prix_achat": 1.20,
                "prix_vente": 5.50,
                "stock_actuel": 60,
                "seuil_minimum": 15,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Rhum 4cl",
                "code_barre": "3456789012350",
                "categorie_id": cat_spiritueux.id if cat_spiritueux else None,
                "unite": "dose",
                "prix_achat": 1.40,
                "prix_vente": 5.80,
                "stock_actuel": 45,
                "seuil_minimum": 15,
                "fournisseur_id": fournisseur.id
            },
            # Sodas
            {
                "nom": "Coca-Cola 33cl",
                "code_barre": "3456789012351",
                "categorie_id": cat_sodas.id if cat_sodas else None,
                "unite": "canette",
                "prix_achat": 0.60,
                "prix_vente": 2.50,
                "stock_actuel": 72,
                "seuil_minimum": 24,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Orangina 33cl",
                "code_barre": "3456789012352",
                "categorie_id": cat_sodas.id if cat_sodas else None,
                "unite": "bouteille",
                "prix_achat": 0.70,
                "prix_vente": 2.80,
                "stock_actuel": 36,
                "seuil_minimum": 18,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Eau minérale 50cl",
                "code_barre": "3456789012353",
                "categorie_id": cat_sodas.id if cat_sodas else None,
                "unite": "bouteille",
                "prix_achat": 0.40,
                "prix_vente": 2.00,
                "stock_actuel": 48,
                "seuil_minimum": 20,
                "fournisseur_id": fournisseur.id
            },
            # Snacks
            {
                "nom": "Chips nature",
                "code_barre": "3456789012354",
                "categorie_id": cat_snacks.id if cat_snacks else None,
                "unite": "sachet",
                "prix_achat": 0.80,
                "prix_vente": 2.50,
                "stock_actuel": 30,
                "seuil_minimum": 10,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Cacahuètes salées",
                "code_barre": "3456789012355",
                "categorie_id": cat_snacks.id if cat_snacks else None,
                "unite": "sachet",
                "prix_achat": 0.90,
                "prix_vente": 2.80,
                "stock_actuel": 25,
                "seuil_minimum": 10,
                "fournisseur_id": fournisseur.id
            },
            {
                "nom": "Olives vertes",
                "code_barre": "3456789012356",
                "categorie_id": cat_snacks.id if cat_snacks else None,
                "unite": "portion",
                "prix_achat": 1.20,
                "prix_vente": 3.50,
                "stock_actuel": 20,
                "seuil_minimum": 8,
                "fournisseur_id": fournisseur.id
            }
        ]
        
        # Créer les produits
        for produit_data in produits_exemple:
            produit = Produit()
            for key, value in produit_data.items():
                setattr(produit, key, value)
            produit.save()
        
        # Créer quelques utilisateurs supplémentaires
        # Serveur
        serveur = Utilisateur()
        serveur.nom_utilisateur = "serveur1"
        serveur.nom_complet = "Marie Martin"
        serveur.role = "serveur"
        serveur.set_password("serveur123")
        serveur.save()
        
        # Caissier
        caissier = Utilisateur()
        caissier.nom_utilisateur = "caissier1"
        caissier.nom_complet = "Pierre Durand"
        caissier.role = "caissier"
        caissier.set_password("caissier123")
        caissier.save()
        
        logging.info(f"Données d'exemple créées: {len(produits_exemple)} produits, 1 fournisseur, 2 utilisateurs")
        
    except Exception as e:
        logging.error(f"Erreur lors de l'initialisation des données d'exemple: {e}")

def main():
    """Fonction principale"""
    try:
        logging.info("Démarrage de l'application de gestion de stock du bar")
        
        # Initialiser la base de données
        db = DatabaseManager()
        logging.info("Base de données initialisée")
        
        # Initialiser les données d'exemple si nécessaire
        init_sample_data()
        
        # Lancer l'interface graphique
        app = MainWindow()
        logging.info("Interface graphique initialisée")
        
        # Démarrer l'application
        app.run()
        
    except Exception as e:
        logging.error(f"Erreur fatale: {e}")
        import traceback
        traceback.print_exc()
        
        # Afficher un message d'erreur à l'utilisateur
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # Cacher la fenêtre principale
            
            messagebox.showerror(
                "Erreur fatale",
                f"Une erreur fatale s'est produite:\n\n{str(e)}\n\n"
                "Consultez le fichier bar_stock.log pour plus de détails."
            )
        except:
            print(f"Erreur fatale: {e}")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
