"""
Gestionnaire d'export de données
"""
import csv
import json
import os
from datetime import datetime
import logging

class ExportManager:
    def __init__(self, export_dir="exports"):
        self.export_dir = export_dir
        self.ensure_export_dir()
    
    def ensure_export_dir(self):
        """S'assure que le répertoire d'export existe"""
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)
    
    def export_to_csv(self, data, filename=None, headers=None):
        """Exporte des données vers un fichier CSV"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"export_{timestamp}.csv"
        
        filepath = os.path.join(self.export_dir, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if not data:
                    return filepath
                
                # Déterminer les en-têtes
                if headers:
                    fieldnames = headers
                elif isinstance(data[0], dict):
                    fieldnames = data[0].keys()
                else:
                    fieldnames = [f"Colonne_{i+1}" for i in range(len(data[0]))]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                # Écrire les données
                for row in data:
                    if isinstance(row, dict):
                        writer.writerow(row)
                    else:
                        # Convertir les listes/tuples en dictionnaires
                        row_dict = {fieldnames[i]: row[i] if i < len(row) else "" 
                                   for i in range(len(fieldnames))}
                        writer.writerow(row_dict)
            
            logging.info(f"Export CSV créé: {filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"Erreur lors de l'export CSV: {e}")
            raise
    
    def export_to_json(self, data, filename=None):
        """Exporte des données vers un fichier JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"export_{timestamp}.json"
        
        filepath = os.path.join(self.export_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False, default=str)
            
            logging.info(f"Export JSON créé: {filepath}")
            return filepath
            
        except Exception as e:
            logging.error(f"Erreur lors de l'export JSON: {e}")
            raise
    
    def export_produits_csv(self, produits):
        """Exporte la liste des produits en CSV"""
        headers = [
            'ID', 'Nom', 'Code-barres', 'Catégorie', 'Unité',
            'Prix d\'achat', 'Prix de vente', 'Stock actuel', 'Seuil minimum',
            'Fournisseur', 'Actif', 'Date création'
        ]
        
        data = []
        for produit in produits:
            data.append({
                'ID': produit.id,
                'Nom': produit.nom,
                'Code-barres': produit.code_barre or '',
                'Catégorie': produit.get_categorie_nom(),
                'Unité': produit.unite,
                'Prix d\'achat': f"{produit.prix_achat:.2f}",
                'Prix de vente': f"{produit.prix_vente:.2f}",
                'Stock actuel': produit.stock_actuel,
                'Seuil minimum': produit.seuil_minimum,
                'Fournisseur': produit.get_fournisseur_nom(),
                'Actif': 'Oui' if produit.actif else 'Non',
                'Date création': produit.created_at
            })
        
        return self.export_to_csv(data, f"produits_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def export_ventes_csv(self, ventes):
        """Exporte la liste des ventes en CSV"""
        headers = [
            'ID', 'Numéro ticket', 'Date', 'Total HT', 'Total TTC',
            'Mode paiement', 'Vendeur', 'Client crédit', 'Statut'
        ]
        
        data = []
        for vente in ventes:
            data.append({
                'ID': vente.id,
                'Numéro ticket': vente.numero_ticket,
                'Date': vente.date_vente,
                'Total HT': f"{vente.total_ht:.2f}",
                'Total TTC': f"{vente.total_ttc:.2f}",
                'Mode paiement': vente.mode_paiement,
                'Vendeur': vente.get_utilisateur_nom(),
                'Client crédit': vente.get_client_credit_nom() or '',
                'Statut': vente.statut
            })
        
        return self.export_to_csv(data, f"ventes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def export_details_ventes_csv(self, ventes):
        """Exporte les détails des ventes en CSV"""
        headers = [
            'ID Vente', 'Numéro ticket', 'Date', 'Produit', 'Quantité',
            'Prix unitaire', 'Sous-total', 'Vendeur'
        ]
        
        data = []
        for vente in ventes:
            for detail in vente.details:
                data.append({
                    'ID Vente': vente.id,
                    'Numéro ticket': vente.numero_ticket,
                    'Date': vente.date_vente,
                    'Produit': detail.get_produit_nom(),
                    'Quantité': detail.quantite,
                    'Prix unitaire': f"{detail.prix_unitaire:.2f}",
                    'Sous-total': f"{detail.sous_total:.2f}",
                    'Vendeur': vente.get_utilisateur_nom()
                })
        
        return self.export_to_csv(data, f"details_ventes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def export_mouvements_stock_csv(self, mouvements):
        """Exporte les mouvements de stock en CSV"""
        headers = [
            'ID', 'Date', 'Produit', 'Type mouvement', 'Quantité',
            'Prix unitaire', 'Référence', 'Utilisateur', 'Commentaire'
        ]
        
        data = []
        for mouvement in mouvements:
            data.append({
                'ID': mouvement.id,
                'Date': mouvement.date_mouvement,
                'Produit': mouvement.get_produit_nom(),
                'Type mouvement': mouvement.type_mouvement,
                'Quantité': mouvement.quantite,
                'Prix unitaire': f"{mouvement.prix_unitaire:.2f}" if mouvement.prix_unitaire else '',
                'Référence': mouvement.reference or '',
                'Utilisateur': mouvement.get_utilisateur_nom(),
                'Commentaire': mouvement.commentaire or ''
            })
        
        return self.export_to_csv(data, f"mouvements_stock_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def export_clients_credit_csv(self, clients):
        """Exporte la liste des clients à crédit en CSV"""
        headers = [
            'ID', 'Nom', 'Téléphone', 'Limite crédit', 'Crédit utilisé',
            'Crédit disponible', 'Actif', 'Date création'
        ]
        
        data = []
        for client in clients:
            data.append({
                'ID': client.id,
                'Nom': client.nom,
                'Téléphone': client.telephone or '',
                'Limite crédit': f"{client.limite_credit:.2f}",
                'Crédit utilisé': f"{client.credit_utilise:.2f}",
                'Crédit disponible': f"{client.get_credit_disponible():.2f}",
                'Actif': 'Oui' if client.actif else 'Non',
                'Date création': client.created_at
            })
        
        return self.export_to_csv(data, f"clients_credit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def export_rapport_ventes_periode(self, ventes, date_debut, date_fin):
        """Exporte un rapport de ventes pour une période"""
        # Statistiques générales
        total_ca = sum(vente.total_ttc for vente in ventes)
        nb_ventes = len(ventes)
        ticket_moyen = total_ca / nb_ventes if nb_ventes > 0 else 0
        
        # Ventes par jour
        ventes_par_jour = {}
        for vente in ventes:
            date = datetime.fromisoformat(vente.date_vente).date()
            if date not in ventes_par_jour:
                ventes_par_jour[date] = {'nb': 0, 'ca': 0}
            ventes_par_jour[date]['nb'] += 1
            ventes_par_jour[date]['ca'] += vente.total_ttc
        
        # Préparer les données
        rapport_data = {
            'periode': {
                'debut': str(date_debut),
                'fin': str(date_fin)
            },
            'statistiques': {
                'nombre_ventes': nb_ventes,
                'chiffre_affaires': total_ca,
                'ticket_moyen': ticket_moyen
            },
            'ventes_par_jour': [
                {
                    'date': str(date),
                    'nombre_ventes': data['nb'],
                    'chiffre_affaires': data['ca'],
                    'ticket_moyen': data['ca'] / data['nb'] if data['nb'] > 0 else 0
                }
                for date, data in sorted(ventes_par_jour.items())
            ],
            'ventes_detail': [vente.to_dict() for vente in ventes]
        }
        
        # Export JSON
        json_file = self.export_to_json(
            rapport_data, 
            f"rapport_ventes_{date_debut}_{date_fin}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        # Export CSV des ventes par jour
        csv_file = self.export_to_csv(
            rapport_data['ventes_par_jour'],
            f"ventes_par_jour_{date_debut}_{date_fin}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )
        
        return {'json': json_file, 'csv': csv_file}
    
    def export_inventaire_csv(self, produits):
        """Exporte un inventaire des produits en CSV"""
        headers = [
            'Code-barres', 'Nom', 'Catégorie', 'Stock actuel', 'Unité',
            'Prix achat', 'Valeur stock', 'Seuil minimum', 'Statut'
        ]
        
        data = []
        for produit in produits:
            valeur_stock = produit.stock_actuel * produit.prix_achat
            statut = "Stock faible" if produit.is_stock_faible() else "OK"
            
            data.append({
                'Code-barres': produit.code_barre or '',
                'Nom': produit.nom,
                'Catégorie': produit.get_categorie_nom(),
                'Stock actuel': produit.stock_actuel,
                'Unité': produit.unite,
                'Prix achat': f"{produit.prix_achat:.2f}",
                'Valeur stock': f"{valeur_stock:.2f}",
                'Seuil minimum': produit.seuil_minimum,
                'Statut': statut
            })
        
        return self.export_to_csv(data, f"inventaire_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    def create_excel_export(self, data_dict, filename=None):
        """Crée un export Excel avec plusieurs feuilles (nécessite openpyxl)"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment

            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"export_{timestamp}.xlsx"

            filepath = os.path.join(self.export_dir, filename)

            wb = openpyxl.Workbook()

            # Supprimer la feuille par défaut
            wb.remove(wb.active)

            for sheet_name, data in data_dict.items():
                ws = wb.create_sheet(title=sheet_name[:31])  # Limiter le nom à 31 caractères

                if not data:
                    continue

                # Déterminer les en-têtes
                if isinstance(data[0], dict):
                    headers = list(data[0].keys())
                else:
                    headers = [f"Colonne_{i+1}" for i in range(len(data[0]))]

                # Écrire les en-têtes
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # Écrire les données
                for row_idx, row_data in enumerate(data, 2):
                    if isinstance(row_data, dict):
                        for col_idx, header in enumerate(headers, 1):
                            value = row_data.get(header, "")
                            ws.cell(row=row_idx, column=col_idx, value=value)
                    else:
                        for col_idx, value in enumerate(row_data, 1):
                            ws.cell(row=row_idx, column=col_idx, value=value)

                # Ajuster la largeur des colonnes
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

            wb.save(filepath)
            logging.info(f"Export Excel créé: {filepath}")
            return filepath

        except ImportError:
            raise ImportError("Module openpyxl non installé. Installez-le avec: pip install openpyxl")
        except Exception as e:
            logging.error(f"Erreur lors de la création de l'export Excel: {e}")
            raise
