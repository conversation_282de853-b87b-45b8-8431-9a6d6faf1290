"""
Fenêtre de gestion financière
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.journal_caisse import JournalCaisse
from models.vente import Vente

class FinancierWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion Financière", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="🔓 Ouvrir caisse", 
                  command=self.ouvrir_caisse).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔒 Fermer caisse", 
                  command=self.fermer_caisse).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="💸 Nouvelle dépense", 
                  command=self.nouvelle_depense).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Notebook pour les onglets
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Journal de caisse
        journal_frame = ttk.Frame(notebook)
        notebook.add(journal_frame, text="Journal de caisse")
        
        # Onglet Recettes et dépenses
        recettes_frame = ttk.Frame(notebook)
        notebook.add(recettes_frame, text="Recettes & Dépenses")
        
        # Onglet Statistiques
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistiques")
        
        self.setup_journal_tab(journal_frame)
        self.setup_recettes_tab(recettes_frame)
        self.setup_stats_tab(stats_frame)
    
    def setup_journal_tab(self, parent):
        """Configure l'onglet journal de caisse"""
        # Informations de la caisse
        info_frame = ttk.LabelFrame(parent, text="État de la caisse", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Grid pour les informations
        info_grid = ttk.Frame(info_frame)
        info_grid.pack(fill=tk.X)
        
        for i in range(4):
            info_grid.columnconfigure(i, weight=1)
        
        # Cartes d'information
        self.create_info_card(info_grid, "Statut caisse", "Fermée", 0, 0, "status")
        self.create_info_card(info_grid, "Solde actuel", "0.00 €", 0, 1, "solde")
        self.create_info_card(info_grid, "Recettes du jour", "0.00 €", 0, 2, "recettes")
        self.create_info_card(info_grid, "Dépenses du jour", "0.00 €", 0, 3, "depenses")
        
        # Filtres
        filters_frame = ttk.Frame(parent)
        filters_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Label(filters_frame, text="Date:").pack(side=tk.LEFT)
        self.date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        date_entry = ttk.Entry(filters_frame, textvariable=self.date_var, width=12)
        date_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(filters_frame, text="Aujourd'hui", 
                  command=lambda: self.set_date(date.today())).pack(side=tk.LEFT, padx=2)
        ttk.Button(filters_frame, text="Hier", 
                  command=lambda: self.set_date(date.today() - timedelta(days=1))).pack(side=tk.LEFT, padx=2)
        ttk.Button(filters_frame, text="Filtrer", 
                  command=self.load_journal).pack(side=tk.LEFT, padx=(10, 0))
        
        # Journal des opérations
        journal_frame = ttk.LabelFrame(parent, text="Journal des opérations", padding="5")
        journal_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour le journal
        columns = ("Heure", "Type", "Description", "Entrée", "Sortie", "Solde", "Utilisateur")
        self.journal_tree = ttk.Treeview(journal_frame, columns=columns, show="headings")
        
        for col in columns:
            self.journal_tree.heading(col, text=col)
            self.journal_tree.column(col, width=100)
        
        # Ajuster les largeurs
        self.journal_tree.column("Description", width=200)
        self.journal_tree.column("Utilisateur", width=120)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(journal_frame, orient=tk.VERTICAL, command=self.journal_tree.yview)
        h_scrollbar = ttk.Scrollbar(journal_frame, orient=tk.HORIZONTAL, command=self.journal_tree.xview)
        self.journal_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.journal_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_recettes_tab(self, parent):
        """Configure l'onglet recettes et dépenses"""
        # Période de sélection
        period_frame = ttk.LabelFrame(parent, text="Période d'analyse", padding="10")
        period_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(period_frame, text="Du:").pack(side=tk.LEFT)
        self.date_debut_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        ttk.Entry(period_frame, textvariable=self.date_debut_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(period_frame, text="Au:").pack(side=tk.LEFT)
        self.date_fin_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        ttk.Entry(period_frame, textvariable=self.date_fin_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(period_frame, text="Cette semaine", 
                  command=self.set_current_week).pack(side=tk.LEFT, padx=2)
        ttk.Button(period_frame, text="Ce mois", 
                  command=self.set_current_month).pack(side=tk.LEFT, padx=2)
        ttk.Button(period_frame, text="Analyser", 
                  command=self.load_recettes_depenses).pack(side=tk.LEFT, padx=(10, 0))
        
        # Résumé
        summary_frame = ttk.LabelFrame(parent, text="Résumé de la période", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=10)
        
        summary_grid = ttk.Frame(summary_frame)
        summary_grid.pack(fill=tk.X)
        
        for i in range(3):
            summary_grid.columnconfigure(i, weight=1)
        
        self.create_info_card(summary_grid, "Total recettes", "0.00 €", 0, 0, "total_recettes")
        self.create_info_card(summary_grid, "Total dépenses", "0.00 €", 0, 1, "total_depenses")
        self.create_info_card(summary_grid, "Résultat", "0.00 €", 0, 2, "resultat")
        
        # Détail des opérations
        detail_frame = ttk.LabelFrame(parent, text="Détail des opérations", padding="5")
        detail_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour le détail
        columns = ("Date", "Type", "Description", "Montant", "Mode paiement", "Utilisateur")
        self.detail_tree = ttk.Treeview(detail_frame, columns=columns, show="headings")
        
        for col in columns:
            self.detail_tree.heading(col, text=col)
            self.detail_tree.column(col, width=100)
        
        self.detail_tree.column("Description", width=200)
        
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_tree.yview)
        self.detail_tree.configure(yscrollcommand=detail_scrollbar.set)
        
        self.detail_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_stats_tab(self, parent):
        """Configure l'onglet statistiques"""
        # Statistiques générales
        stats_frame = ttk.LabelFrame(parent, text="Statistiques générales", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.stats_text = tk.Text(stats_frame, height=15, width=80, state=tk.DISABLED)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bouton de mise à jour
        ttk.Button(parent, text="Actualiser les statistiques", 
                  command=self.load_statistics).pack(pady=10)
    
    def create_info_card(self, parent, title, value, row, col, key):
        """Crée une carte d'information"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(content_frame, text=title, font=('Arial', 10)).pack(anchor=tk.W)
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 12, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"info_{key}", value_label)
    
    def set_date(self, selected_date):
        """Définit la date sélectionnée"""
        self.date_var.set(selected_date.strftime("%Y-%m-%d"))
        self.load_journal()
    
    def set_current_week(self):
        """Définit la semaine courante"""
        today = date.today()
        start_week = today - timedelta(days=today.weekday())
        self.date_debut_var.set(start_week.strftime("%Y-%m-%d"))
        self.date_fin_var.set(today.strftime("%Y-%m-%d"))
    
    def set_current_month(self):
        """Définit le mois courant"""
        today = date.today()
        start_month = today.replace(day=1)
        self.date_debut_var.set(start_month.strftime("%Y-%m-%d"))
        self.date_fin_var.set(today.strftime("%Y-%m-%d"))
    
    def load_data(self):
        """Charge toutes les données"""
        self.load_caisse_info()
        self.load_journal()
        self.load_recettes_depenses()
        self.load_statistics()
    
    def load_caisse_info(self):
        """Charge les informations de la caisse"""
        try:
            # Statut de la caisse
            is_open = JournalCaisse.is_caisse_ouverte()
            status_text = "🔓 Ouverte" if is_open else "🔒 Fermée"
            status_color = "green" if is_open else "red"
            
            self.info_status.config(text=status_text, foreground=status_color)
            
            # Solde actuel
            solde = JournalCaisse.get_solde_actuel()
            self.info_solde.config(text=f"{solde:.2f} €")
            
            # Recettes et dépenses du jour
            today = date.today()
            operations = JournalCaisse.get_operations_jour(today)
            
            recettes_jour = sum(op.montant_entree for op in operations if op.type_operation in ['vente', 'ouverture'])
            depenses_jour = sum(op.montant_sortie for op in operations if op.type_operation == 'depense')
            
            self.info_recettes.config(text=f"{recettes_jour:.2f} €")
            self.info_depenses.config(text=f"{depenses_jour:.2f} €")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des informations: {e}")
    
    def load_journal(self):
        """Charge le journal de caisse"""
        # Nettoyer le treeview
        for item in self.journal_tree.get_children():
            self.journal_tree.delete(item)
        
        try:
            selected_date = datetime.strptime(self.date_var.get(), "%Y-%m-%d").date()
            operations = JournalCaisse.get_operations_jour(selected_date)
            
            for operation in operations:
                # Formater l'heure
                heure = datetime.fromisoformat(operation.created_at).strftime("%H:%M:%S")
                
                # Icône selon le type
                type_icons = {
                    "ouverture": "🔓 Ouverture",
                    "vente": "💰 Vente",
                    "depense": "💸 Dépense",
                    "fermeture": "🔒 Fermeture"
                }
                type_text = type_icons.get(operation.type_operation, operation.type_operation)
                
                # Montants
                entree_text = f"{operation.montant_entree:.2f} €" if operation.montant_entree > 0 else ""
                sortie_text = f"{operation.montant_sortie:.2f} €" if operation.montant_sortie > 0 else ""
                
                self.journal_tree.insert("", "end", values=(
                    heure,
                    type_text,
                    operation.description,
                    entree_text,
                    sortie_text,
                    f"{operation.solde:.2f} €",
                    operation.get_utilisateur_nom()
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement du journal: {e}")
    
    def load_recettes_depenses(self):
        """Charge les recettes et dépenses de la période"""
        try:
            date_debut = datetime.strptime(self.date_debut_var.get(), "%Y-%m-%d").date()
            date_fin = datetime.strptime(self.date_fin_var.get(), "%Y-%m-%d").date()
            
            # Recettes
            total_recettes, operations_recettes = JournalCaisse.get_recettes_periode(date_debut, date_fin)
            
            # Dépenses
            total_depenses, operations_depenses = JournalCaisse.get_depenses_periode(date_debut, date_fin)
            
            # Résultat
            resultat = total_recettes - total_depenses
            
            # Mettre à jour les cartes
            self.info_total_recettes.config(text=f"{total_recettes:.2f} €")
            self.info_total_depenses.config(text=f"{total_depenses:.2f} €")
            
            resultat_color = "green" if resultat >= 0 else "red"
            self.info_resultat.config(text=f"{resultat:.2f} €", foreground=resultat_color)
            
            # Charger le détail
            self.load_detail_operations(operations_recettes + operations_depenses)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des recettes/dépenses: {e}")
    
    def load_detail_operations(self, operations):
        """Charge le détail des opérations"""
        # Nettoyer le treeview
        for item in self.detail_tree.get_children():
            self.detail_tree.delete(item)
        
        # Trier par date
        operations_sorted = sorted(operations, key=lambda x: x.created_at, reverse=True)
        
        for operation in operations_sorted:
            type_text = {
                "vente": "💰 Recette",
                "depense": "💸 Dépense",
                "ouverture": "🔓 Ouverture"
            }.get(operation.type_operation, operation.type_operation)
            
            montant = operation.montant_entree if operation.montant_entree > 0 else -operation.montant_sortie
            montant_text = f"{montant:+.2f} €"
            
            self.detail_tree.insert("", "end", values=(
                operation.date_operation,
                type_text,
                operation.description,
                montant_text,
                operation.mode_paiement or "",
                operation.get_utilisateur_nom()
            ))
    
    def load_statistics(self):
        """Charge les statistiques générales"""
        try:
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            
            stats_text = "STATISTIQUES FINANCIÈRES\n"
            stats_text += "=" * 50 + "\n\n"
            
            # Statistiques de la semaine
            today = date.today()
            start_week = today - timedelta(days=today.weekday())
            
            total_recettes_semaine, _ = JournalCaisse.get_recettes_periode(start_week, today)
            total_depenses_semaine, _ = JournalCaisse.get_depenses_periode(start_week, today)
            
            stats_text += f"CETTE SEMAINE ({start_week} au {today}):\n"
            stats_text += f"  Recettes: {total_recettes_semaine:.2f} €\n"
            stats_text += f"  Dépenses: {total_depenses_semaine:.2f} €\n"
            stats_text += f"  Résultat: {total_recettes_semaine - total_depenses_semaine:.2f} €\n\n"
            
            # Statistiques du mois
            start_month = today.replace(day=1)
            
            total_recettes_mois, _ = JournalCaisse.get_recettes_periode(start_month, today)
            total_depenses_mois, _ = JournalCaisse.get_depenses_periode(start_month, today)
            
            stats_text += f"CE MOIS ({start_month} au {today}):\n"
            stats_text += f"  Recettes: {total_recettes_mois:.2f} €\n"
            stats_text += f"  Dépenses: {total_depenses_mois:.2f} €\n"
            stats_text += f"  Résultat: {total_recettes_mois - total_depenses_mois:.2f} €\n\n"
            
            # Solde actuel
            solde_actuel = JournalCaisse.get_solde_actuel()
            stats_text += f"SOLDE ACTUEL DE LA CAISSE: {solde_actuel:.2f} €\n\n"
            
            # Statut de la caisse
            is_open = JournalCaisse.is_caisse_ouverte()
            status = "OUVERTE" if is_open else "FERMÉE"
            stats_text += f"STATUT DE LA CAISSE: {status}\n"
            
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des statistiques: {e}")
    
    def ouvrir_caisse(self):
        """Ouvre la caisse"""
        try:
            from gui.dialogs.ouverture_caisse_dialog import OuvertureCaisseDialog
            dialog = OuvertureCaisseDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
        except ImportError:
            # Dialogue simple
            self.ouvrir_caisse_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture de caisse: {e}")
    
    def ouvrir_caisse_simple(self):
        """Dialogue simple pour ouvrir la caisse"""
        if JournalCaisse.is_caisse_ouverte():
            messagebox.showinfo("Info", "La caisse est déjà ouverte aujourd'hui")
            return
        
        # Demander le montant initial
        dialog = tk.Toplevel(self)
        dialog.title("Ouverture de caisse")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Montant initial (fond de caisse):").pack(pady=10)
        
        montant_var = tk.StringVar(value="50.00")
        ttk.Entry(dialog, textvariable=montant_var, width=15).pack(pady=5)
        
        def valider():
            try:
                montant = float(montant_var.get())
                JournalCaisse.ouvrir_caisse(self.current_user.id, montant)
                dialog.destroy()
                self.load_data()
                messagebox.showinfo("Succès", f"Caisse ouverte avec {montant:.2f} € de fond")
            except ValueError:
                messagebox.showerror("Erreur", "Montant invalide")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur: {e}")
        
        ttk.Button(dialog, text="Ouvrir", command=valider).pack(pady=10)
    
    def fermer_caisse(self):
        """Ferme la caisse"""
        if not JournalCaisse.is_caisse_ouverte():
            messagebox.showinfo("Info", "La caisse n'est pas ouverte")
            return
        
        try:
            from gui.dialogs.fermeture_caisse_dialog import FermetureCaisseDialog
            dialog = FermetureCaisseDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
        except ImportError:
            # Dialogue simple
            self.fermer_caisse_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la fermeture de caisse: {e}")
    
    def fermer_caisse_simple(self):
        """Dialogue simple pour fermer la caisse"""
        try:
            JournalCaisse.fermer_caisse(self.current_user.id)
            self.load_data()
            messagebox.showinfo("Succès", "Caisse fermée")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def nouvelle_depense(self):
        """Ajoute une nouvelle dépense"""
        try:
            from gui.dialogs.depense_dialog import DepenseDialog
            dialog = DepenseDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
        except ImportError:
            # Dialogue simple
            self.nouvelle_depense_simple()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout de dépense: {e}")
    
    def nouvelle_depense_simple(self):
        """Dialogue simple pour ajouter une dépense"""
        dialog = tk.Toplevel(self)
        dialog.title("Nouvelle dépense")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Montant (€):").pack(pady=5)
        montant_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=montant_var, width=15).pack(pady=5)
        
        ttk.Label(dialog, text="Description:").pack(pady=5)
        description_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=description_var, width=40).pack(pady=5)
        
        def valider():
            try:
                montant = float(montant_var.get())
                description = description_var.get().strip()
                
                if not description:
                    messagebox.showerror("Erreur", "Description obligatoire")
                    return
                
                JournalCaisse.enregistrer_depense(montant, description, self.current_user.id)
                dialog.destroy()
                self.load_data()
                messagebox.showinfo("Succès", f"Dépense de {montant:.2f} € enregistrée")
                
            except ValueError:
                messagebox.showerror("Erreur", "Montant invalide")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur: {e}")
        
        ttk.Button(dialog, text="Enregistrer", command=valider).pack(pady=20)
