"""
Middleware de sécurité pour toutes les fenêtres de l'application
"""
import logging
from datetime import datetime
from tkinter import messagebox
from utils.session_manager import session_manager

class SecurityMiddleware:
    """Middleware de sécurité centralisé"""
    
    def __init__(self):
        self.required_permissions = {
            'ProduitsWindow': ['produits'],
            'VentesWindow': ['ventes'],
            'StockWindow': ['stock'],
            'UtilisateursWindow': ['admin'],
            'RapportsWindow': ['rapports'],
            'ApprovisionnementWindow': ['stock', 'approvisionnements'],
            'ClientsCreditWindow': ['client_credit'],
            'ParametresWindow': ['admin'],
            'FinancierWindow': ['admin'],
            'AuditWindow': ['admin'],
            'SecurityWindow': ['admin'],
            'AdminWindow': ['admin']
        }
        
        self.required_roles = {
            'AdminWindow': ['gerant'],
            'SecurityWindow': ['gerant'],
            'AuditWindow': ['gerant', 'admin']
        }
    
    def check_access(self, window_class_name, user):
        """Vérifie l'accès à une fenêtre"""
        if not user:
            messagebox.showerror("Accès refusé", "Vous devez être connecté pour accéder à cette section")
            return False
        
        # Vérifier la session
        if not self._validate_session(user):
            return False
        
        # Vérifier les permissions
        if not self._check_permissions(window_class_name, user):
            return False
        
        # Vérifier les rôles
        if not self._check_roles(window_class_name, user):
            return False
        
        # Enregistrer l'accès
        self._log_access(window_class_name, user)
        
        return True
    
    def _validate_session(self, user):
        """Valide la session utilisateur"""
        if not hasattr(user, 'current_session'):
            messagebox.showerror("Session invalide", "Session utilisateur non trouvée. Veuillez vous reconnecter.")
            return False
        
        session = session_manager.get_session(user.current_session.session_id)
        
        if not session:
            messagebox.showerror("Session expirée", "Votre session a expiré. Veuillez vous reconnecter.")
            return False
        
        # Mettre à jour l'activité
        session.update_activity()
        user.last_activity = session.last_activity
        
        return True
    
    def _check_permissions(self, window_class_name, user):
        """Vérifie les permissions requises"""
        required_perms = self.required_permissions.get(window_class_name, [])
        
        for permission in required_perms:
            if not user.has_permission(permission):
                messagebox.showerror(
                    "Accès refusé", 
                    f"Vous n'avez pas la permission '{permission}' requise pour accéder à cette section"
                )
                logging.warning(f"Accès refusé à {user.nom_complet} pour {window_class_name} - Permission manquante: {permission}")
                return False
        
        return True
    
    def _check_roles(self, window_class_name, user):
        """Vérifie les rôles requis"""
        required_roles = self.required_roles.get(window_class_name, [])
        
        if required_roles and user.role not in required_roles:
            roles_str = ", ".join(required_roles)
            messagebox.showerror(
                "Accès refusé", 
                f"Cette section nécessite un des rôles suivants: {roles_str}"
            )
            logging.warning(f"Accès refusé à {user.nom_complet} pour {window_class_name} - Rôle insuffisant: {user.role}")
            return False
        
        return True
    
    def _log_access(self, window_class_name, user):
        """Enregistre l'accès dans les logs"""
        try:
            from models.audit_log import AuditLog
            
            audit_log = AuditLog()
            audit_log.utilisateur_id = user.id
            audit_log.action = f"ACCESS_{window_class_name.upper()}"
            audit_log.description = f"Accès à {window_class_name}"
            audit_log.date_action = datetime.now().isoformat()
            audit_log.adresse_ip = "127.0.0.1"
            audit_log.save()
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement de l'accès: {e}")

class SecureWindow:
    """Classe de base pour toutes les fenêtres sécurisées"""
    
    def __init__(self, parent, current_user, *args, **kwargs):
        self.parent = parent
        self.current_user = current_user
        
        # Vérifier l'accès avant d'initialiser la fenêtre
        if not self._check_access():
            return
        
        # Initialiser la fenêtre
        self._init_window(*args, **kwargs)
        
        # Démarrer la vérification périodique de session
        self._start_session_monitoring()
    
    def _check_access(self):
        """Vérifie l'accès à cette fenêtre"""
        middleware = SecurityMiddleware()
        window_class_name = self.__class__.__name__
        
        return middleware.check_access(window_class_name, self.current_user)
    
    def _init_window(self, *args, **kwargs):
        """À surcharger dans les classes filles"""
        pass
    
    def _start_session_monitoring(self):
        """Démarre la surveillance de session"""
        self._check_session()
        # Programmer la prochaine vérification dans 30 secondes
        if hasattr(self.parent, 'after'):
            self.parent.after(30000, self._start_session_monitoring)
    
    def _check_session(self):
        """Vérifie la validité de la session"""
        if not self.current_user or not hasattr(self.current_user, 'current_session'):
            self._handle_session_expired()
            return False
        
        session = session_manager.get_session(self.current_user.current_session.session_id)
        
        if not session:
            self._handle_session_expired()
            return False
        
        # Mettre à jour l'activité
        session.update_activity()
        self.current_user.last_activity = session.last_activity
        
        return True
    
    def _handle_session_expired(self):
        """Gère l'expiration de session"""
        messagebox.showwarning(
            "Session expirée", 
            "Votre session a expiré. La fenêtre va se fermer."
        )
        
        # Fermer la fenêtre
        if hasattr(self, 'destroy'):
            self.destroy()
        elif hasattr(self.parent, 'destroy'):
            self.parent.destroy()

def secure_window_decorator(window_class):
    """Décorateur pour sécuriser automatiquement une fenêtre"""
    
    class SecuredWindow(window_class):
        def __init__(self, parent, current_user, *args, **kwargs):
            # Vérifier l'accès
            middleware = SecurityMiddleware()
            if not middleware.check_access(window_class.__name__, current_user):
                return
            
            # Initialiser la fenêtre parente
            super().__init__(parent, current_user, *args, **kwargs)
            
            # Démarrer la surveillance
            self._start_session_monitoring()
        
        def _start_session_monitoring(self):
            """Démarre la surveillance de session"""
            self._check_session()
            # Programmer la prochaine vérification
            if hasattr(self, 'after'):
                self.after(30000, self._start_session_monitoring)
            elif hasattr(self.parent, 'after'):
                self.parent.after(30000, self._start_session_monitoring)
        
        def _check_session(self):
            """Vérifie la session"""
            if not current_user or not hasattr(current_user, 'current_session'):
                self._handle_session_expired()
                return False
            
            session = session_manager.get_session(current_user.current_session.session_id)
            
            if not session:
                self._handle_session_expired()
                return False
            
            session.update_activity()
            return True
        
        def _handle_session_expired(self):
            """Gère l'expiration"""
            messagebox.showwarning("Session expirée", "Votre session a expiré.")
            if hasattr(self, 'destroy'):
                self.destroy()
    
    return SecuredWindow

class OperationSecurity:
    """Sécurité au niveau des opérations"""
    
    @staticmethod
    def require_confirmation(operation_name, user, sensitive=False):
        """Demande une confirmation pour une opération"""
        if sensitive:
            # Pour les opérations sensibles, demander le mot de passe
            from tkinter import simpledialog
            
            password = simpledialog.askstring(
                "Confirmation requise",
                f"Veuillez saisir votre mot de passe pour confirmer l'opération:\n{operation_name}",
                show='*'
            )
            
            if not password:
                return False
            
            # Vérifier le mot de passe
            from models.utilisateur import Utilisateur
            if not Utilisateur.verify_password(user.nom_utilisateur, password):
                messagebox.showerror("Erreur", "Mot de passe incorrect")
                return False
        else:
            # Confirmation simple
            result = messagebox.askyesno(
                "Confirmation",
                f"Êtes-vous sûr de vouloir effectuer cette opération?\n{operation_name}"
            )
            
            if not result:
                return False
        
        # Enregistrer la confirmation
        try:
            from models.audit_log import AuditLog
            
            audit_log = AuditLog()
            audit_log.utilisateur_id = user.id
            audit_log.action = "OPERATION_CONFIRMED"
            audit_log.description = f"Confirmation d'opération: {operation_name}"
            audit_log.date_action = datetime.now().isoformat()
            audit_log.adresse_ip = "127.0.0.1"
            audit_log.save()
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement de la confirmation: {e}")
        
        return True
    
    @staticmethod
    def log_sensitive_operation(operation_name, user, details=""):
        """Enregistre une opération sensible"""
        try:
            from models.audit_log import AuditLog
            
            audit_log = AuditLog()
            audit_log.utilisateur_id = user.id
            audit_log.action = "SENSITIVE_OPERATION"
            audit_log.description = f"{operation_name}: {details}"
            audit_log.date_action = datetime.now().isoformat()
            audit_log.adresse_ip = "127.0.0.1"
            audit_log.save()
            
            logging.warning(f"Opération sensible effectuée par {user.nom_complet}: {operation_name}")
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement de l'opération sensible: {e}")

# Instance globale
security_middleware = SecurityMiddleware()
operation_security = OperationSecurity()
