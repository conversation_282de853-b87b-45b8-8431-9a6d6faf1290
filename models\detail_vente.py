"""
Modèle pour les détails de vente
"""
from models.base_model import BaseModel

class DetailVente(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "details_vente"
        self.vente_id = None
        self.produit_id = None
        self.quantite = 0
        self.prix_unitaire = 0.0
        self.sous_total = 0.0
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'vente_id': self.vente_id,
            'produit_id': self.produit_id,
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'sous_total': self.sous_total
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.vente_id = row[1]
        self.produit_id = row[2]
        self.quantite = row[3]
        self.prix_unitaire = float(row[4]) if row[4] else 0.0
        self.sous_total = float(row[5]) if row[5] else 0.0
    
    def get_produit_nom(self):
        """Retourne le nom du produit"""
        if self.produit_id:
            from models.produit import Produit
            produit = Produit.find_by_id(self.produit_id)
            return produit.nom if produit else "Produit inconnu"
        return "Produit inconnu"
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'vente_id': self.vente_id,
            'produit_id': self.produit_id,
            'produit_nom': self.get_produit_nom(),
            'quantite': self.quantite,
            'prix_unitaire': self.prix_unitaire,
            'sous_total': self.sous_total
        }
