"""
Modèle pour les catégories de produits
"""
from models.base_model import BaseModel

class Categorie(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "categories"
        self.nom = ""
        self.description = ""
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'nom': self.nom,
            'description': self.description
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.nom = row[1]
        self.description = row[2]
        self.created_at = row[3]
    
    def get_produits_count(self):
        """Retourne le nombre de produits dans cette catégorie"""
        query = "SELECT COUNT(*) FROM produits WHERE categorie_id = ? AND actif = 1"
        result = self.db.execute_query(query, (self.id,))
        return result[0][0] if result else 0
    
    def get_produits(self):
        """Retourne tous les produits de cette catégorie"""
        from models.produit import Produit
        return Produit.find_by_categorie(self.id)
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom': self.nom,
            'description': self.description,
            'produits_count': self.get_produits_count(),
            'created_at': self.created_at
        }
