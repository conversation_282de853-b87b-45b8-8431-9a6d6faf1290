"""
Dialogue pour la création/modification de clients à crédit
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.client_credit import ClientCredit

class ClientCreditDialog:
    def __init__(self, parent, title="Nouveau client à crédit", client=None):
        self.result = None
        self.client = client
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("450x350")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
        
        # Charger les données si modification
        if self.client:
            self.load_client_data()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.window.winfo_screenheight() // 2) - (350 // 2)
        self.window.geometry(f"450x350+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.nom_var = tk.StringVar()
        self.telephone_var = tk.StringVar()
        self.limite_credit_var = tk.StringVar(value="100.00")
        self.actif_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations du client", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Nom du client
        ttk.Label(info_frame, text="Nom du client *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(info_frame, textvariable=self.nom_var, width=30)
        nom_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        nom_entry.focus()
        
        # Téléphone
        ttk.Label(info_frame, text="Téléphone:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.telephone_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Configurer les colonnes
        info_frame.columnconfigure(1, weight=1)
        
        # Informations de crédit
        credit_frame = ttk.LabelFrame(main_frame, text="Paramètres de crédit", padding="10")
        credit_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Limite de crédit
        ttk.Label(credit_frame, text="Limite de crédit (€) *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        limite_entry = ttk.Entry(credit_frame, textvariable=self.limite_credit_var, width=15)
        limite_entry.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Informations sur le crédit actuel (si modification)
        if self.client:
            ttk.Label(credit_frame, text="Crédit utilisé:").grid(row=1, column=0, sticky=tk.W, pady=5)
            self.credit_utilise_label = ttk.Label(credit_frame, text=f"{self.client.credit_utilise:.2f} €", 
                                                 font=('Arial', 10, 'bold'))
            self.credit_utilise_label.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
            
            ttk.Label(credit_frame, text="Crédit disponible:").grid(row=2, column=0, sticky=tk.W, pady=5)
            credit_disponible = self.client.get_credit_disponible()
            self.credit_disponible_label = ttk.Label(credit_frame, text=f"{credit_disponible:.2f} €", 
                                                    font=('Arial', 10, 'bold'))
            self.credit_disponible_label.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))
            
            # Mettre à jour les couleurs
            self.update_credit_colors()
            
            # Bind pour recalculer quand la limite change
            limite_entry.bind('<KeyRelease>', self.on_limite_change)
        
        # Statut
        status_frame = ttk.LabelFrame(main_frame, text="Statut", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Checkbutton(status_frame, text="Client actif", variable=self.actif_var).pack(anchor=tk.W)
        
        # Avertissement si modification avec crédit utilisé
        if self.client and self.client.credit_utilise > 0:
            warning_frame = ttk.Frame(main_frame)
            warning_frame.pack(fill=tk.X, pady=(0, 15))
            
            warning_label = ttk.Label(warning_frame, 
                                    text="⚠️ Attention: Ce client a un crédit en cours d'utilisation",
                                    foreground="orange", font=('Arial', 9, 'bold'))
            warning_label.pack()
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Enregistrer", command=self.save).pack(side=tk.RIGHT)
        
        # Bind Enter key
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def load_client_data(self):
        """Charge les données du client pour modification"""
        if not self.client:
            return
        
        self.nom_var.set(self.client.nom)
        self.telephone_var.set(self.client.telephone or "")
        self.limite_credit_var.set(f"{self.client.limite_credit:.2f}")
        self.actif_var.set(self.client.actif)
    
    def on_limite_change(self, event):
        """Appelé quand la limite de crédit change"""
        if self.client:
            self.update_credit_colors()
    
    def update_credit_colors(self):
        """Met à jour les couleurs selon le crédit disponible"""
        if not self.client:
            return
        
        try:
            nouvelle_limite = float(self.limite_credit_var.get() or 0)
            credit_disponible = nouvelle_limite - self.client.credit_utilise
            
            # Mettre à jour le label
            self.credit_disponible_label.config(text=f"{credit_disponible:.2f} €")
            
            # Couleur selon le crédit disponible
            if credit_disponible < 0:
                self.credit_disponible_label.config(foreground="red")
                self.credit_utilise_label.config(foreground="red")
            elif credit_disponible < (nouvelle_limite * 0.2):  # Moins de 20%
                self.credit_disponible_label.config(foreground="orange")
                self.credit_utilise_label.config(foreground="orange")
            else:
                self.credit_disponible_label.config(foreground="green")
                self.credit_utilise_label.config(foreground="black")
                
        except ValueError:
            self.credit_disponible_label.config(text="N/A", foreground="black")
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Nom obligatoire
        nom = self.nom_var.get().strip()
        if not nom:
            errors.append("Le nom du client est obligatoire")
        elif len(nom) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")
        
        # Limite de crédit valide
        try:
            limite = float(self.limite_credit_var.get())
            if limite < 0:
                errors.append("La limite de crédit doit être positive ou nulle")
            elif limite > 10000:  # Limite arbitraire de sécurité
                errors.append("La limite de crédit ne peut pas dépasser 10 000 €")
        except ValueError:
            errors.append("La limite de crédit doit être un nombre valide")
        
        # Validation du téléphone si fourni
        telephone = self.telephone_var.get().strip()
        if telephone:
            import re
            # Supprimer les espaces et caractères spéciaux pour la validation
            tel_clean = re.sub(r'[^\d+]', '', telephone)
            if len(tel_clean) < 8:
                errors.append("Le numéro de téléphone doit contenir au moins 8 chiffres")
        
        # Vérifier que la nouvelle limite ne rend pas le crédit négatif
        if self.client:
            try:
                nouvelle_limite = float(self.limite_credit_var.get())
                if nouvelle_limite < self.client.credit_utilise:
                    errors.append(f"La limite ne peut pas être inférieure au crédit utilisé ({self.client.credit_utilise:.2f} €)")
            except ValueError:
                pass  # Déjà géré ci-dessus
        
        return errors
    
    def save(self):
        """Sauvegarde le client"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            # Créer ou modifier le client
            if self.client:
                client = self.client
            else:
                client = ClientCredit()
            
            # Remplir les données
            client.nom = self.nom_var.get().strip()
            client.telephone = self.telephone_var.get().strip() or None
            client.limite_credit = float(self.limite_credit_var.get())
            client.actif = self.actif_var.get()
            
            # Sauvegarder
            client.save()
            
            self.result = client.to_dict()
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
