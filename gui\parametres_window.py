"""
Fenêtre de paramètres de l'application
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.backup_manager import BackupManager
from utils.export_manager import ExportManager

class ParametresWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        # Vérifier les permissions
        if not self.current_user.can_manage_users():
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder aux paramètres")
            return
        
        self.backup_manager = BackupManager()
        self.export_manager = ExportManager()
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="Paramètres de l'Application", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Notebook pour les onglets
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Général
        general_frame = ttk.Frame(notebook)
        notebook.add(general_frame, text="Général")
        
        # Onglet Sauvegarde
        backup_frame = ttk.Frame(notebook)
        notebook.add(backup_frame, text="Sauvegarde")
        
        # Onglet Export
        export_frame = ttk.Frame(notebook)
        notebook.add(export_frame, text="Export")
        
        # Onglet Base de données
        database_frame = ttk.Frame(notebook)
        notebook.add(database_frame, text="Base de données")
        
        self.setup_general_tab(general_frame)
        self.setup_backup_tab(backup_frame)
        self.setup_export_tab(export_frame)
        self.setup_database_tab(database_frame)
    
    def setup_general_tab(self, parent):
        """Configure l'onglet général"""
        # Informations du bar
        info_frame = ttk.LabelFrame(parent, text="Informations du bar", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Variables
        self.nom_bar_var = tk.StringVar(value="Mon Bar")
        self.adresse_bar_var = tk.StringVar()
        self.telephone_bar_var = tk.StringVar()
        self.email_bar_var = tk.StringVar()
        
        # Champs
        ttk.Label(info_frame, text="Nom du bar:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.nom_bar_var, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        ttk.Label(info_frame, text="Adresse:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.adresse_bar_var, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        ttk.Label(info_frame, text="Téléphone:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.telephone_bar_var, width=40).grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        ttk.Label(info_frame, text="Email:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.email_bar_var, width=40).grid(row=3, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        info_frame.columnconfigure(1, weight=1)
        
        # Paramètres généraux
        params_frame = ttk.LabelFrame(parent, text="Paramètres généraux", padding="10")
        params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Variables
        self.tva_var = tk.StringVar(value="20.0")
        self.devise_var = tk.StringVar(value="EUR")
        self.alerte_stock_var = tk.BooleanVar(value=True)
        self.impression_auto_var = tk.BooleanVar(value=False)
        
        # Champs
        ttk.Label(params_frame, text="Taux de TVA (%):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(params_frame, textvariable=self.tva_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        ttk.Label(params_frame, text="Devise:").grid(row=1, column=0, sticky=tk.W, pady=5)
        devise_combo = ttk.Combobox(params_frame, textvariable=self.devise_var, 
                                   values=["EUR", "USD", "GBP", "CHF"], state="readonly", width=8)
        devise_combo.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        ttk.Checkbutton(params_frame, text="Alertes de stock activées", 
                       variable=self.alerte_stock_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        ttk.Checkbutton(params_frame, text="Impression automatique des tickets", 
                       variable=self.impression_auto_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Bouton de sauvegarde
        ttk.Button(parent, text="Sauvegarder les paramètres", 
                  command=self.save_general_settings).pack(pady=20)
    
    def setup_backup_tab(self, parent):
        """Configure l'onglet sauvegarde"""
        # Sauvegarde manuelle
        manual_frame = ttk.LabelFrame(parent, text="Sauvegarde manuelle", padding="10")
        manual_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(manual_frame, text="Créer une sauvegarde complète de l'application").pack(anchor=tk.W, pady=5)
        
        buttons_frame = ttk.Frame(manual_frame)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(buttons_frame, text="Créer une sauvegarde", 
                  command=self.create_backup).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="Restaurer une sauvegarde", 
                  command=self.restore_backup).pack(side=tk.LEFT)
        
        # Liste des sauvegardes
        backups_frame = ttk.LabelFrame(parent, text="Sauvegardes existantes", padding="10")
        backups_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour les sauvegardes
        columns = ("Nom", "Date", "Taille", "Description")
        self.backups_tree = ttk.Treeview(backups_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=120)
        
        backups_scrollbar = ttk.Scrollbar(backups_frame, orient=tk.VERTICAL, command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=backups_scrollbar.set)
        
        self.backups_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        backups_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons pour les sauvegardes
        backup_buttons_frame = ttk.Frame(backups_frame)
        backup_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(backup_buttons_frame, text="Actualiser", 
                  command=self.load_backups).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_buttons_frame, text="Supprimer", 
                  command=self.delete_backup).pack(side=tk.LEFT)
    
    def setup_export_tab(self, parent):
        """Configure l'onglet export"""
        # Export des données
        export_frame = ttk.LabelFrame(parent, text="Export des données", padding="10")
        export_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(export_frame, text="Exporter les données vers différents formats").pack(anchor=tk.W, pady=5)
        
        # Boutons d'export
        export_buttons_frame = ttk.Frame(export_frame)
        export_buttons_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(export_buttons_frame, text="Export SQL", 
                  command=self.export_sql).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(export_buttons_frame, text="Export CSV", 
                  command=self.export_csv).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(export_buttons_frame, text="Export Excel", 
                  command=self.export_excel).pack(side=tk.LEFT)
        
        # Paramètres d'export
        export_params_frame = ttk.LabelFrame(parent, text="Paramètres d'export", padding="10")
        export_params_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.export_produits_var = tk.BooleanVar(value=True)
        self.export_ventes_var = tk.BooleanVar(value=True)
        self.export_stock_var = tk.BooleanVar(value=True)
        self.export_clients_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(export_params_frame, text="Produits", 
                       variable=self.export_produits_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(export_params_frame, text="Ventes", 
                       variable=self.export_ventes_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(export_params_frame, text="Mouvements de stock", 
                       variable=self.export_stock_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(export_params_frame, text="Clients à crédit", 
                       variable=self.export_clients_var).pack(anchor=tk.W, pady=2)
    
    def setup_database_tab(self, parent):
        """Configure l'onglet base de données"""
        # Statistiques
        stats_frame = ttk.LabelFrame(parent, text="Statistiques de la base de données", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.stats_text = tk.Text(stats_frame, height=10, width=60, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # Maintenance
        maintenance_frame = ttk.LabelFrame(parent, text="Maintenance", padding="10")
        maintenance_frame.pack(fill=tk.X, padx=10, pady=10)
        
        maintenance_buttons_frame = ttk.Frame(maintenance_frame)
        maintenance_buttons_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(maintenance_buttons_frame, text="Vérifier l'intégrité", 
                  command=self.check_integrity).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(maintenance_buttons_frame, text="Optimiser la base", 
                  command=self.optimize_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(maintenance_buttons_frame, text="Actualiser les stats", 
                  command=self.load_database_stats).pack(side=tk.LEFT)
    
    def load_data(self):
        """Charge les données"""
        self.load_backups()
        self.load_database_stats()
    
    def load_backups(self):
        """Charge la liste des sauvegardes"""
        # Nettoyer le treeview
        for item in self.backups_tree.get_children():
            self.backups_tree.delete(item)
        
        try:
            backups = self.backup_manager.list_backups()
            
            for backup in backups:
                # Formater la taille
                size_mb = backup['size'] / (1024 * 1024)
                size_str = f"{size_mb:.1f} MB"
                
                # Formater la date
                from datetime import datetime
                date_obj = datetime.fromisoformat(backup['created_at'])
                date_str = date_obj.strftime("%d/%m/%Y %H:%M")
                
                self.backups_tree.insert("", "end", values=(
                    backup['filename'],
                    date_str,
                    size_str,
                    backup.get('description', 'Sauvegarde')
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des sauvegardes: {e}")
    
    def load_database_stats(self):
        """Charge les statistiques de la base de données"""
        try:
            stats = self.backup_manager.get_database_stats()
            
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            
            stats_text = "STATISTIQUES DE LA BASE DE DONNÉES\n"
            stats_text += "=" * 40 + "\n\n"
            
            # Taille de la base
            if 'database_size' in stats:
                size_mb = stats['database_size'] / (1024 * 1024)
                stats_text += f"Taille de la base de données: {size_mb:.2f} MB\n"
            
            # Date de dernière modification
            if 'last_modified' in stats:
                stats_text += f"Dernière modification: {stats['last_modified']}\n\n"
            
            # Nombre d'enregistrements par table
            stats_text += "NOMBRE D'ENREGISTREMENTS PAR TABLE:\n"
            stats_text += "-" * 40 + "\n"
            
            tables_info = [
                ('produits', 'Produits'),
                ('categories', 'Catégories'),
                ('fournisseurs', 'Fournisseurs'),
                ('utilisateurs', 'Utilisateurs'),
                ('ventes', 'Ventes'),
                ('details_vente', 'Détails de vente'),
                ('mouvements_stock', 'Mouvements de stock'),
                ('clients_credit', 'Clients à crédit'),
                ('approvisionnements', 'Approvisionnements'),
                ('details_approvisionnement', 'Détails d\'approvisionnement')
            ]
            
            for table, label in tables_info:
                count = stats.get(table, 0)
                stats_text += f"{label}: {count}\n"
            
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des statistiques: {e}")
    
    def save_general_settings(self):
        """Sauvegarde les paramètres généraux"""
        try:
            # Ici, on sauvegarderait normalement dans une table de paramètres
            # Pour l'instant, on affiche juste un message de confirmation
            messagebox.showinfo("Succès", "Paramètres sauvegardés avec succès")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def create_backup(self):
        """Crée une nouvelle sauvegarde"""
        try:
            backup_path = self.backup_manager.create_backup()
            self.load_backups()
            messagebox.showinfo("Succès", f"Sauvegarde créée avec succès:\n{backup_path}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création de la sauvegarde: {e}")
    
    def restore_backup(self):
        """Restaure une sauvegarde"""
        backup_file = filedialog.askopenfilename(
            title="Sélectionner une sauvegarde",
            filetypes=[("Fichiers ZIP", "*.zip"), ("Tous les fichiers", "*.*")]
        )
        
        if backup_file:
            if messagebox.askyesno("Confirmation", 
                                  "Êtes-vous sûr de vouloir restaurer cette sauvegarde ?\n"
                                  "Cette action remplacera toutes les données actuelles."):
                try:
                    self.backup_manager.restore_backup(backup_file)
                    messagebox.showinfo("Succès", "Sauvegarde restaurée avec succès")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de la restauration: {e}")
    
    def delete_backup(self):
        """Supprime une sauvegarde sélectionnée"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner une sauvegarde à supprimer")
            return
        
        item = self.backups_tree.item(selection[0])
        filename = item['values'][0]
        
        if messagebox.askyesno("Confirmation", f"Supprimer la sauvegarde '{filename}' ?"):
            try:
                backup_path = os.path.join(self.backup_manager.backup_dir, filename)
                os.remove(backup_path)
                self.load_backups()
                messagebox.showinfo("Succès", "Sauvegarde supprimée")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {e}")
    
    def export_sql(self):
        """Exporte vers SQL"""
        try:
            sql_file = self.backup_manager.export_data_to_sql()
            messagebox.showinfo("Succès", f"Export SQL créé:\n{sql_file}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export SQL: {e}")
    
    def export_csv(self):
        """Exporte vers CSV"""
        messagebox.showinfo("Info", "Fonctionnalité d'export CSV à implémenter")
    
    def export_excel(self):
        """Exporte vers Excel"""
        messagebox.showinfo("Info", "Fonctionnalité d'export Excel à implémenter")
    
    def check_integrity(self):
        """Vérifie l'intégrité de la base de données"""
        try:
            is_ok, message = self.backup_manager.verify_database_integrity()
            if is_ok:
                messagebox.showinfo("Intégrité", message)
            else:
                messagebox.showwarning("Intégrité", message)
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la vérification: {e}")
    
    def optimize_database(self):
        """Optimise la base de données"""
        try:
            # Ici on pourrait exécuter VACUUM sur SQLite
            import sqlite3
            conn = sqlite3.connect(self.backup_manager.db_path)
            conn.execute("VACUUM")
            conn.close()
            messagebox.showinfo("Succès", "Base de données optimisée")
            self.load_database_stats()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'optimisation: {e}")
