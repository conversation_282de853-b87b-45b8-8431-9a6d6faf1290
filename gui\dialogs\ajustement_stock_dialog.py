"""
Dialogue pour l'ajustement de stock
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.produit import Produit

class AjustementStockDialog:
    def __init__(self, parent, produit, current_user):
        self.result = None
        self.produit = produit
        self.current_user = current_user
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(f"Ajuster le stock - {produit.nom}")
        self.window.geometry("450x400")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.window.winfo_screenheight() // 2) - (400 // 2)
        self.window.geometry(f"450x400+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.type_mouvement_var = tk.StringVar(value="entree")
        self.quantite_var = tk.StringVar(value="0")
        self.prix_unitaire_var = tk.StringVar(value=f"{self.produit.prix_achat:.2f}")
        self.reference_var = tk.StringVar()
        self.commentaire_var = tk.StringVar()
        self.nouveau_stock_var = tk.StringVar()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations du produit
        info_frame = ttk.LabelFrame(main_frame, text="Produit", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(info_frame, text="Nom:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(info_frame, text=self.produit.nom, font=('Arial', 10, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_frame, text="Stock actuel:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.stock_actuel_label = ttk.Label(info_frame, text=f"{self.produit.stock_actuel} {self.produit.unite}", 
                                           font=('Arial', 10, 'bold'), foreground="blue")
        self.stock_actuel_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_frame, text="Seuil minimum:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Label(info_frame, text=f"{self.produit.seuil_minimum} {self.produit.unite}").grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Type de mouvement
        mouvement_frame = ttk.LabelFrame(main_frame, text="Type de mouvement", padding="10")
        mouvement_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Radiobutton(mouvement_frame, text="📥 Entrée de stock (réception, retour)", 
                       variable=self.type_mouvement_var, value="entree",
                       command=self.on_type_change).pack(anchor=tk.W, pady=2)
        
        ttk.Radiobutton(mouvement_frame, text="📤 Sortie de stock (perte, casse, don)", 
                       variable=self.type_mouvement_var, value="sortie",
                       command=self.on_type_change).pack(anchor=tk.W, pady=2)
        
        ttk.Radiobutton(mouvement_frame, text="📊 Inventaire (correction de stock)", 
                       variable=self.type_mouvement_var, value="inventaire",
                       command=self.on_type_change).pack(anchor=tk.W, pady=2)
        
        # Détails du mouvement
        details_frame = ttk.LabelFrame(main_frame, text="Détails", padding="10")
        details_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Quantité
        ttk.Label(details_frame, text="Quantité *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        quantite_entry = ttk.Entry(details_frame, textvariable=self.quantite_var, width=15)
        quantite_entry.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        quantite_entry.bind('<KeyRelease>', self.calculer_nouveau_stock)
        quantite_entry.focus()
        
        self.quantite_label = ttk.Label(details_frame, text="unités")
        self.quantite_label.grid(row=0, column=2, sticky=tk.W, pady=5, padx=(5, 0))
        
        # Prix unitaire
        ttk.Label(details_frame, text="Prix unitaire (€):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(details_frame, textvariable=self.prix_unitaire_var, width=15).grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Référence
        ttk.Label(details_frame, text="Référence:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(details_frame, textvariable=self.reference_var, width=25).grid(row=2, column=1, columnspan=2, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Commentaire
        ttk.Label(details_frame, text="Commentaire:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=5)
        commentaire_text = tk.Text(details_frame, height=3, width=25)
        commentaire_text.grid(row=3, column=1, columnspan=2, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        self.commentaire_text = commentaire_text
        
        # Configurer les colonnes
        details_frame.columnconfigure(1, weight=1)
        
        # Résultat
        result_frame = ttk.LabelFrame(main_frame, text="Résultat", padding="10")
        result_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(result_frame, text="Nouveau stock:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.nouveau_stock_label = ttk.Label(result_frame, text="", font=('Arial', 12, 'bold'), foreground="green")
        self.nouveau_stock_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Appliquer", command=self.apply).pack(side=tk.RIGHT)
        
        # Initialiser l'affichage
        self.on_type_change()
        self.calculer_nouveau_stock()
    
    def on_type_change(self):
        """Appelé quand le type de mouvement change"""
        type_mouvement = self.type_mouvement_var.get()
        
        if type_mouvement == "entree":
            self.quantite_label.config(text="unités à ajouter")
            self.prix_unitaire_var.set(f"{self.produit.prix_achat:.2f}")
        elif type_mouvement == "sortie":
            self.quantite_label.config(text="unités à retirer")
            self.prix_unitaire_var.set(f"{self.produit.prix_vente:.2f}")
        else:  # inventaire
            self.quantite_label.config(text="nouveau stock total")
            self.prix_unitaire_var.set(f"{self.produit.prix_achat:.2f}")
        
        self.calculer_nouveau_stock()
    
    def calculer_nouveau_stock(self, event=None):
        """Calcule et affiche le nouveau stock"""
        try:
            quantite = int(self.quantite_var.get() or 0)
            type_mouvement = self.type_mouvement_var.get()
            stock_actuel = self.produit.stock_actuel
            
            if type_mouvement == "entree":
                nouveau_stock = stock_actuel + quantite
            elif type_mouvement == "sortie":
                nouveau_stock = stock_actuel - quantite
            else:  # inventaire
                nouveau_stock = quantite
            
            # Afficher le nouveau stock
            self.nouveau_stock_label.config(text=f"{nouveau_stock} {self.produit.unite}")
            
            # Couleur selon le résultat
            if nouveau_stock < 0:
                self.nouveau_stock_label.config(foreground="red")
            elif nouveau_stock <= self.produit.seuil_minimum:
                self.nouveau_stock_label.config(foreground="orange")
            else:
                self.nouveau_stock_label.config(foreground="green")
                
        except ValueError:
            self.nouveau_stock_label.config(text="Quantité invalide", foreground="red")
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Quantité valide
        try:
            quantite = int(self.quantite_var.get())
            if quantite <= 0:
                errors.append("La quantité doit être positive")
        except ValueError:
            errors.append("La quantité doit être un nombre entier valide")
            return errors
        
        # Vérifier que le stock ne devient pas négatif
        type_mouvement = self.type_mouvement_var.get()
        if type_mouvement == "sortie" and quantite > self.produit.stock_actuel:
            errors.append("Impossible de retirer plus que le stock disponible")
        
        # Prix unitaire valide
        try:
            prix = float(self.prix_unitaire_var.get() or 0)
            if prix < 0:
                errors.append("Le prix unitaire doit être positif ou nul")
        except ValueError:
            errors.append("Le prix unitaire doit être un nombre valide")
        
        return errors
    
    def apply(self):
        """Applique l'ajustement de stock"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            quantite = int(self.quantite_var.get())
            type_mouvement = self.type_mouvement_var.get()
            prix_unitaire = float(self.prix_unitaire_var.get() or 0)
            reference = self.reference_var.get().strip() or f"Ajustement {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            commentaire = self.commentaire_text.get(1.0, tk.END).strip()
            
            # Appliquer l'ajustement
            self.produit.ajuster_stock(
                quantite=quantite,
                type_mouvement=type_mouvement,
                utilisateur_id=self.current_user.id,
                reference=reference,
                commentaire=commentaire
            )
            
            self.result = True
            messagebox.showinfo("Succès", "Ajustement de stock effectué avec succès")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajustement: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
