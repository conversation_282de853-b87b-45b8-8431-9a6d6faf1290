"""
Gestionnaire de sauvegarde et de restauration
"""
import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
import logging
import json

class BackupManager:
    def __init__(self, db_path="bar_stock.db", backup_dir="backups"):
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """S'assure que le répertoire de sauvegarde existe"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, include_logs=True):
        """Crée une sauvegarde complète"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Sauvegarder la base de données
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "database.db")
                
                # Sauvegarder les logs si demandé
                if include_logs and os.path.exists("bar_stock.log"):
                    zipf.write("bar_stock.log", "logs.txt")
                
                # Sauvegarder la configuration
                if os.path.exists("config.py"):
                    zipf.write("config.py", "config.py")
                
                # Créer un fichier d'informations sur la sauvegarde
                backup_info = {
                    "created_at": datetime.now().isoformat(),
                    "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    "version": "1.0",
                    "description": "Sauvegarde automatique de l'application de gestion de stock"
                }
                
                # Ajouter les informations au zip
                zipf.writestr("backup_info.json", json.dumps(backup_info, indent=2))
            
            # Nettoyer les anciennes sauvegardes
            self.cleanup_old_backups()
            
            logging.info(f"Sauvegarde créée: {backup_path}")
            return backup_path
            
        except Exception as e:
            logging.error(f"Erreur lors de la création de la sauvegarde: {e}")
            raise
    
    def restore_backup(self, backup_path):
        """Restaure une sauvegarde"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"Fichier de sauvegarde introuvable: {backup_path}")
            
            # Créer une sauvegarde de sécurité avant la restauration
            security_backup = self.create_backup()
            logging.info(f"Sauvegarde de sécurité créée: {security_backup}")
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Extraire la base de données
                if "database.db" in zipf.namelist():
                    zipf.extract("database.db", ".")
                    if os.path.exists(self.db_path):
                        os.remove(self.db_path)
                    os.rename("database.db", self.db_path)
                
                # Extraire les logs si présents
                if "logs.txt" in zipf.namelist():
                    zipf.extract("logs.txt", ".")
                    if os.path.exists("bar_stock.log"):
                        os.remove("bar_stock.log")
                    os.rename("logs.txt", "bar_stock.log")
            
            logging.info(f"Sauvegarde restaurée: {backup_path}")
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de la restauration: {e}")
            raise
    
    def list_backups(self):
        """Liste toutes les sauvegardes disponibles"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.startswith("backup_") and filename.endswith(".zip"):
                backup_path = os.path.join(self.backup_dir, filename)
                
                try:
                    # Lire les informations de la sauvegarde
                    with zipfile.ZipFile(backup_path, 'r') as zipf:
                        if "backup_info.json" in zipf.namelist():
                            info_data = zipf.read("backup_info.json")
                            backup_info = json.loads(info_data.decode())
                        else:
                            # Informations par défaut si pas de fichier info
                            backup_info = {
                                "created_at": datetime.fromtimestamp(os.path.getctime(backup_path)).isoformat(),
                                "description": "Sauvegarde"
                            }
                    
                    backup_info["filename"] = filename
                    backup_info["path"] = backup_path
                    backup_info["size"] = os.path.getsize(backup_path)
                    backups.append(backup_info)
                    
                except Exception as e:
                    logging.warning(f"Impossible de lire la sauvegarde {filename}: {e}")
        
        # Trier par date de création (plus récent en premier)
        backups.sort(key=lambda x: x["created_at"], reverse=True)
        return backups
    
    def cleanup_old_backups(self, max_backups=30):
        """Supprime les anciennes sauvegardes"""
        try:
            backups = self.list_backups()
            
            if len(backups) > max_backups:
                # Supprimer les plus anciennes
                for backup in backups[max_backups:]:
                    os.remove(backup["path"])
                    logging.info(f"Ancienne sauvegarde supprimée: {backup['filename']}")
                    
        except Exception as e:
            logging.warning(f"Erreur lors du nettoyage des sauvegardes: {e}")
    
    def export_data_to_sql(self, output_path=None):
        """Exporte les données vers un fichier SQL"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"export_sql_{timestamp}.sql"
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                # En-tête
                f.write("-- Export de la base de données de gestion de stock\n")
                f.write(f"-- Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Exporter le schéma et les données
                for line in conn.iterdump():
                    f.write(f"{line}\n")
            
            conn.close()
            logging.info(f"Export SQL créé: {output_path}")
            return output_path
            
        except Exception as e:
            logging.error(f"Erreur lors de l'export SQL: {e}")
            raise
    
    def import_data_from_sql(self, sql_path):
        """Importe les données depuis un fichier SQL"""
        try:
            if not os.path.exists(sql_path):
                raise FileNotFoundError(f"Fichier SQL introuvable: {sql_path}")
            
            # Créer une sauvegarde de sécurité
            security_backup = self.create_backup()
            logging.info(f"Sauvegarde de sécurité créée: {security_backup}")
            
            # Lire et exécuter le fichier SQL
            conn = sqlite3.connect(self.db_path)
            
            with open(sql_path, 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            conn.executescript(sql_script)
            conn.close()
            
            logging.info(f"Import SQL terminé: {sql_path}")
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de l'import SQL: {e}")
            raise
    
    def get_database_stats(self):
        """Retourne les statistiques de la base de données"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            stats = {}
            
            # Tables principales
            tables = [
                'produits', 'categories', 'fournisseurs', 'utilisateurs',
                'ventes', 'details_vente', 'mouvements_stock', 'clients_credit',
                'approvisionnements', 'details_approvisionnement'
            ]
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    stats[table] = count
                except sqlite3.OperationalError:
                    stats[table] = 0
            
            # Taille de la base de données
            stats['database_size'] = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            # Date de dernière modification
            if os.path.exists(self.db_path):
                stats['last_modified'] = datetime.fromtimestamp(os.path.getmtime(self.db_path)).isoformat()
            
            conn.close()
            return stats
            
        except Exception as e:
            logging.error(f"Erreur lors de la récupération des statistiques: {e}")
            return {}
    
    def verify_database_integrity(self):
        """Vérifie l'intégrité de la base de données"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Vérification de l'intégrité SQLite
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            conn.close()
            
            if result and result[0] == "ok":
                logging.info("Vérification d'intégrité: OK")
                return True, "Base de données intègre"
            else:
                logging.warning(f"Problème d'intégrité détecté: {result}")
                return False, f"Problème détecté: {result}"
                
        except Exception as e:
            logging.error(f"Erreur lors de la vérification d'intégrité: {e}")
            return False, f"Erreur: {e}"
    
    def schedule_automatic_backup(self, interval_hours=24):
        """Programme une sauvegarde automatique (à implémenter avec un scheduler)"""
        # Cette méthode pourrait être étendue pour utiliser un scheduler
        # comme APScheduler pour des sauvegardes automatiques
        pass
