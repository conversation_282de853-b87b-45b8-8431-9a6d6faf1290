"""
Décorateurs de sécurité pour l'application
"""
import functools
import logging
from datetime import datetime
from tkinter import messagebox

def require_authentication(func):
    """Décorateur qui nécessite une authentification"""
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        if not hasattr(self, 'current_user') or not self.current_user:
            messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
            if hasattr(self, 'show_login'):
                self.show_login()
            return None
        return func(self, *args, **kwargs)
    return wrapper

def require_permission(permission):
    """Décorateur qui nécessite une permission spécifique"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            if not self.current_user.has_permission(permission):
                messagebox.showerror("Accès refusé", f"Vous n'avez pas la permission '{permission}' pour effectuer cette action")
                logging.warning(f"Tentative d'accès non autorisé par {self.current_user.nom_complet} à la permission '{permission}'")
                return None
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def require_role(required_role):
    """Décorateur qui nécessite un rôle spécifique"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            if self.current_user.role != required_role:
                messagebox.showerror("Accès refusé", f"Seuls les {required_role}s peuvent effectuer cette action")
                logging.warning(f"Tentative d'accès non autorisé par {self.current_user.nom_complet} (rôle: {self.current_user.role}) à une fonction nécessitant le rôle '{required_role}'")
                return None
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def require_roles(allowed_roles):
    """Décorateur qui nécessite un des rôles spécifiés"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            if self.current_user.role not in allowed_roles:
                roles_str = ", ".join(allowed_roles)
                messagebox.showerror("Accès refusé", f"Cette action nécessite un des rôles suivants: {roles_str}")
                logging.warning(f"Tentative d'accès non autorisé par {self.current_user.nom_complet} (rôle: {self.current_user.role}) à une fonction nécessitant les rôles: {roles_str}")
                return None
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def audit_action(action_type, description=""):
    """Décorateur qui enregistre l'action dans les logs d'audit"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # Vérifier l'authentification
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            # Enregistrer l'action avant exécution
            try:
                from models.audit_log import AuditLog
                audit_log = AuditLog()
                audit_log.utilisateur_id = self.current_user.id
                audit_log.action = action_type
                audit_log.description = description or f"Exécution de {func.__name__}"
                audit_log.date_action = datetime.now().isoformat()
                audit_log.adresse_ip = "127.0.0.1"  # À améliorer pour récupérer la vraie IP
                audit_log.save()
            except Exception as e:
                logging.error(f"Erreur lors de l'enregistrement de l'audit: {e}")
            
            # Exécuter la fonction
            try:
                result = func(self, *args, **kwargs)
                
                # Enregistrer le succès
                try:
                    audit_log = AuditLog()
                    audit_log.utilisateur_id = self.current_user.id
                    audit_log.action = f"{action_type}_SUCCESS"
                    audit_log.description = f"Succès: {description or func.__name__}"
                    audit_log.date_action = datetime.now().isoformat()
                    audit_log.adresse_ip = "127.0.0.1"
                    audit_log.save()
                except Exception as e:
                    logging.error(f"Erreur lors de l'enregistrement du succès: {e}")
                
                return result
                
            except Exception as e:
                # Enregistrer l'échec
                try:
                    audit_log = AuditLog()
                    audit_log.utilisateur_id = self.current_user.id
                    audit_log.action = f"{action_type}_FAILURE"
                    audit_log.description = f"Échec: {str(e)}"
                    audit_log.date_action = datetime.now().isoformat()
                    audit_log.adresse_ip = "127.0.0.1"
                    audit_log.save()
                except Exception as audit_error:
                    logging.error(f"Erreur lors de l'enregistrement de l'échec: {audit_error}")
                
                raise e
        
        return wrapper
    return decorator

def session_timeout_check(timeout_minutes=30):
    """Décorateur qui vérifie l'expiration de session"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            # Vérifier l'expiration de session
            if hasattr(self.current_user, 'last_activity'):
                from datetime import datetime, timedelta
                if datetime.now() - self.current_user.last_activity > timedelta(minutes=timeout_minutes):
                    messagebox.showwarning("Session expirée", "Votre session a expiré. Veuillez vous reconnecter.")
                    self.logout()
                    return None
            
            # Mettre à jour l'activité
            self.current_user.last_activity = datetime.now()
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def rate_limit(max_calls=10, window_minutes=1):
    """Décorateur qui limite le nombre d'appels par fenêtre de temps"""
    call_history = {}
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            user_id = self.current_user.id
            current_time = datetime.now()
            
            # Nettoyer l'historique ancien
            if user_id in call_history:
                call_history[user_id] = [
                    call_time for call_time in call_history[user_id]
                    if current_time - call_time < timedelta(minutes=window_minutes)
                ]
            else:
                call_history[user_id] = []
            
            # Vérifier la limite
            if len(call_history[user_id]) >= max_calls:
                messagebox.showerror("Limite atteinte", f"Trop d'appels. Limite: {max_calls} par {window_minutes} minute(s)")
                logging.warning(f"Limite de taux atteinte pour {self.current_user.nom_complet}")
                return None
            
            # Enregistrer l'appel
            call_history[user_id].append(current_time)
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def secure_operation(permission=None, role=None, audit_type=None, rate_limit_calls=None):
    """Décorateur combiné pour sécuriser complètement une opération"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # Vérification d'authentification
            if not hasattr(self, 'current_user') or not self.current_user:
                messagebox.showerror("Accès refusé", "Vous devez être connecté pour effectuer cette action")
                if hasattr(self, 'show_login'):
                    self.show_login()
                return None
            
            # Vérification de permission
            if permission and not self.current_user.has_permission(permission):
                messagebox.showerror("Accès refusé", f"Permission '{permission}' requise")
                logging.warning(f"Accès refusé à {self.current_user.nom_complet} pour la permission '{permission}'")
                return None
            
            # Vérification de rôle
            if role and self.current_user.role != role:
                messagebox.showerror("Accès refusé", f"Rôle '{role}' requis")
                logging.warning(f"Accès refusé à {self.current_user.nom_complet} pour le rôle '{role}'")
                return None
            
            # Limitation de taux
            if rate_limit_calls:
                # Implémentation simplifiée de rate limiting
                pass
            
            # Audit
            if audit_type:
                try:
                    from models.audit_log import AuditLog
                    audit_log = AuditLog()
                    audit_log.utilisateur_id = self.current_user.id
                    audit_log.action = audit_type
                    audit_log.description = f"Exécution de {func.__name__}"
                    audit_log.date_action = datetime.now().isoformat()
                    audit_log.adresse_ip = "127.0.0.1"
                    audit_log.save()
                except Exception as e:
                    logging.error(f"Erreur d'audit: {e}")
            
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

class SecurityManager:
    """Gestionnaire de sécurité centralisé"""
    
    def __init__(self):
        self.failed_login_attempts = {}
        self.blocked_users = set()
        self.max_failed_attempts = 5
        self.block_duration_minutes = 15
    
    def check_user_blocked(self, username):
        """Vérifie si un utilisateur est bloqué"""
        return username in self.blocked_users
    
    def record_failed_login(self, username):
        """Enregistre une tentative de connexion échouée"""
        if username not in self.failed_login_attempts:
            self.failed_login_attempts[username] = []
        
        self.failed_login_attempts[username].append(datetime.now())
        
        # Nettoyer les anciennes tentatives
        cutoff_time = datetime.now() - timedelta(minutes=self.block_duration_minutes)
        self.failed_login_attempts[username] = [
            attempt for attempt in self.failed_login_attempts[username]
            if attempt > cutoff_time
        ]
        
        # Bloquer si trop de tentatives
        if len(self.failed_login_attempts[username]) >= self.max_failed_attempts:
            self.blocked_users.add(username)
            logging.warning(f"Utilisateur {username} bloqué pour trop de tentatives de connexion")
    
    def record_successful_login(self, username):
        """Enregistre une connexion réussie"""
        if username in self.failed_login_attempts:
            del self.failed_login_attempts[username]
        if username in self.blocked_users:
            self.blocked_users.remove(username)
    
    def validate_password_strength(self, password):
        """Valide la force d'un mot de passe"""
        if len(password) < 8:
            return False, "Le mot de passe doit contenir au moins 8 caractères"
        
        if not any(c.isupper() for c in password):
            return False, "Le mot de passe doit contenir au moins une majuscule"
        
        if not any(c.islower() for c in password):
            return False, "Le mot de passe doit contenir au moins une minuscule"
        
        if not any(c.isdigit() for c in password):
            return False, "Le mot de passe doit contenir au moins un chiffre"
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return False, "Le mot de passe doit contenir au moins un caractère spécial"
        
        return True, "Mot de passe valide"

# Instance globale
security_manager = SecurityManager()
