"""
Gestionnaire de chiffrement pour les données sensibles
"""
import os
import base64
import json
from typing import Union, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import secrets

class EncryptionManager:
    """Gestionnaire de chiffrement pour protéger les données sensibles"""
    
    def __init__(self, master_password: Optional[str] = None):
        self.master_password = master_password
        self._fernet = None
        self.key_file = os.path.join(os.path.dirname(__file__), 'encryption.key')
        
        # Initialiser le chiffrement
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialise le système de chiffrement"""
        try:
            if os.path.exists(self.key_file):
                # Charger la clé existante
                self._load_key()
            else:
                # Générer une nouvelle clé
                self._generate_key()
        except Exception as e:
            print(f"Erreur lors de l'initialisation du chiffrement: {e}")
            # Fallback: utiliser une clé par défaut (moins sécurisé)
            self._use_default_key()
    
    def _generate_key(self):
        """Génère une nouvelle clé de chiffrement"""
        try:
            if self.master_password:
                # Utiliser le mot de passe maître pour dériver la clé
                password = self.master_password.encode()
                salt = os.urandom(16)
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
                
                # Sauvegarder le salt avec la clé
                key_data = {
                    'key': key.decode(),
                    'salt': base64.b64encode(salt).decode(),
                    'method': 'pbkdf2'
                }
            else:
                # Générer une clé aléatoire
                key = Fernet.generate_key()
                key_data = {
                    'key': key.decode(),
                    'method': 'random'
                }
            
            # Sauvegarder la clé de manière sécurisée
            with open(self.key_file, 'w') as f:
                json.dump(key_data, f)
            
            # Protéger le fichier de clé (permissions restrictives)
            os.chmod(self.key_file, 0o600)
            
            self._fernet = Fernet(key if isinstance(key, bytes) else key.encode())
            
        except Exception as e:
            print(f"Erreur lors de la génération de la clé: {e}")
            self._use_default_key()
    
    def _load_key(self):
        """Charge la clé de chiffrement existante"""
        try:
            with open(self.key_file, 'r') as f:
                key_data = json.load(f)
            
            if key_data['method'] == 'pbkdf2':
                if not self.master_password:
                    raise ValueError("Mot de passe maître requis pour déchiffrer la clé")
                
                # Reconstruire la clé à partir du mot de passe maître
                password = self.master_password.encode()
                salt = base64.b64decode(key_data['salt'])
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
            else:
                # Clé aléatoire
                key = key_data['key'].encode()
            
            self._fernet = Fernet(key)
            
        except Exception as e:
            print(f"Erreur lors du chargement de la clé: {e}")
            self._use_default_key()
    
    def _use_default_key(self):
        """Utilise une clé par défaut (moins sécurisé)"""
        # Clé par défaut basée sur un identifiant de machine
        import platform
        machine_id = platform.node() + platform.system()
        
        # Dériver une clé à partir de l'identifiant de machine
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'bar_management_salt',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_id.encode()))
        self._fernet = Fernet(key)
    
    def encrypt(self, data: Union[str, bytes, dict]) -> str:
        """Chiffre des données"""
        try:
            if self._fernet is None:
                raise ValueError("Système de chiffrement non initialisé")
            
            # Convertir en bytes si nécessaire
            if isinstance(data, dict):
                data = json.dumps(data)
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Chiffrer
            encrypted_data = self._fernet.encrypt(data)
            
            # Retourner en base64 pour stockage
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            print(f"Erreur lors du chiffrement: {e}")
            # En cas d'erreur, retourner les données non chiffrées (avec marqueur)
            if isinstance(data, bytes):
                data = data.decode('utf-8')
            elif isinstance(data, dict):
                data = json.dumps(data)
            return f"UNENCRYPTED:{data}"
    
    def decrypt(self, encrypted_data: str) -> str:
        """Déchiffre des données"""
        try:
            if self._fernet is None:
                raise ValueError("Système de chiffrement non initialisé")
            
            # Vérifier si les données sont marquées comme non chiffrées
            if encrypted_data.startswith("UNENCRYPTED:"):
                return encrypted_data[12:]  # Retirer le préfixe
            
            # Décoder depuis base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Déchiffrer
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            print(f"Erreur lors du déchiffrement: {e}")
            # En cas d'erreur, retourner les données telles quelles
            return encrypted_data
    
    def encrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """Chiffre un fichier"""
        try:
            if output_path is None:
                output_path = file_path + '.encrypted'
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = self._fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Erreur lors du chiffrement du fichier: {e}")
    
    def decrypt_file(self, encrypted_file_path: str, output_path: Optional[str] = None) -> str:
        """Déchiffre un fichier"""
        try:
            if output_path is None:
                output_path = encrypted_file_path.replace('.encrypted', '')
            
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Erreur lors du déchiffrement du fichier: {e}")
    
    def hash_sensitive_data(self, data: str, salt: Optional[bytes] = None) -> tuple:
        """Hash des données sensibles avec salt"""
        try:
            if salt is None:
                salt = os.urandom(32)
            
            # Utiliser PBKDF2 pour le hachage
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            hash_value = kdf.derive(data.encode('utf-8'))
            
            return base64.b64encode(hash_value).decode('utf-8'), base64.b64encode(salt).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Erreur lors du hachage: {e}")
    
    def verify_hash(self, data: str, hash_value: str, salt: str) -> bool:
        """Vérifie un hash"""
        try:
            salt_bytes = base64.b64decode(salt)
            expected_hash, _ = self.hash_sensitive_data(data, salt_bytes)
            return expected_hash == hash_value
            
        except Exception as e:
            print(f"Erreur lors de la vérification du hash: {e}")
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Génère un token sécurisé"""
        return secrets.token_urlsafe(length)
    
    def is_encryption_available(self) -> bool:
        """Vérifie si le chiffrement est disponible"""
        return self._fernet is not None
    
    def rotate_key(self, new_master_password: Optional[str] = None):
        """Effectue une rotation de la clé de chiffrement"""
        try:
            # Sauvegarder l'ancienne clé
            old_fernet = self._fernet
            
            # Générer une nouvelle clé
            old_master_password = self.master_password
            self.master_password = new_master_password
            
            # Supprimer l'ancien fichier de clé
            if os.path.exists(self.key_file):
                os.remove(self.key_file)
            
            # Générer la nouvelle clé
            self._generate_key()
            
            return old_fernet, self._fernet
            
        except Exception as e:
            # Restaurer l'ancienne configuration en cas d'erreur
            self.master_password = old_master_password
            self._fernet = old_fernet
            raise Exception(f"Erreur lors de la rotation de clé: {e}")
    
    def secure_delete_file(self, file_path: str):
        """Suppression sécurisée d'un fichier"""
        try:
            if os.path.exists(file_path):
                # Écraser le fichier avec des données aléatoires
                file_size = os.path.getsize(file_path)
                
                with open(file_path, 'r+b') as f:
                    for _ in range(3):  # 3 passes d'écrasement
                        f.seek(0)
                        f.write(os.urandom(file_size))
                        f.flush()
                        os.fsync(f.fileno())
                
                # Supprimer le fichier
                os.remove(file_path)
                
        except Exception as e:
            print(f"Erreur lors de la suppression sécurisée: {e}")
            # Fallback: suppression normale
            try:
                os.remove(file_path)
            except:
                pass

# Instance globale du gestionnaire de chiffrement
try:
    encryption_manager = EncryptionManager()
    ENCRYPTION_AVAILABLE = True
except ImportError:
    encryption_manager = None
    ENCRYPTION_AVAILABLE = False
except Exception as e:
    print(f"Erreur lors de l'initialisation du chiffrement: {e}")
    encryption_manager = None
    ENCRYPTION_AVAILABLE = False
