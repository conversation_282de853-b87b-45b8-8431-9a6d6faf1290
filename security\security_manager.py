"""
Gestionnaire de sécurité avancé pour l'application
"""
import hashlib
import secrets
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import bcrypt
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.audit_log import AuditLog

class SecurityManager:
    """Gestionnaire de sécurité centralisé"""
    
    def __init__(self):
        self.failed_attempts = {}  # IP/User -> {count, last_attempt, locked_until}
        self.active_sessions = {}  # session_id -> {user_id, created_at, last_activity, ip}
        self.security_config = {
            'max_failed_attempts': 5,
            'lockout_duration': 900,  # 15 minutes en secondes
            'session_timeout': 3600,  # 1 heure en secondes
            'password_min_length': 8,
            'password_require_special': True,
            'password_require_numbers': True,
            'password_require_uppercase': True,
            'password_history_count': 5,  # Nombre d'anciens mots de passe à retenir
            'force_password_change_days': 90,
            'audit_retention_days': 365
        }
        
        # Charger la configuration de sécurité si elle existe
        self.load_security_config()
    
    def load_security_config(self):
        """Charge la configuration de sécurité depuis un fichier"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), 'security_config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    saved_config = json.load(f)
                    self.security_config.update(saved_config)
        except Exception as e:
            print(f"Erreur lors du chargement de la configuration de sécurité: {e}")
    
    def save_security_config(self):
        """Sauvegarde la configuration de sécurité"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), 'security_config.json')
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w') as f:
                json.dump(self.security_config, f, indent=2)
        except Exception as e:
            print(f"Erreur lors de la sauvegarde de la configuration de sécurité: {e}")
    
    def generate_secure_password(self, length: int = 12) -> str:
        """Génère un mot de passe sécurisé"""
        import string
        
        # Caractères disponibles
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        # Assurer au moins un caractère de chaque type
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]
        
        # Compléter avec des caractères aléatoires
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # Mélanger le mot de passe
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """Valide la force d'un mot de passe"""
        errors = []
        
        # Longueur minimale
        if len(password) < self.security_config['password_min_length']:
            errors.append(f"Le mot de passe doit contenir au moins {self.security_config['password_min_length']} caractères")
        
        # Caractères spéciaux
        if self.security_config['password_require_special']:
            if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
                errors.append("Le mot de passe doit contenir au moins un caractère spécial")
        
        # Chiffres
        if self.security_config['password_require_numbers']:
            if not any(c.isdigit() for c in password):
                errors.append("Le mot de passe doit contenir au moins un chiffre")
        
        # Majuscules
        if self.security_config['password_require_uppercase']:
            if not any(c.isupper() for c in password):
                errors.append("Le mot de passe doit contenir au moins une majuscule")
        
        # Minuscules
        if not any(c.islower() for c in password):
            errors.append("Le mot de passe doit contenir au moins une minuscule")
        
        # Mots de passe communs (liste basique)
        common_passwords = [
            'password', '123456', 'admin', 'qwerty', 'letmein',
            'welcome', 'monkey', '1234567890', 'password123'
        ]
        if password.lower() in common_passwords:
            errors.append("Ce mot de passe est trop commun")
        
        return len(errors) == 0, errors
    
    def hash_password(self, password: str) -> str:
        """Hash un mot de passe avec bcrypt et salt"""
        # Générer un salt aléatoire
        salt = bcrypt.gensalt(rounds=12)
        # Hasher le mot de passe
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Vérifie un mot de passe contre son hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception:
            return False
    
    def check_failed_attempts(self, identifier: str) -> Tuple[bool, int, Optional[datetime]]:
        """
        Vérifie les tentatives de connexion échouées
        
        Returns:
            (is_locked, attempts_count, locked_until)
        """
        now = datetime.now()
        
        if identifier not in self.failed_attempts:
            return False, 0, None
        
        attempt_data = self.failed_attempts[identifier]
        
        # Vérifier si le verrouillage a expiré
        if 'locked_until' in attempt_data and attempt_data['locked_until']:
            if now < attempt_data['locked_until']:
                return True, attempt_data['count'], attempt_data['locked_until']
            else:
                # Le verrouillage a expiré, réinitialiser
                self.failed_attempts[identifier] = {'count': 0, 'last_attempt': now}
                return False, 0, None
        
        return False, attempt_data['count'], None
    
    def record_failed_attempt(self, identifier: str, user_id: Optional[int] = None):
        """Enregistre une tentative de connexion échouée"""
        now = datetime.now()
        
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = {'count': 0, 'last_attempt': now}
        
        self.failed_attempts[identifier]['count'] += 1
        self.failed_attempts[identifier]['last_attempt'] = now
        
        # Verrouiller si trop de tentatives
        if self.failed_attempts[identifier]['count'] >= self.security_config['max_failed_attempts']:
            lockout_until = now + timedelta(seconds=self.security_config['lockout_duration'])
            self.failed_attempts[identifier]['locked_until'] = lockout_until
            
            # Log de sécurité
            AuditLog.log_action(
                utilisateur_id=user_id,
                action="account_locked",
                module="security",
                resultat="echec",
                details=f"Compte verrouillé pour {identifier} après {self.failed_attempts[identifier]['count']} tentatives"
            )
    
    def record_successful_login(self, identifier: str, user_id: int) -> str:
        """Enregistre une connexion réussie et crée une session"""
        # Réinitialiser les tentatives échouées
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]
        
        # Créer une session
        session_id = self.create_session(user_id, identifier)
        
        # Log de sécurité
        AuditLog.log_action(
            utilisateur_id=user_id,
            action="login_success",
            module="security",
            details=f"Connexion réussie pour {identifier}"
        )
        
        return session_id
    
    def create_session(self, user_id: int, ip: str = "127.0.0.1") -> str:
        """Crée une nouvelle session utilisateur"""
        session_id = secrets.token_urlsafe(32)
        now = datetime.now()
        
        self.active_sessions[session_id] = {
            'user_id': user_id,
            'created_at': now,
            'last_activity': now,
            'ip': ip
        }
        
        return session_id
    
    def validate_session(self, session_id: str) -> Tuple[bool, Optional[int]]:
        """Valide une session et retourne l'ID utilisateur si valide"""
        if session_id not in self.active_sessions:
            return False, None
        
        session = self.active_sessions[session_id]
        now = datetime.now()
        
        # Vérifier l'expiration
        if (now - session['last_activity']).total_seconds() > self.security_config['session_timeout']:
            del self.active_sessions[session_id]
            return False, None
        
        # Mettre à jour l'activité
        session['last_activity'] = now
        
        return True, session['user_id']
    
    def invalidate_session(self, session_id: str):
        """Invalide une session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
    
    def invalidate_all_user_sessions(self, user_id: int):
        """Invalide toutes les sessions d'un utilisateur"""
        sessions_to_remove = []
        for sid, session in self.active_sessions.items():
            if session['user_id'] == user_id:
                sessions_to_remove.append(sid)
        
        for sid in sessions_to_remove:
            del self.active_sessions[sid]
    
    def get_active_sessions_count(self) -> int:
        """Retourne le nombre de sessions actives"""
        return len(self.active_sessions)
    
    def get_user_sessions(self, user_id: int) -> List[Dict]:
        """Retourne les sessions actives d'un utilisateur"""
        user_sessions = []
        for sid, session in self.active_sessions.items():
            if session['user_id'] == user_id:
                user_sessions.append({
                    'session_id': sid,
                    'created_at': session['created_at'],
                    'last_activity': session['last_activity'],
                    'ip': session['ip']
                })
        return user_sessions
    
    def cleanup_expired_sessions(self):
        """Nettoie les sessions expirées"""
        now = datetime.now()
        expired_sessions = []
        
        for sid, session in self.active_sessions.items():
            if (now - session['last_activity']).total_seconds() > self.security_config['session_timeout']:
                expired_sessions.append(sid)
        
        for sid in expired_sessions:
            del self.active_sessions[sid]
        
        return len(expired_sessions)
    
    def detect_suspicious_activity(self, user_id: int, action: str, details: str = "") -> bool:
        """Détecte les activités suspectes"""
        # Récupérer les actions récentes de l'utilisateur
        recent_logs = AuditLog.find_all(
            "utilisateur_id = ? AND created_at > datetime('now', '-1 hour') ORDER BY created_at DESC LIMIT 50",
            (user_id,)
        )
        
        # Analyser les patterns suspects
        suspicious = False
        alerts = []
        
        # 1. Trop d'actions en peu de temps
        if len(recent_logs) > 100:
            suspicious = True
            alerts.append("Activité intensive détectée")
        
        # 2. Actions répétitives
        action_counts = {}
        for log in recent_logs:
            action_counts[log.action] = action_counts.get(log.action, 0) + 1
        
        for act, count in action_counts.items():
            if count > 20:
                suspicious = True
                alerts.append(f"Action répétitive détectée: {act} ({count} fois)")
        
        # 3. Tentatives d'accès à des ressources non autorisées
        if "permission_denied" in [log.resultat for log in recent_logs[-10:]]:
            suspicious = True
            alerts.append("Tentatives d'accès non autorisé")
        
        # Enregistrer l'alerte si activité suspecte
        if suspicious:
            AuditLog.log_action(
                utilisateur_id=user_id,
                action="suspicious_activity_detected",
                module="security",
                resultat="alerte",
                details=f"Activité suspecte: {', '.join(alerts)}. Action: {action}. {details}"
            )
        
        return suspicious
    
    def generate_security_report(self) -> Dict:
        """Génère un rapport de sécurité"""
        now = datetime.now()
        
        # Statistiques des sessions
        sessions_stats = {
            'active_sessions': len(self.active_sessions),
            'sessions_by_user': {}
        }
        
        for session in self.active_sessions.values():
            user_id = session['user_id']
            sessions_stats['sessions_by_user'][user_id] = sessions_stats['sessions_by_user'].get(user_id, 0) + 1
        
        # Statistiques des tentatives échouées
        failed_stats = {
            'locked_accounts': 0,
            'failed_attempts_total': 0
        }
        
        for identifier, data in self.failed_attempts.items():
            failed_stats['failed_attempts_total'] += data['count']
            if 'locked_until' in data and data['locked_until'] and now < data['locked_until']:
                failed_stats['locked_accounts'] += 1
        
        # Logs de sécurité récents
        security_logs = AuditLog.find_all(
            "module = 'security' AND created_at > datetime('now', '-24 hours') ORDER BY created_at DESC LIMIT 20"
        )
        
        return {
            'timestamp': now.isoformat(),
            'sessions': sessions_stats,
            'failed_attempts': failed_stats,
            'recent_security_events': len(security_logs),
            'config': self.security_config
        }

# Instance globale du gestionnaire de sécurité
security_manager = SecurityManager()
