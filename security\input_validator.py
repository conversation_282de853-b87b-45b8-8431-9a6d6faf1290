"""
Validateur d'entrées et protection contre les injections
"""
import re
import html
import json
from typing import Any, Dict, List, Optional, Union
import unicodedata

class InputValidator:
    """Validateur d'entrées pour sécuriser l'application"""
    
    def __init__(self):
        # Patterns dangereux pour la détection d'injection SQL
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
            r"(\b(UNION|OR|AND)\s+\d+\s*=\s*\d+)",
            r"(--|#|/\*|\*/)",
            r"(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b)",
            r"(<script|</script>|javascript:|vbscript:)",
            r"(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\(\s*\d+\s*\))",
            r"(\b(CAST|CONVERT|SUBSTRING|ASCII|CHAR_LENGTH)\s*\()",
            r"(\b(WAITFOR|DELAY|SLEEP)\s*\()",
            r"(\b(XP_|SP_)\w+)",
            r"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
        ]
        
        # Patterns pour XSS
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
            r"<style[^>]*>.*?</style>"
        ]
        
        # Caractères autorisés pour différents types de données
        self.allowed_chars = {
            'alphanumeric': r'^[a-zA-Z0-9]+$',
            'alphanumeric_space': r'^[a-zA-Z0-9\s]+$',
            'alphanumeric_special': r'^[a-zA-Z0-9\s\-_\.@]+$',
            'numeric': r'^\d+$',
            'decimal': r'^\d+(\.\d+)?$',
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'phone': r'^[\+]?[\d\s\-\(\)]+$',
            'username': r'^[a-zA-Z0-9_\-\.]{3,50}$',
            'password': r'^.{8,}$',  # Au moins 8 caractères
            'name': r'^[a-zA-ZÀ-ÿ\s\-\'\.]{2,100}$',
            'address': r'^[a-zA-Z0-9À-ÿ\s\-\'\.,#]+$',
            'barcode': r'^[a-zA-Z0-9\-]+$',
            'price': r'^\d+(\.\d{1,2})?$'
        }
    
    def sanitize_input(self, input_value: Any, max_length: Optional[int] = None) -> str:
        """Nettoie et sécurise une entrée utilisateur"""
        if input_value is None:
            return ""
        
        # Convertir en string
        clean_value = str(input_value)
        
        # Normaliser les caractères Unicode
        clean_value = unicodedata.normalize('NFKC', clean_value)
        
        # Supprimer les caractères de contrôle
        clean_value = ''.join(char for char in clean_value if unicodedata.category(char)[0] != 'C')
        
        # Échapper les caractères HTML
        clean_value = html.escape(clean_value)
        
        # Limiter la longueur
        if max_length and len(clean_value) > max_length:
            clean_value = clean_value[:max_length]
        
        # Supprimer les espaces en début et fin
        clean_value = clean_value.strip()
        
        return clean_value
    
    def validate_against_pattern(self, value: str, pattern_name: str) -> bool:
        """Valide une valeur contre un pattern prédéfini"""
        if pattern_name not in self.allowed_chars:
            return False
        
        pattern = self.allowed_chars[pattern_name]
        return bool(re.match(pattern, value, re.IGNORECASE))
    
    def detect_sql_injection(self, input_value: str) -> bool:
        """Détecte les tentatives d'injection SQL"""
        if not input_value:
            return False
        
        # Convertir en minuscules pour la détection
        lower_input = input_value.lower()
        
        # Vérifier chaque pattern
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, lower_input, re.IGNORECASE):
                return True
        
        return False
    
    def detect_xss(self, input_value: str) -> bool:
        """Détecte les tentatives de XSS"""
        if not input_value:
            return False
        
        # Vérifier chaque pattern XSS
        for pattern in self.xss_patterns:
            if re.search(pattern, input_value, re.IGNORECASE):
                return True
        
        return False
    
    def validate_user_input(self, input_data: Dict[str, Any], validation_rules: Dict[str, Dict]) -> Dict[str, Any]:
        """
        Valide un dictionnaire d'entrées utilisateur
        
        Args:
            input_data: Données à valider
            validation_rules: Règles de validation par champ
                Format: {
                    'field_name': {
                        'required': bool,
                        'type': str,  # 'string', 'int', 'float', 'email', etc.
                        'min_length': int,
                        'max_length': int,
                        'pattern': str,  # nom du pattern prédéfini
                        'custom_validator': callable
                    }
                }
        
        Returns:
            Dict avec 'valid': bool, 'errors': list, 'cleaned_data': dict
        """
        result = {
            'valid': True,
            'errors': [],
            'cleaned_data': {}
        }
        
        for field_name, rules in validation_rules.items():
            field_value = input_data.get(field_name)
            
            # Vérifier si le champ est requis
            if rules.get('required', False) and (field_value is None or field_value == ''):
                result['errors'].append(f"Le champ '{field_name}' est obligatoire")
                result['valid'] = False
                continue
            
            # Si le champ n'est pas requis et est vide, passer au suivant
            if field_value is None or field_value == '':
                result['cleaned_data'][field_name] = ''
                continue
            
            # Nettoyer la valeur
            cleaned_value = self.sanitize_input(field_value, rules.get('max_length'))
            
            # Détecter les injections
            if self.detect_sql_injection(cleaned_value):
                result['errors'].append(f"Tentative d'injection SQL détectée dans '{field_name}'")
                result['valid'] = False
                continue
            
            if self.detect_xss(cleaned_value):
                result['errors'].append(f"Tentative de XSS détectée dans '{field_name}'")
                result['valid'] = False
                continue
            
            # Validation de type
            field_type = rules.get('type', 'string')
            if not self._validate_type(cleaned_value, field_type):
                result['errors'].append(f"Type invalide pour '{field_name}' (attendu: {field_type})")
                result['valid'] = False
                continue
            
            # Validation de longueur
            min_length = rules.get('min_length')
            if min_length and len(cleaned_value) < min_length:
                result['errors'].append(f"'{field_name}' doit contenir au moins {min_length} caractères")
                result['valid'] = False
                continue
            
            max_length = rules.get('max_length')
            if max_length and len(cleaned_value) > max_length:
                result['errors'].append(f"'{field_name}' ne peut pas dépasser {max_length} caractères")
                result['valid'] = False
                continue
            
            # Validation par pattern
            pattern = rules.get('pattern')
            if pattern and not self.validate_against_pattern(cleaned_value, pattern):
                result['errors'].append(f"Format invalide pour '{field_name}'")
                result['valid'] = False
                continue
            
            # Validation personnalisée
            custom_validator = rules.get('custom_validator')
            if custom_validator and callable(custom_validator):
                try:
                    if not custom_validator(cleaned_value):
                        result['errors'].append(f"Validation personnalisée échouée pour '{field_name}'")
                        result['valid'] = False
                        continue
                except Exception as e:
                    result['errors'].append(f"Erreur de validation pour '{field_name}': {e}")
                    result['valid'] = False
                    continue
            
            # Convertir le type si nécessaire
            try:
                if field_type == 'int':
                    cleaned_value = int(cleaned_value)
                elif field_type == 'float':
                    cleaned_value = float(cleaned_value)
                elif field_type == 'bool':
                    cleaned_value = cleaned_value.lower() in ('true', '1', 'yes', 'on')
            except ValueError:
                result['errors'].append(f"Impossible de convertir '{field_name}' en {field_type}")
                result['valid'] = False
                continue
            
            result['cleaned_data'][field_name] = cleaned_value
        
        return result
    
    def _validate_type(self, value: str, expected_type: str) -> bool:
        """Valide le type d'une valeur"""
        if expected_type == 'string':
            return True
        elif expected_type == 'int':
            try:
                int(value)
                return True
            except ValueError:
                return False
        elif expected_type == 'float':
            try:
                float(value)
                return True
            except ValueError:
                return False
        elif expected_type == 'email':
            return self.validate_against_pattern(value, 'email')
        elif expected_type == 'phone':
            return self.validate_against_pattern(value, 'phone')
        elif expected_type == 'bool':
            return value.lower() in ('true', 'false', '1', '0', 'yes', 'no', 'on', 'off')
        else:
            return True
    
    def validate_json_input(self, json_string: str, max_depth: int = 10) -> Dict[str, Any]:
        """Valide et parse un JSON de manière sécurisée"""
        result = {
            'valid': False,
            'data': None,
            'error': None
        }
        
        try:
            # Limiter la taille du JSON
            if len(json_string) > 1024 * 1024:  # 1MB max
                result['error'] = "JSON trop volumineux"
                return result
            
            # Parser le JSON
            data = json.loads(json_string)
            
            # Vérifier la profondeur
            if self._get_json_depth(data) > max_depth:
                result['error'] = "JSON trop profond"
                return result
            
            result['valid'] = True
            result['data'] = data
            
        except json.JSONDecodeError as e:
            result['error'] = f"JSON invalide: {e}"
        except Exception as e:
            result['error'] = f"Erreur lors de la validation JSON: {e}"
        
        return result
    
    def _get_json_depth(self, obj: Any, depth: int = 0) -> int:
        """Calcule la profondeur d'un objet JSON"""
        if isinstance(obj, dict):
            return max([self._get_json_depth(v, depth + 1) for v in obj.values()], default=depth)
        elif isinstance(obj, list):
            return max([self._get_json_depth(item, depth + 1) for item in obj], default=depth)
        else:
            return depth
    
    def create_safe_filename(self, filename: str) -> str:
        """Crée un nom de fichier sécurisé"""
        # Supprimer les caractères dangereux
        safe_chars = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # Limiter la longueur
        if len(safe_chars) > 255:
            name, ext = os.path.splitext(safe_chars)
            safe_chars = name[:255-len(ext)] + ext
        
        # Éviter les noms réservés
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        name_without_ext = os.path.splitext(safe_chars)[0].upper()
        if name_without_ext in reserved_names:
            safe_chars = f"file_{safe_chars}"
        
        return safe_chars
    
    def validate_file_upload(self, file_path: str, allowed_extensions: List[str], 
                           max_size: int = 10 * 1024 * 1024) -> Dict[str, Any]:
        """Valide un fichier uploadé"""
        result = {
            'valid': False,
            'error': None,
            'safe_filename': None
        }
        
        try:
            import os
            
            # Vérifier que le fichier existe
            if not os.path.exists(file_path):
                result['error'] = "Fichier non trouvé"
                return result
            
            # Vérifier la taille
            file_size = os.path.getsize(file_path)
            if file_size > max_size:
                result['error'] = f"Fichier trop volumineux (max: {max_size} bytes)"
                return result
            
            # Vérifier l'extension
            filename = os.path.basename(file_path)
            _, ext = os.path.splitext(filename)
            ext = ext.lower()
            
            if ext not in [e.lower() for e in allowed_extensions]:
                result['error'] = f"Extension non autorisée. Autorisées: {', '.join(allowed_extensions)}"
                return result
            
            # Créer un nom de fichier sécurisé
            safe_filename = self.create_safe_filename(filename)
            
            result['valid'] = True
            result['safe_filename'] = safe_filename
            
        except Exception as e:
            result['error'] = f"Erreur lors de la validation du fichier: {e}"
        
        return result

# Instance globale du validateur
input_validator = InputValidator()
