"""
Générateur de rapports PDF
"""
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.platypus.flowables import HRFlowable

class PDFGenerator:
    def __init__(self):
        self.export_dir = "exports"
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)
        
        # Styles
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center
        )
        
        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12
        )
    
    def generate_sales_report(self, ventes, date_debut, date_fin):
        """Génère un rapport de ventes en PDF"""
        filename = f"rapport_ventes_{date_debut}_{date_fin}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join(self.export_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        
        # Titre
        title = Paragraph(f"Rapport de Ventes<br/>{date_debut} au {date_fin}", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Statistiques générales
        total_ca = sum(vente.total_ttc for vente in ventes)
        nb_ventes = len(ventes)
        ticket_moyen = total_ca / nb_ventes if nb_ventes > 0 else 0
        
        stats_data = [
            ['Statistiques Générales', ''],
            ['Nombre de ventes', str(nb_ventes)],
            ['Chiffre d\'affaires', f"{total_ca:.2f} €"],
            ['Ticket moyen', f"{ticket_moyen:.2f} €"]
        ]
        
        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 20))
        
        # Détail des ventes
        if ventes:
            story.append(Paragraph("Détail des Ventes", self.heading_style))
            
            ventes_data = [['Date', 'N° Ticket', 'Total TTC', 'Mode Paiement', 'Vendeur']]
            
            for vente in ventes[:50]:  # Limiter à 50 ventes pour éviter un PDF trop long
                ventes_data.append([
                    datetime.fromisoformat(vente.date_vente).strftime('%d/%m/%Y %H:%M'),
                    vente.numero_ticket,
                    f"{vente.total_ttc:.2f} €",
                    vente.mode_paiement.capitalize(),
                    vente.get_utilisateur_nom()
                ])
            
            if len(ventes) > 50:
                ventes_data.append(['...', f'({len(ventes) - 50} ventes supplémentaires)', '', '', ''])
            
            ventes_table = Table(ventes_data, colWidths=[1.5*inch, 1.2*inch, 1*inch, 1.2*inch, 1.5*inch])
            ventes_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(ventes_table)
        
        # Pied de page
        story.append(Spacer(1, 30))
        story.append(HRFlowable(width="100%"))
        story.append(Spacer(1, 10))
        footer = Paragraph(f"Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", self.styles['Normal'])
        story.append(footer)
        
        doc.build(story)
        return filepath
    
    def generate_stock_report(self, produits):
        """Génère un rapport de stock en PDF"""
        filename = f"rapport_stock_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join(self.export_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        
        # Titre
        title = Paragraph("Rapport d'État du Stock", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Statistiques générales
        total_valeur = sum(p.stock_actuel * p.prix_achat for p in produits)
        produits_faible_stock = sum(1 for p in produits if p.is_stock_faible())
        produits_rupture = sum(1 for p in produits if p.stock_actuel == 0)
        
        stats_data = [
            ['Statistiques Générales', ''],
            ['Nombre de produits', str(len(produits))],
            ['Valeur totale du stock', f"{total_valeur:.2f} €"],
            ['Produits en stock faible', str(produits_faible_stock)],
            ['Produits en rupture', str(produits_rupture)]
        ]
        
        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 20))
        
        # Détail du stock
        story.append(Paragraph("Détail du Stock", self.heading_style))
        
        stock_data = [['Produit', 'Stock', 'Unité', 'Prix Achat', 'Valeur', 'Statut']]
        
        for produit in produits:
            valeur = produit.stock_actuel * produit.prix_achat
            
            if produit.stock_actuel == 0:
                statut = "Rupture"
            elif produit.is_stock_faible():
                statut = "Stock faible"
            else:
                statut = "Normal"
            
            stock_data.append([
                produit.nom[:25] + "..." if len(produit.nom) > 25 else produit.nom,
                str(produit.stock_actuel),
                produit.unite,
                f"{produit.prix_achat:.2f} €",
                f"{valeur:.2f} €",
                statut
            ])
        
        stock_table = Table(stock_data, colWidths=[2.2*inch, 0.8*inch, 0.8*inch, 1*inch, 1*inch, 1*inch])
        stock_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stock_table)
        
        # Pied de page
        story.append(Spacer(1, 30))
        story.append(HRFlowable(width="100%"))
        story.append(Spacer(1, 10))
        footer = Paragraph(f"Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", self.styles['Normal'])
        story.append(footer)
        
        doc.build(story)
        return filepath
    
    def generate_financial_report(self, ventes, date_debut, date_fin):
        """Génère un rapport financier en PDF"""
        filename = f"rapport_financier_{date_debut}_{date_fin}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join(self.export_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        
        # Titre
        title = Paragraph(f"Rapport Financier<br/>{date_debut} au {date_fin}", self.title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Statistiques financières
        total_ca = sum(vente.total_ttc for vente in ventes)
        nb_ventes = len(ventes)
        
        # Calculer le coût des ventes
        total_cout = 0
        for vente in ventes:
            for detail in vente.details:
                from models.produit import Produit
                produit = Produit.find_by_id(detail.produit_id)
                if produit:
                    total_cout += detail.quantite * produit.prix_achat
        
        marge_brute = total_ca - total_cout
        taux_marge = (marge_brute / total_ca * 100) if total_ca > 0 else 0
        
        stats_data = [
            ['Indicateurs Financiers', ''],
            ['Chiffre d\'affaires', f"{total_ca:.2f} €"],
            ['Coût des ventes', f"{total_cout:.2f} €"],
            ['Marge brute', f"{marge_brute:.2f} €"],
            ['Taux de marge', f"{taux_marge:.1f}%"],
            ['Nombre de transactions', str(nb_ventes)]
        ]
        
        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 20))
        
        # Répartition par mode de paiement
        story.append(Paragraph("Répartition par Mode de Paiement", self.heading_style))
        
        paiements = {}
        for vente in ventes:
            mode = vente.mode_paiement
            if mode not in paiements:
                paiements[mode] = {'nombre': 0, 'montant': 0}
            paiements[mode]['nombre'] += 1
            paiements[mode]['montant'] += vente.total_ttc
        
        payment_data = [['Mode de Paiement', 'Nombre', 'Montant', 'Pourcentage']]
        
        for mode, data in paiements.items():
            pourcentage = (data['montant'] / total_ca * 100) if total_ca > 0 else 0
            mode_nom = {
                'cash': 'Espèces',
                'carte': 'Carte bancaire',
                'mobile': 'Paiement mobile',
                'credit': 'Crédit client',
                'cheque': 'Chèque'
            }.get(mode, mode.capitalize())
            
            payment_data.append([
                mode_nom,
                str(data['nombre']),
                f"{data['montant']:.2f} €",
                f"{pourcentage:.1f}%"
            ])
        
        payment_table = Table(payment_data, colWidths=[2*inch, 1*inch, 1.5*inch, 1.5*inch])
        payment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(payment_table)
        
        # Pied de page
        story.append(Spacer(1, 30))
        story.append(HRFlowable(width="100%"))
        story.append(Spacer(1, 10))
        footer = Paragraph(f"Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", self.styles['Normal'])
        story.append(footer)
        
        doc.build(story)
        return filepath
