"""
Gestionnaire de base de données pour l'application de gestion de stock du bar
"""
import sqlite3
import os
from datetime import datetime
import hashlib
import logging

class DatabaseManager:
    def __init__(self, db_path="bar_stock.db"):
        self.db_path = db_path
        self.init_database()
        
    def get_connection(self):
        """Retourne une connexion à la base de données"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialise la base de données avec toutes les tables nécessaires"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Table des catégories de produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des fournisseurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fournisseurs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom VARCHAR(200) NOT NULL,
                    contact VARCHAR(100),
                    telephone VARCHAR(20),
                    email VARCHAR(100),
                    adresse TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS produits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom VARCHAR(200) NOT NULL,
                    code_barre VARCHAR(50) UNIQUE,
                    categorie_id INTEGER,
                    unite VARCHAR(50) NOT NULL,
                    prix_achat DECIMAL(10,2) NOT NULL,
                    prix_vente DECIMAL(10,2) NOT NULL,
                    stock_actuel INTEGER DEFAULT 0,
                    seuil_minimum INTEGER DEFAULT 5,
                    fournisseur_id INTEGER,
                    actif BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (categorie_id) REFERENCES categories(id),
                    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id)
                )
            ''')
            
            # Table des utilisateurs avec sécurité avancée
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS utilisateurs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom_utilisateur VARCHAR(50) NOT NULL UNIQUE,
                    mot_de_passe_hash VARCHAR(255) NOT NULL,
                    nom_complet VARCHAR(200) NOT NULL,
                    role VARCHAR(20) NOT NULL CHECK (role IN ('gerant', 'serveur', 'caissier')),
                    actif BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    derniere_connexion TIMESTAMP,
                    mot_de_passe_expire_le TIMESTAMP,
                    force_changement_mdp BOOLEAN DEFAULT 0,
                    tentatives_connexion_echouees INTEGER DEFAULT 0,
                    verrouille_jusqu TIMESTAMP,
                    historique_mots_de_passe TEXT,
                    session_id TEXT,
                    permissions_personnalisees TEXT
                )
            ''')

            # Ajouter les nouvelles colonnes si elles n'existent pas (migration)
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN derniere_connexion TIMESTAMP')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN mot_de_passe_expire_le TIMESTAMP')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN force_changement_mdp BOOLEAN DEFAULT 0')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN tentatives_connexion_echouees INTEGER DEFAULT 0')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN verrouille_jusqu TIMESTAMP')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN historique_mots_de_passe TEXT')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN session_id TEXT')
            except:
                pass
            try:
                cursor.execute('ALTER TABLE utilisateurs ADD COLUMN permissions_personnalisees TEXT')
            except:
                pass
            
            # Table des sessions utilisateurs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    utilisateur_id INTEGER,
                    token VARCHAR(255) NOT NULL,
                    debut_session TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    fin_session TIMESTAMP,
                    actif BOOLEAN DEFAULT 1,
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id)
                )
            ''')
            
            # Table des mouvements de stock
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mouvements_stock (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    produit_id INTEGER NOT NULL,
                    type_mouvement VARCHAR(20) NOT NULL CHECK (type_mouvement IN ('entree', 'sortie', 'inventaire')),
                    quantite INTEGER NOT NULL,
                    prix_unitaire DECIMAL(10,2),
                    reference VARCHAR(100),
                    utilisateur_id INTEGER,
                    date_mouvement TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    commentaire TEXT,
                    FOREIGN KEY (produit_id) REFERENCES produits(id),
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id)
                )
            ''')
            
            # Table des ventes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ventes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero_ticket VARCHAR(50) NOT NULL UNIQUE,
                    utilisateur_id INTEGER,
                    total_ht DECIMAL(10,2) NOT NULL,
                    total_ttc DECIMAL(10,2) NOT NULL,
                    mode_paiement VARCHAR(20) NOT NULL CHECK (mode_paiement IN ('cash', 'carte', 'mobile', 'credit')),
                    client_credit_id INTEGER,
                    date_vente TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    statut VARCHAR(20) DEFAULT 'termine' CHECK (statut IN ('en_cours', 'termine', 'annule')),
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
                    FOREIGN KEY (client_credit_id) REFERENCES clients_credit(id)
                )
            ''')
            
            # Table des détails de vente
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS details_vente (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    vente_id INTEGER NOT NULL,
                    produit_id INTEGER NOT NULL,
                    quantite INTEGER NOT NULL,
                    prix_unitaire DECIMAL(10,2) NOT NULL,
                    sous_total DECIMAL(10,2) NOT NULL,
                    FOREIGN KEY (vente_id) REFERENCES ventes(id),
                    FOREIGN KEY (produit_id) REFERENCES produits(id)
                )
            ''')
            
            # Table des clients à crédit
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients_credit (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom VARCHAR(200) NOT NULL,
                    telephone VARCHAR(20),
                    limite_credit DECIMAL(10,2) DEFAULT 0,
                    credit_utilise DECIMAL(10,2) DEFAULT 0,
                    actif BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des approvisionnements
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS approvisionnements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    fournisseur_id INTEGER NOT NULL,
                    numero_facture VARCHAR(100),
                    date_livraison DATE NOT NULL,
                    total_ht DECIMAL(10,2) NOT NULL,
                    total_ttc DECIMAL(10,2) NOT NULL,
                    utilisateur_id INTEGER,
                    statut VARCHAR(20) DEFAULT 'recu' CHECK (statut IN ('commande', 'recu', 'facture')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id),
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id)
                )
            ''')
            
            # Table des détails d'approvisionnement
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS details_approvisionnement (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    approvisionnement_id INTEGER NOT NULL,
                    produit_id INTEGER NOT NULL,
                    quantite INTEGER NOT NULL,
                    prix_unitaire DECIMAL(10,2) NOT NULL,
                    sous_total DECIMAL(10,2) NOT NULL,
                    FOREIGN KEY (approvisionnement_id) REFERENCES approvisionnements(id),
                    FOREIGN KEY (produit_id) REFERENCES produits(id)
                )
            ''')
            
            # Table des journaux d'audit
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    utilisateur_id INTEGER,
                    action VARCHAR(100) NOT NULL,
                    table_affectee VARCHAR(50),
                    enregistrement_id INTEGER,
                    anciennes_valeurs TEXT,
                    nouvelles_valeurs TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id)
                )
            ''')
            
            # Table des paramètres de l'application
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS parametres (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cle VARCHAR(100) NOT NULL UNIQUE,
                    valeur TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Table du journal de caisse
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS journal_caisse (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date_operation DATE NOT NULL,
                    type_operation TEXT NOT NULL,
                    description TEXT NOT NULL,
                    montant_entree REAL DEFAULT 0.0,
                    montant_sortie REAL DEFAULT 0.0,
                    solde REAL NOT NULL,
                    utilisateur_id INTEGER,
                    reference TEXT,
                    mode_paiement TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs (id)
                )
            ''')

            # Table des logs d'audit
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    utilisateur_id INTEGER,
                    action TEXT NOT NULL,
                    module TEXT NOT NULL,
                    objet_id INTEGER,
                    objet_type TEXT,
                    donnees_avant TEXT,
                    donnees_apres TEXT,
                    adresse_ip TEXT,
                    user_agent TEXT,
                    resultat TEXT DEFAULT 'succes',
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs (id)
                )
            ''')
            
            conn.commit()
            self._insert_default_data(cursor)
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            logging.error(f"Erreur lors de l'initialisation de la base de données: {e}")
            raise
        finally:
            conn.close()
    
    def _insert_default_data(self, cursor):
        """Insère les données par défaut"""
        # Catégories par défaut
        categories_default = [
            ('Bières', 'Toutes les bières'),
            ('Spiritueux', 'Alcools forts'),
            ('Vins', 'Vins rouges, blancs, rosés'),
            ('Sodas', 'Boissons non alcoolisées'),
            ('Snacks', 'Grignotages et petite restauration'),
            ('Autres', 'Produits divers')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO categories (nom, description) VALUES (?, ?)
        ''', categories_default)
        
        # Utilisateur administrateur par défaut
        admin_password = self.hash_password("admin123")
        cursor.execute('''
            INSERT OR IGNORE INTO utilisateurs (nom_utilisateur, mot_de_passe_hash, nom_complet, role)
            VALUES (?, ?, ?, ?)
        ''', ("admin", admin_password, "Administrateur", "gerant"))
        
        # Paramètres par défaut
        parametres_default = [
            ('nom_bar', 'Mon Bar', 'Nom du bar'),
            ('adresse_bar', '', 'Adresse du bar'),
            ('telephone_bar', '', 'Téléphone du bar'),
            ('tva_taux', '20', 'Taux de TVA en pourcentage'),
            ('devise', 'EUR', 'Devise utilisée'),
            ('backup_auto', '1', 'Sauvegarde automatique activée'),
            ('alerte_stock', '1', 'Alertes de stock activées')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO parametres (cle, valeur, description) VALUES (?, ?, ?)
        ''', parametres_default)
    
    @staticmethod
    def hash_password(password):
        """Hash un mot de passe avec SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, hashed_password):
        """Vérifie un mot de passe"""
        return self.hash_password(password) == hashed_password
    
    def execute_query(self, query, params=None):
        """Exécute une requête et retourne les résultats"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            conn.rollback()
            logging.error(f"Erreur lors de l'exécution de la requête: {e}")
            raise
        finally:
            conn.close()
    
    def get_table_columns(self, table_name):
        """Retourne les colonnes d'une table"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [column[1] for column in cursor.fetchall()]
        conn.close()
        return columns
