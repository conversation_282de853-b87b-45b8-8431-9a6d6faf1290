"""
Modèle pour les clients à crédit
"""
from models.base_model import BaseModel

class ClientCredit(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "clients_credit"
        self.nom = ""
        self.telephone = ""
        self.limite_credit = 0.0
        self.credit_utilise = 0.0
        self.actif = True
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'nom': self.nom,
            'telephone': self.telephone,
            'limite_credit': self.limite_credit,
            'credit_utilise': self.credit_utilise,
            'actif': self.actif
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.nom = row[1]
        self.telephone = row[2]
        self.limite_credit = float(row[3]) if row[3] else 0.0
        self.credit_utilise = float(row[4]) if row[4] else 0.0
        self.actif = bool(row[5])
        self.created_at = row[6]
    
    def get_credit_disponible(self):
        """Retourne le crédit disponible"""
        return self.limite_credit - self.credit_utilise
    
    def peut_acheter(self, montant):
        """Vérifie si le client peut acheter pour ce montant"""
        return self.get_credit_disponible() >= montant
    
    def ajouter_credit(self, montant):
        """Ajoute du crédit utilisé"""
        if not self.peut_acheter(montant):
            raise ValueError("Limite de crédit dépassée")
        
        self.credit_utilise += montant
        self.save()
    
    def payer_credit(self, montant):
        """Enregistre un paiement de crédit"""
        if montant > self.credit_utilise:
            montant = self.credit_utilise
        
        self.credit_utilise -= montant
        self.save()
        return montant
    
    def get_ventes_credit(self):
        """Retourne toutes les ventes à crédit de ce client"""
        from models.vente import Vente
        return Vente.find_all("client_credit_id = ? AND mode_paiement = 'credit'", (self.id,))
    
    def is_limite_atteinte(self):
        """Vérifie si la limite de crédit est atteinte"""
        return self.credit_utilise >= self.limite_credit
    
    @classmethod
    def find_clients_en_dette(cls):
        """Trouve tous les clients ayant du crédit utilisé"""
        return cls.find_all("credit_utilise > 0 AND actif = 1")
    
    @classmethod
    def find_clients_limite_atteinte(cls):
        """Trouve tous les clients ayant atteint leur limite"""
        return cls.find_all("credit_utilise >= limite_credit AND actif = 1")
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom': self.nom,
            'telephone': self.telephone,
            'limite_credit': self.limite_credit,
            'credit_utilise': self.credit_utilise,
            'credit_disponible': self.get_credit_disponible(),
            'actif': self.actif,
            'limite_atteinte': self.is_limite_atteinte(),
            'created_at': self.created_at
        }
