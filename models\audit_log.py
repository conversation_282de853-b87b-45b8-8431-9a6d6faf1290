"""
Modèle pour les logs d'audit
"""
from models.base_model import BaseModel
from datetime import datetime
import json

class AuditLog(BaseModel):
    def __init__(self):
        super().__init__()
        self.table_name = "audit_logs"
        self.utilisateur_id = None
        self.action = ""
        self.module = ""
        self.objet_id = None
        self.objet_type = ""
        self.donnees_avant = None
        self.donnees_apres = None
        self.adresse_ip = ""
        self.user_agent = ""
        self.resultat = "succes"  # succes, echec, erreur
        self.details = ""
    
    def _get_fields(self):
        """Retourne les champs à sauvegarder"""
        return {
            'utilisateur_id': self.utilisateur_id,
            'action': self.action,
            'module': self.module,
            'objet_id': self.objet_id,
            'objet_type': self.objet_type,
            'donnees_avant': json.dumps(self.donnees_avant) if self.donnees_avant else None,
            'donnees_apres': json.dumps(self.donnees_apres) if self.donnees_apres else None,
            'adresse_ip': self.adresse_ip,
            'user_agent': self.user_agent,
            'resultat': self.resultat,
            'details': self.details
        }
    
    def _load_from_row(self, row):
        """Charge les données depuis une ligne de résultat"""
        self.id = row[0]
        self.utilisateur_id = row[1]
        self.action = row[2]
        self.module = row[3]
        self.objet_id = row[4]
        self.objet_type = row[5]
        self.donnees_avant = json.loads(row[6]) if row[6] else None
        self.donnees_apres = json.loads(row[7]) if row[7] else None
        self.adresse_ip = row[8]
        self.user_agent = row[9]
        self.resultat = row[10]
        self.details = row[11]
        self.created_at = row[12]
    
    def get_utilisateur_nom(self):
        """Retourne le nom de l'utilisateur"""
        if self.utilisateur_id:
            from models.utilisateur import Utilisateur
            utilisateur = Utilisateur.find_by_id(self.utilisateur_id)
            return utilisateur.nom_complet if utilisateur else "Utilisateur supprimé"
        return "Système"
    
    @classmethod
    def log_action(cls, utilisateur_id, action, module, objet_id=None, objet_type="", 
                   donnees_avant=None, donnees_apres=None, resultat="succes", details=""):
        """Enregistre une action dans les logs d'audit"""
        try:
            log = cls()
            log.utilisateur_id = utilisateur_id
            log.action = action
            log.module = module
            log.objet_id = objet_id
            log.objet_type = objet_type
            log.donnees_avant = donnees_avant
            log.donnees_apres = donnees_apres
            log.resultat = resultat
            log.details = details
            
            # Tentative de récupération de l'IP (simplifiée pour une app desktop)
            log.adresse_ip = "127.0.0.1"
            log.user_agent = "Desktop Application"
            
            log.save()
            return log
            
        except Exception as e:
            # Ne pas faire échouer l'action principale si le log échoue
            import logging
            logging.error(f"Erreur lors de l'enregistrement du log d'audit: {e}")
            return None
    
    @classmethod
    def log_connexion(cls, utilisateur_id, succes=True, details=""):
        """Enregistre une tentative de connexion"""
        action = "connexion_succes" if succes else "connexion_echec"
        resultat = "succes" if succes else "echec"
        
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action=action,
            module="authentification",
            resultat=resultat,
            details=details
        )
    
    @classmethod
    def log_deconnexion(cls, utilisateur_id):
        """Enregistre une déconnexion"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action="deconnexion",
            module="authentification"
        )
    
    @classmethod
    def log_creation(cls, utilisateur_id, objet_type, objet_id, donnees):
        """Enregistre la création d'un objet"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action="creation",
            module=objet_type,
            objet_id=objet_id,
            objet_type=objet_type,
            donnees_apres=donnees
        )
    
    @classmethod
    def log_modification(cls, utilisateur_id, objet_type, objet_id, donnees_avant, donnees_apres):
        """Enregistre la modification d'un objet"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action="modification",
            module=objet_type,
            objet_id=objet_id,
            objet_type=objet_type,
            donnees_avant=donnees_avant,
            donnees_apres=donnees_apres
        )
    
    @classmethod
    def log_suppression(cls, utilisateur_id, objet_type, objet_id, donnees):
        """Enregistre la suppression d'un objet"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action="suppression",
            module=objet_type,
            objet_id=objet_id,
            objet_type=objet_type,
            donnees_avant=donnees
        )
    
    @classmethod
    def log_vente(cls, utilisateur_id, vente_id, montant, mode_paiement):
        """Enregistre une vente"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action="vente",
            module="ventes",
            objet_id=vente_id,
            objet_type="vente",
            donnees_apres={
                "montant": montant,
                "mode_paiement": mode_paiement
            }
        )
    
    @classmethod
    def log_mouvement_stock(cls, utilisateur_id, produit_id, type_mouvement, quantite, reference):
        """Enregistre un mouvement de stock"""
        return cls.log_action(
            utilisateur_id=utilisateur_id,
            action=f"stock_{type_mouvement}",
            module="stock",
            objet_id=produit_id,
            objet_type="produit",
            donnees_apres={
                "type_mouvement": type_mouvement,
                "quantite": quantite,
                "reference": reference
            }
        )
    
    @classmethod
    def get_logs_utilisateur(cls, utilisateur_id, limit=100):
        """Retourne les logs d'un utilisateur"""
        return cls.find_all(
            "utilisateur_id = ? ORDER BY created_at DESC LIMIT ?",
            (utilisateur_id, limit)
        )
    
    @classmethod
    def get_logs_module(cls, module, limit=100):
        """Retourne les logs d'un module"""
        return cls.find_all(
            "module = ? ORDER BY created_at DESC LIMIT ?",
            (module, limit)
        )
    
    @classmethod
    def get_logs_periode(cls, date_debut, date_fin, limit=1000):
        """Retourne les logs d'une période"""
        return cls.find_all(
            "DATE(created_at) BETWEEN ? AND ? ORDER BY created_at DESC LIMIT ?",
            (date_debut, date_fin, limit)
        )
    
    @classmethod
    def get_logs_echecs(cls, limit=100):
        """Retourne les logs d'échec"""
        return cls.find_all(
            "resultat IN ('echec', 'erreur') ORDER BY created_at DESC LIMIT ?",
            (limit,)
        )
    
    @classmethod
    def get_connexions_recentes(cls, limit=50):
        """Retourne les connexions récentes"""
        return cls.find_all(
            "action IN ('connexion_succes', 'connexion_echec') ORDER BY created_at DESC LIMIT ?",
            (limit,)
        )
    
    @classmethod
    def get_statistiques_activite(cls, date_debut, date_fin):
        """Retourne les statistiques d'activité"""
        logs = cls.get_logs_periode(date_debut, date_fin)
        
        stats = {
            'total_actions': len(logs),
            'connexions_succes': 0,
            'connexions_echecs': 0,
            'ventes': 0,
            'mouvements_stock': 0,
            'modifications': 0,
            'utilisateurs_actifs': set()
        }
        
        for log in logs:
            if log.action == 'connexion_succes':
                stats['connexions_succes'] += 1
            elif log.action == 'connexion_echec':
                stats['connexions_echecs'] += 1
            elif log.action == 'vente':
                stats['ventes'] += 1
            elif log.action.startswith('stock_'):
                stats['mouvements_stock'] += 1
            elif log.action == 'modification':
                stats['modifications'] += 1
            
            if log.utilisateur_id:
                stats['utilisateurs_actifs'].add(log.utilisateur_id)
        
        stats['utilisateurs_actifs'] = len(stats['utilisateurs_actifs'])
        
        return stats
    
    @classmethod
    def nettoyer_anciens_logs(cls, jours_conservation=90):
        """Nettoie les anciens logs (garde les X derniers jours)"""
        from datetime import timedelta
        date_limite = datetime.now() - timedelta(days=jours_conservation)
        
        # Compter les logs à supprimer
        logs_a_supprimer = cls.find_all(
            "created_at < ?",
            (date_limite.isoformat(),)
        )
        
        if logs_a_supprimer:
            # Supprimer les anciens logs
            cls.db.execute_query(
                f"DELETE FROM {cls().table_name} WHERE created_at < ?",
                (date_limite.isoformat(),)
            )
            
            return len(logs_a_supprimer)
        
        return 0
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'utilisateur_id': self.utilisateur_id,
            'utilisateur_nom': self.get_utilisateur_nom(),
            'action': self.action,
            'module': self.module,
            'objet_id': self.objet_id,
            'objet_type': self.objet_type,
            'donnees_avant': self.donnees_avant,
            'donnees_apres': self.donnees_apres,
            'adresse_ip': self.adresse_ip,
            'user_agent': self.user_agent,
            'resultat': self.resultat,
            'details': self.details,
            'created_at': self.created_at
        }
