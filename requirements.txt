# Dépendances optionnelles pour l'application de gestion de stock

# Pour l'export Excel (optionnel)
openpyxl>=3.0.0

# Pour la génération de PDF (optionnel)
reportlab>=3.6.0

# Pour les graphiques dans les rapports (optionnel)
matplotlib>=3.5.0

# Pour la planification de tâches automatiques (optionnel)
APScheduler>=3.9.0

# Pour l'envoi d'emails d'alertes (optionnel)
smtplib

# Pour la validation d'emails (optionnel)
email-validator>=1.3.0

# Pour les codes-barres (optionnel)
python-barcode>=0.13.0
Pillow>=8.0.0

# Pour la sécurité des mots de passe (requis)
bcrypt>=4.0.0

# Pour l'interface web (optionnel - pour une future extension)
Flask>=2.0.0
Flask-SQLAlchemy>=2.5.0

# Note: Les modules suivants sont inclus avec Python par défaut:
# - tkinter (interface graphique)
# - sqlite3 (base de données)
# - csv (export CSV)
# - json (export JSON)
# - datetime (gestion des dates)
# - logging (journalisation)
# - os, sys, shutil (système de fichiers)
# - zipfile (compression)
# - hashlib (hachage des mots de passe)
# - re (expressions régulières)
