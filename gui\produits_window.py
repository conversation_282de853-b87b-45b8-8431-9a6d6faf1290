"""
Fenêtre de gestion des produits
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.produit import Produit
from models.categorie import Categorie
from models.fournisseur import Fournisseur

class ProduitsWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        self.selected_produit = None
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion des Produits", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="➕ Nouveau", 
                  command=self.nouveau_produit).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="✏️ Modifier", 
                  command=self.modifier_produit).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🗑️ Supprimer", 
                  command=self.supprimer_produit).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Frame principal avec deux panneaux
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Panneau gauche - Liste des produits
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Panneau droit - Détails du produit
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_products_list(left_frame)
        self.setup_product_details(right_frame)
    
    def setup_products_list(self, parent):
        """Configure la liste des produits"""
        # Barre de recherche
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Rechercher:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # Filtre par catégorie
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filter_frame, text="Catégorie:").pack(side=tk.LEFT)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(filter_frame, textvariable=self.category_var, 
                                          state="readonly")
        self.category_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.category_combo.bind('<<ComboboxSelected>>', self.on_category_change)
        
        # Treeview pour les produits
        columns = ("ID", "Nom", "Code-barres", "Catégorie", "Stock", "Prix vente", "Statut")
        self.products_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        self.products_tree.heading("ID", text="ID")
        self.products_tree.heading("Nom", text="Nom")
        self.products_tree.heading("Code-barres", text="Code-barres")
        self.products_tree.heading("Catégorie", text="Catégorie")
        self.products_tree.heading("Stock", text="Stock")
        self.products_tree.heading("Prix vente", text="Prix vente")
        self.products_tree.heading("Statut", text="Statut")
        
        # Largeur des colonnes
        self.products_tree.column("ID", width=50)
        self.products_tree.column("Nom", width=200)
        self.products_tree.column("Code-barres", width=120)
        self.products_tree.column("Catégorie", width=100)
        self.products_tree.column("Stock", width=80)
        self.products_tree.column("Prix vente", width=100)
        self.products_tree.column("Statut", width=80)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.products_tree.xview)
        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack du treeview et scrollbars
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind de sélection
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.products_tree.bind('<Double-1>', self.modifier_produit)
    
    def setup_product_details(self, parent):
        """Configure le panneau des détails du produit"""
        details_frame = ttk.LabelFrame(parent, text="Détails du produit", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations de base
        info_frame = ttk.LabelFrame(details_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_labels = {}
        
        # Créer les labels d'information
        fields = [
            ("Nom", "nom"),
            ("Code-barres", "code_barre"),
            ("Catégorie", "categorie"),
            ("Unité", "unite"),
            ("Fournisseur", "fournisseur")
        ]
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(info_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Informations financières
        finance_frame = ttk.LabelFrame(details_frame, text="Informations financières", padding="10")
        finance_frame.pack(fill=tk.X, pady=(0, 10))
        
        finance_fields = [
            ("Prix d'achat", "prix_achat"),
            ("Prix de vente", "prix_vente"),
            ("Marge", "marge")
        ]
        
        for i, (label, key) in enumerate(finance_fields):
            ttk.Label(finance_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(finance_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Informations de stock
        stock_frame = ttk.LabelFrame(details_frame, text="Informations de stock", padding="10")
        stock_frame.pack(fill=tk.X, pady=(0, 10))
        
        stock_fields = [
            ("Stock actuel", "stock_actuel"),
            ("Seuil minimum", "seuil_minimum"),
            ("Statut stock", "statut_stock")
        ]
        
        for i, (label, key) in enumerate(stock_fields):
            ttk.Label(stock_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(stock_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Actions sur le produit sélectionné
        actions_frame = ttk.LabelFrame(details_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X)
        
        ttk.Button(actions_frame, text="📝 Modifier", 
                  command=self.modifier_produit).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="📦 Ajuster stock", 
                  command=self.ajuster_stock).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="📊 Historique", 
                  command=self.voir_historique).pack(fill=tk.X, pady=2)
    
    def load_data(self):
        """Charge les données"""
        self.load_categories()
        self.load_products()
    
    def load_categories(self):
        """Charge les catégories dans le combobox"""
        try:
            categories = Categorie.find_all()
            category_values = ["Toutes"] + [cat.nom for cat in categories]
            self.category_combo['values'] = category_values
            self.category_combo.set("Toutes")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des catégories: {e}")
    
    def load_products(self):
        """Charge les produits dans le treeview"""
        # Nettoyer le treeview
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        try:
            # Filtres
            search_term = self.search_var.get().strip()
            selected_category = self.category_var.get()
            
            # Récupérer les produits
            if search_term:
                produits = Produit.search(search_term)
            else:
                produits = Produit.find_all("actif = 1")
            
            # Filtrer par catégorie si nécessaire
            if selected_category and selected_category != "Toutes":
                categories = Categorie.find_all("nom = ?", (selected_category,))
                if categories:
                    cat_id = categories[0].id
                    produits = [p for p in produits if p.categorie_id == cat_id]
            
            # Ajouter les produits au treeview
            for produit in produits:
                statut = "🔴 Stock faible" if produit.is_stock_faible() else "✅ OK"
                
                self.products_tree.insert("", "end", values=(
                    produit.id,
                    produit.nom,
                    produit.code_barre or "-",
                    produit.get_categorie_nom(),
                    produit.stock_actuel,
                    f"{produit.prix_vente:.2f} €",
                    statut
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def on_search_change(self, *args):
        """Appelé quand le texte de recherche change"""
        self.load_products()
    
    def on_category_change(self, event):
        """Appelé quand la catégorie change"""
        self.load_products()
    
    def on_product_select(self, event):
        """Appelé quand un produit est sélectionné"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            produit_id = item['values'][0]
            self.load_product_details(produit_id)
    
    def load_product_details(self, produit_id):
        """Charge les détails d'un produit"""
        try:
            produit = Produit.find_by_id(produit_id)
            if produit:
                self.selected_produit = produit
                
                # Mettre à jour les labels
                self.detail_labels['nom'].config(text=produit.nom)
                self.detail_labels['code_barre'].config(text=produit.code_barre or "-")
                self.detail_labels['categorie'].config(text=produit.get_categorie_nom())
                self.detail_labels['unite'].config(text=produit.unite)
                self.detail_labels['fournisseur'].config(text=produit.get_fournisseur_nom())
                
                self.detail_labels['prix_achat'].config(text=f"{produit.prix_achat:.2f} €")
                self.detail_labels['prix_vente'].config(text=f"{produit.prix_vente:.2f} €")
                self.detail_labels['marge'].config(text=f"{produit.calculer_marge():.1f}%")
                
                self.detail_labels['stock_actuel'].config(text=f"{produit.stock_actuel} {produit.unite}")
                self.detail_labels['seuil_minimum'].config(text=f"{produit.seuil_minimum} {produit.unite}")
                
                statut_stock = "🔴 Stock faible" if produit.is_stock_faible() else "✅ Stock OK"
                self.detail_labels['statut_stock'].config(text=statut_stock)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des détails: {e}")
    
    def nouveau_produit(self):
        """Ouvre la fenêtre de création d'un nouveau produit"""
        try:
            from gui.dialogs.produit_dialog import ProduitDialog
            dialog = ProduitDialog(self, "Nouveau produit")
            if dialog.result:
                self.load_products()
                messagebox.showinfo("Succès", "Produit créé avec succès")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création du produit: {e}")

    def modifier_produit(self):
        """Ouvre la fenêtre de modification du produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à modifier")
            return

        try:
            from gui.dialogs.produit_dialog import ProduitDialog
            dialog = ProduitDialog(self, f"Modifier {self.selected_produit.nom}", self.selected_produit)
            if dialog.result:
                self.load_products()
                self.load_product_details(self.selected_produit.id)
                messagebox.showinfo("Succès", "Produit modifié avec succès")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la modification du produit: {e}")
    
    def supprimer_produit(self):
        """Supprime le produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", 
                              f"Êtes-vous sûr de vouloir supprimer le produit '{self.selected_produit.nom}' ?"):
            try:
                self.selected_produit.actif = False
                self.selected_produit.save()
                self.load_products()
                messagebox.showinfo("Succès", "Produit supprimé avec succès")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {e}")
    
    def ajuster_stock(self):
        """Ajuste le stock du produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit")
            return

        try:
            from gui.dialogs.ajustement_stock_dialog import AjustementStockDialog
            dialog = AjustementStockDialog(self, self.selected_produit, self.current_user)
            if dialog.result:
                # Recharger les données
                self.load_products()
                self.load_product_details(self.selected_produit.id)
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajustement de stock: {e}")

    def voir_historique(self):
        """Affiche l'historique des mouvements du produit"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit")
            return

        try:
            from gui.dialogs.historique_produit_dialog import HistoriqueProduitDialog
            dialog = HistoriqueProduitDialog(self, self.selected_produit)
        except ImportError:
            messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Historique de {self.selected_produit.nom}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'affichage de l'historique: {e}")
