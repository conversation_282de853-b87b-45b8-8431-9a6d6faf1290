"""
Gestionnaire de protection des données et conformité RGPD
"""
import os
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from security.encryption_manager import encryption_manager, ENCRYPTION_AVAILABLE
except ImportError:
    encryption_manager = None
    ENCRYPTION_AVAILABLE = False

class DataProtectionManager:
    """Gestionnaire de protection des données et conformité RGPD"""
    
    def __init__(self):
        self.sensitive_fields = {
            # Champs personnels identifiables
            'personal_data': [
                'nom_complet', 'nom_utilisateur', 'email', 'telephone', 
                'adresse', 'date_naissance', 'numero_carte'
            ],
            
            # Données financières
            'financial_data': [
                'numero_compte', 'iban', 'numero_carte_credit', 'montant_salaire',
                'limite_credit', 'credit_utilise'
            ],
            
            # Données de connexion
            'authentication_data': [
                'mot_de_passe_hash', 'session_id', 'token_reset', 'historique_mots_de_passe'
            ],
            
            # Données comportementales
            'behavioral_data': [
                'derniere_connexion', 'historique_achats', 'preferences'
            ]
        }
        
        self.retention_periods = {
            'user_data': 2555,  # 7 ans en jours
            'transaction_data': 2555,  # 7 ans pour les données comptables
            'audit_logs': 1095,  # 3 ans
            'session_data': 30,  # 30 jours
            'temporary_data': 7   # 7 jours
        }
        
        self.anonymization_rules = {
            'nom_complet': lambda x: self._anonymize_name(x),
            'nom_utilisateur': lambda x: f"user_{hashlib.md5(x.encode()).hexdigest()[:8]}",
            'email': lambda x: self._anonymize_email(x),
            'telephone': lambda x: self._anonymize_phone(x),
            'adresse': lambda x: "Adresse anonymisée",
            'numero_carte': lambda x: self._anonymize_card_number(x)
        }
    
    def classify_data_sensitivity(self, field_name: str, value: Any) -> str:
        """Classifie la sensibilité d'une donnée"""
        for category, fields in self.sensitive_fields.items():
            if field_name in fields:
                return category
        
        # Classification basée sur le contenu
        if isinstance(value, str):
            if self._looks_like_email(value):
                return 'personal_data'
            elif self._looks_like_phone(value):
                return 'personal_data'
            elif self._looks_like_card_number(value):
                return 'financial_data'
        
        return 'public_data'
    
    def encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Chiffre les données sensibles"""
        if not ENCRYPTION_AVAILABLE:
            return data
        
        encrypted_data = data.copy()
        
        for field_name, value in data.items():
            sensitivity = self.classify_data_sensitivity(field_name, value)
            
            if sensitivity in ['personal_data', 'financial_data', 'authentication_data']:
                try:
                    encrypted_data[field_name] = encryption_manager.encrypt(str(value))
                    encrypted_data[f"{field_name}_encrypted"] = True
                except Exception as e:
                    print(f"Erreur lors du chiffrement de {field_name}: {e}")
        
        return encrypted_data
    
    def decrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Déchiffre les données sensibles"""
        if not ENCRYPTION_AVAILABLE:
            return data
        
        decrypted_data = data.copy()
        
        for field_name, value in data.items():
            if field_name.endswith('_encrypted'):
                continue
            
            if f"{field_name}_encrypted" in data and data[f"{field_name}_encrypted"]:
                try:
                    decrypted_data[field_name] = encryption_manager.decrypt(str(value))
                    del decrypted_data[f"{field_name}_encrypted"]
                except Exception as e:
                    print(f"Erreur lors du déchiffrement de {field_name}: {e}")
        
        return decrypted_data
    
    def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymise les données personnelles"""
        anonymized_data = data.copy()
        
        for field_name, value in data.items():
            if field_name in self.anonymization_rules:
                try:
                    anonymized_data[field_name] = self.anonymization_rules[field_name](value)
                except Exception as e:
                    print(f"Erreur lors de l'anonymisation de {field_name}: {e}")
                    anonymized_data[field_name] = "[ANONYMISÉ]"
        
        return anonymized_data
    
    def pseudonymize_data(self, data: Dict[str, Any], salt: str = None) -> Dict[str, Any]:
        """Pseudonymise les données (anonymisation réversible)"""
        if salt is None:
            salt = "bar_management_salt_2024"
        
        pseudonymized_data = data.copy()
        
        for field_name, value in data.items():
            sensitivity = self.classify_data_sensitivity(field_name, value)
            
            if sensitivity == 'personal_data':
                # Créer un hash réversible avec salt
                hash_input = f"{salt}_{field_name}_{value}"
                pseudo_value = hashlib.sha256(hash_input.encode()).hexdigest()[:16]
                pseudonymized_data[field_name] = f"pseudo_{pseudo_value}"
        
        return pseudonymized_data
    
    def check_data_retention(self, data_type: str, created_date: datetime) -> Dict[str, Any]:
        """Vérifie si les données respectent les règles de rétention"""
        retention_days = self.retention_periods.get(data_type, 365)
        expiry_date = created_date + timedelta(days=retention_days)
        
        now = datetime.now()
        is_expired = now > expiry_date
        days_remaining = (expiry_date - now).days if not is_expired else 0
        
        return {
            'is_expired': is_expired,
            'expiry_date': expiry_date,
            'days_remaining': days_remaining,
            'retention_period': retention_days,
            'action_required': is_expired
        }
    
    def generate_data_export(self, user_id: int) -> Dict[str, Any]:
        """Génère un export des données personnelles (droit à la portabilité)"""
        try:
            from models.utilisateur import Utilisateur
            from models.client_credit import ClientCredit
            from models.vente import Vente
            from models.audit_log import AuditLog
            
            export_data = {
                'export_date': datetime.now().isoformat(),
                'user_id': user_id,
                'data_categories': {}
            }
            
            # Données utilisateur
            user = Utilisateur.find_by_id(user_id)
            if user:
                user_data = {
                    'nom_utilisateur': user.nom_utilisateur,
                    'nom_complet': user.nom_complet,
                    'role': user.role,
                    'created_at': user.created_at,
                    'derniere_connexion': user.derniere_connexion
                }
                export_data['data_categories']['user_profile'] = user_data
            
            # Données client (si applicable)
            client = ClientCredit.find_by_field('nom', user.nom_complet if user else '')
            if client:
                client_data = {
                    'nom': client.nom,
                    'telephone': client.telephone,
                    'limite_credit': client.limite_credit,
                    'credit_utilise': client.credit_utilise,
                    'created_at': client.created_at
                }
                export_data['data_categories']['client_data'] = client_data
            
            # Historique des ventes (si l'utilisateur est un client)
            ventes = Vente.find_all(f"client_credit_id = {client.id}" if client else "1=0")
            if ventes:
                ventes_data = []
                for vente in ventes:
                    ventes_data.append({
                        'numero_ticket': vente.numero_ticket,
                        'date_vente': vente.date_vente,
                        'total_ttc': vente.total_ttc,
                        'mode_paiement': vente.mode_paiement
                    })
                export_data['data_categories']['purchase_history'] = ventes_data
            
            # Logs d'audit de l'utilisateur
            logs = AuditLog.find_all(f"utilisateur_id = {user_id}")
            if logs:
                logs_data = []
                for log in logs[-50:]:  # Limiter aux 50 derniers
                    logs_data.append({
                        'action': log.action,
                        'module': log.module,
                        'created_at': log.created_at,
                        'resultat': log.resultat
                    })
                export_data['data_categories']['activity_logs'] = logs_data
            
            return export_data
            
        except Exception as e:
            return {'error': f"Erreur lors de l'export: {e}"}
    
    def delete_user_data(self, user_id: int, anonymize_only: bool = True) -> Dict[str, Any]:
        """Supprime ou anonymise les données d'un utilisateur (droit à l'oubli)"""
        try:
            from models.utilisateur import Utilisateur
            from models.client_credit import ClientCredit
            from models.audit_log import AuditLog
            
            result = {
                'success': False,
                'actions_performed': [],
                'errors': []
            }
            
            # Traiter les données utilisateur
            user = Utilisateur.find_by_id(user_id)
            if user:
                if anonymize_only:
                    # Anonymiser les données
                    user.nom_complet = self._anonymize_name(user.nom_complet)
                    user.nom_utilisateur = f"user_deleted_{user_id}"
                    user.actif = False
                    user.save()
                    result['actions_performed'].append("Données utilisateur anonymisées")
                else:
                    # Supprimer complètement (attention aux contraintes)
                    user.delete()
                    result['actions_performed'].append("Utilisateur supprimé")
            
            # Traiter les données client
            client = ClientCredit.find_by_field('nom', user.nom_complet if user else '')
            if client:
                if anonymize_only:
                    client.nom = self._anonymize_name(client.nom)
                    client.telephone = self._anonymize_phone(client.telephone)
                    client.actif = False
                    client.save()
                    result['actions_performed'].append("Données client anonymisées")
                else:
                    client.delete()
                    result['actions_performed'].append("Client supprimé")
            
            # Anonymiser les logs d'audit (ne jamais supprimer pour la traçabilité)
            logs = AuditLog.find_all(f"utilisateur_id = {user_id}")
            for log in logs:
                # Marquer comme anonymisé dans les détails
                log.details = f"[UTILISATEUR ANONYMISÉ] {log.details}"
                log.save()
            
            if logs:
                result['actions_performed'].append(f"{len(logs)} logs d'audit anonymisés")
            
            result['success'] = True
            
            # Log de l'action
            AuditLog.log_action(
                utilisateur_id=None,
                action="data_deletion_gdpr",
                module="data_protection",
                objet_id=user_id,
                details=f"Suppression/anonymisation RGPD pour utilisateur {user_id}"
            )
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Erreur lors de la suppression: {e}",
                'actions_performed': [],
                'errors': [str(e)]
            }
    
    def audit_data_processing(self) -> Dict[str, Any]:
        """Audit des traitements de données personnelles"""
        try:
            from models.utilisateur import Utilisateur
            from models.client_credit import ClientCredit
            from models.audit_log import AuditLog
            
            audit_result = {
                'audit_date': datetime.now().isoformat(),
                'data_categories': {},
                'retention_compliance': {},
                'security_measures': {},
                'recommendations': []
            }
            
            # Analyser les utilisateurs
            users = Utilisateur.find_all()
            active_users = [u for u in users if u.actif]
            inactive_users = [u for u in users if not u.actif]
            
            audit_result['data_categories']['users'] = {
                'total': len(users),
                'active': len(active_users),
                'inactive': len(inactive_users)
            }
            
            # Analyser les clients
            clients = ClientCredit.find_all()
            active_clients = [c for c in clients if c.actif]
            
            audit_result['data_categories']['clients'] = {
                'total': len(clients),
                'active': len(active_clients)
            }
            
            # Vérifier la conformité de rétention
            now = datetime.now()
            for data_type, retention_days in self.retention_periods.items():
                cutoff_date = now - timedelta(days=retention_days)
                
                if data_type == 'audit_logs':
                    old_logs = AuditLog.find_all(f"created_at < '{cutoff_date.isoformat()}'")
                    audit_result['retention_compliance'][data_type] = {
                        'expired_records': len(old_logs),
                        'action_required': len(old_logs) > 0
                    }
            
            # Mesures de sécurité
            audit_result['security_measures'] = {
                'encryption_available': ENCRYPTION_AVAILABLE,
                'rbac_implemented': True,
                'audit_logging': True,
                'session_management': True
            }
            
            # Recommandations
            if not ENCRYPTION_AVAILABLE:
                audit_result['recommendations'].append("Implémenter le chiffrement des données sensibles")
            
            if audit_result['retention_compliance'].get('audit_logs', {}).get('expired_records', 0) > 0:
                audit_result['recommendations'].append("Nettoyer les anciens logs d'audit")
            
            return audit_result
            
        except Exception as e:
            return {'error': f"Erreur lors de l'audit: {e}"}
    
    def _anonymize_name(self, name: str) -> str:
        """Anonymise un nom"""
        if not name:
            return "Nom anonymisé"
        
        parts = name.split()
        if len(parts) == 1:
            return f"{parts[0][0]}***"
        else:
            return f"{parts[0][0]}*** {parts[-1][0]}***"
    
    def _anonymize_email(self, email: str) -> str:
        """Anonymise une adresse email"""
        if '@' in email:
            local, domain = email.split('@', 1)
            return f"{local[:2]}***@{domain}"
        return "<EMAIL>"
    
    def _anonymize_phone(self, phone: str) -> str:
        """Anonymise un numéro de téléphone"""
        if len(phone) >= 4:
            return f"***{phone[-4:]}"
        return "***"
    
    def _anonymize_card_number(self, card_number: str) -> str:
        """Anonymise un numéro de carte"""
        if len(card_number) >= 4:
            return f"****-****-****-{card_number[-4:]}"
        return "****-****-****-****"
    
    def _looks_like_email(self, value: str) -> bool:
        """Vérifie si une valeur ressemble à un email"""
        return '@' in value and '.' in value
    
    def _looks_like_phone(self, value: str) -> bool:
        """Vérifie si une valeur ressemble à un téléphone"""
        import re
        return bool(re.match(r'^[\+]?[\d\s\-\(\)]{8,}$', value))
    
    def _looks_like_card_number(self, value: str) -> bool:
        """Vérifie si une valeur ressemble à un numéro de carte"""
        import re
        return bool(re.match(r'^\d{4}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{4}$', value))

# Instance globale du gestionnaire de protection des données
data_protection_manager = DataProtectionManager()
