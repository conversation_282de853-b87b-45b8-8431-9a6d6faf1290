"""
Générateur de codes-barres pour les produits
"""
import os
import random
import string
from datetime import datetime

class BarcodeGenerator:
    def __init__(self):
        self.export_dir = "barcodes"
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)
    
    def generate_ean13(self, produit_id=None):
        """Génère un code-barres EAN-13"""
        if produit_id:
            # Utiliser l'ID du produit comme base
            base = f"{produit_id:06d}"
        else:
            # Générer un numéro aléatoire
            base = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        
        # Préfixe pour identifier l'application (200 = usage interne)
        prefix = "200"
        
        # Code complet sans la clé de contrôle
        code_sans_cle = prefix + base + "000"
        
        # Calculer la clé de contrôle EAN-13
        cle_controle = self.calculate_ean13_checksum(code_sans_cle)
        
        # Code complet
        code_complet = code_sans_cle[:-1] + str(cle_controle)
        
        return code_complet
    
    def calculate_ean13_checksum(self, code):
        """Calcule la clé de contrôle EAN-13"""
        if len(code) != 12:
            raise ValueError("Le code doit faire 12 chiffres")
        
        somme = 0
        for i, digit in enumerate(code):
            if i % 2 == 0:
                somme += int(digit)
            else:
                somme += int(digit) * 3
        
        reste = somme % 10
        return (10 - reste) % 10
    
    def generate_ean8(self, produit_id=None):
        """Génère un code-barres EAN-8"""
        if produit_id:
            # Utiliser l'ID du produit comme base
            base = f"{produit_id:04d}"
        else:
            # Générer un numéro aléatoire
            base = ''.join([str(random.randint(0, 9)) for _ in range(4)])
        
        # Préfixe pour identifier l'application
        prefix = "20"
        
        # Code complet sans la clé de contrôle
        code_sans_cle = prefix + base + "0"
        
        # Calculer la clé de contrôle EAN-8
        cle_controle = self.calculate_ean8_checksum(code_sans_cle)
        
        # Code complet
        code_complet = code_sans_cle[:-1] + str(cle_controle)
        
        return code_complet
    
    def calculate_ean8_checksum(self, code):
        """Calcule la clé de contrôle EAN-8"""
        if len(code) != 7:
            raise ValueError("Le code doit faire 7 chiffres")
        
        somme = 0
        for i, digit in enumerate(code):
            if i % 2 == 0:
                somme += int(digit) * 3
            else:
                somme += int(digit)
        
        reste = somme % 10
        return (10 - reste) % 10
    
    def generate_internal_code(self, produit_id=None, prefix="BAR"):
        """Génère un code interne pour l'application"""
        if produit_id:
            return f"{prefix}{produit_id:06d}"
        else:
            # Code aléatoire
            suffix = ''.join(random.choices(string.digits, k=6))
            return f"{prefix}{suffix}"
    
    def create_barcode_image(self, code, format_type="ean13"):
        """Crée une image de code-barres (nécessite python-barcode et Pillow)"""
        try:
            import barcode
            from barcode.writer import ImageWriter
            
            # Choisir le type de code-barres
            if format_type == "ean13":
                barcode_class = barcode.get_barcode_class('ean13')
            elif format_type == "ean8":
                barcode_class = barcode.get_barcode_class('ean8')
            elif format_type == "code128":
                barcode_class = barcode.get_barcode_class('code128')
            else:
                raise ValueError(f"Format non supporté: {format_type}")
            
            # Créer le code-barres
            barcode_instance = barcode_class(code, writer=ImageWriter())
            
            # Nom du fichier
            filename = f"barcode_{code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            filepath = os.path.join(self.export_dir, filename)
            
            # Sauvegarder l'image
            barcode_instance.save(filepath)
            
            return f"{filepath}.png"
            
        except ImportError:
            raise ImportError("Modules python-barcode et Pillow non installés. Installez-les avec: pip install python-barcode[images]")
        except Exception as e:
            raise Exception(f"Erreur lors de la création du code-barres: {e}")
    
    def validate_ean13(self, code):
        """Valide un code EAN-13"""
        if not code or len(code) != 13:
            return False
        
        if not code.isdigit():
            return False
        
        # Vérifier la clé de contrôle
        code_sans_cle = code[:12]
        cle_attendue = self.calculate_ean13_checksum(code_sans_cle)
        cle_fournie = int(code[12])
        
        return cle_attendue == cle_fournie
    
    def validate_ean8(self, code):
        """Valide un code EAN-8"""
        if not code or len(code) != 8:
            return False
        
        if not code.isdigit():
            return False
        
        # Vérifier la clé de contrôle
        code_sans_cle = code[:7]
        cle_attendue = self.calculate_ean8_checksum(code_sans_cle)
        cle_fournie = int(code[7])
        
        return cle_attendue == cle_fournie
    
    def get_barcode_info(self, code):
        """Retourne des informations sur un code-barres"""
        info = {
            'code': code,
            'length': len(code),
            'type': 'unknown',
            'valid': False,
            'country': None,
            'manufacturer': None
        }
        
        if len(code) == 13 and code.isdigit():
            info['type'] = 'EAN-13'
            info['valid'] = self.validate_ean13(code)
            
            if info['valid']:
                # Identifier le pays (premiers chiffres)
                country_code = code[:3]
                if country_code.startswith('200'):
                    info['country'] = 'Usage interne'
                elif country_code.startswith('30') or country_code.startswith('31'):
                    info['country'] = 'France'
                elif country_code.startswith('40'):
                    info['country'] = 'Allemagne'
                # Ajouter d'autres pays selon les besoins
                
        elif len(code) == 8 and code.isdigit():
            info['type'] = 'EAN-8'
            info['valid'] = self.validate_ean8(code)
            
        elif code.startswith('BAR') and len(code) == 9:
            info['type'] = 'Code interne'
            info['valid'] = True
            info['country'] = 'Application bar'
        
        return info
    
    def generate_batch_codes(self, count, format_type="ean13", start_id=1):
        """Génère plusieurs codes-barres en lot"""
        codes = []
        
        for i in range(count):
            produit_id = start_id + i
            
            if format_type == "ean13":
                code = self.generate_ean13(produit_id)
            elif format_type == "ean8":
                code = self.generate_ean8(produit_id)
            elif format_type == "internal":
                code = self.generate_internal_code(produit_id)
            else:
                raise ValueError(f"Format non supporté: {format_type}")
            
            codes.append({
                'produit_id': produit_id,
                'code': code,
                'type': format_type,
                'generated_at': datetime.now().isoformat()
            })
        
        return codes
    
    def export_codes_csv(self, codes, filename=None):
        """Exporte les codes-barres en CSV"""
        import csv
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"codes_barres_{timestamp}.csv"
        
        filepath = os.path.join(self.export_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['produit_id', 'code', 'type', 'generated_at']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for code_info in codes:
                writer.writerow(code_info)
        
        return filepath

# Instance globale
barcode_generator = BarcodeGenerator()
