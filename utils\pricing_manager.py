"""
Gestionnaire de tarification automatique
"""
import logging
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP

class PricingManager:
    def __init__(self):
        self.default_margins = {
            'Bières': 2.5,      # 150% de marge
            'Spiritueux': 3.0,   # 200% de marge
            'Vins': 2.2,        # 120% de marge
            'Sodas': 2.8,       # 180% de marge
            'Snacks': 2.0,      # 100% de marge
            'Autres': 2.5       # 150% de marge
        }
        
        self.pricing_rules = {
            'round_to': 0.05,    # Arrondir au 5 centimes près
            'min_margin': 1.5,   # Marge minimum de 50%
            'max_margin': 5.0,   # Marge maximum de 400%
            'competitor_factor': 0.95,  # 5% en dessous de la concurrence
            'volume_discount_threshold': 100,  # Remise volume à partir de 100 unités
            'volume_discount_rate': 0.1  # 10% de remise volume
        }
    
    def calculate_suggested_price(self, produit, margin_override=None):
        """Calcule un prix de vente suggéré pour un produit"""
        try:
            if produit.prix_achat <= 0:
                return None
            
            # Déterminer la marge à appliquer
            if margin_override:
                margin = margin_override
            else:
                # Récupérer la catégorie du produit
                categorie_nom = self.get_category_name(produit.categorie_id)
                margin = self.default_margins.get(categorie_nom, 2.5)
            
            # Calculer le prix de base
            prix_base = produit.prix_achat * margin
            
            # Appliquer les règles d'arrondi
            prix_arrondi = self.round_price(prix_base)
            
            # Vérifier les limites de marge
            prix_final = self.apply_margin_limits(prix_arrondi, produit.prix_achat)
            
            return float(prix_final)
            
        except Exception as e:
            logging.error(f"Erreur lors du calcul du prix pour {produit.nom}: {e}")
            return None
    
    def get_category_name(self, categorie_id):
        """Récupère le nom de la catégorie"""
        try:
            if not categorie_id:
                return 'Autres'
            
            from models.categorie import Categorie
            categorie = Categorie.find_by_id(categorie_id)
            return categorie.nom if categorie else 'Autres'
            
        except Exception:
            return 'Autres'
    
    def round_price(self, price):
        """Arrondit un prix selon les règles définies"""
        round_to = Decimal(str(self.pricing_rules['round_to']))
        price_decimal = Decimal(str(price))
        
        # Arrondir au multiple le plus proche
        rounded = (price_decimal / round_to).quantize(Decimal('1'), rounding=ROUND_HALF_UP) * round_to
        
        return rounded
    
    def apply_margin_limits(self, price, cost):
        """Applique les limites de marge minimum et maximum"""
        min_price = cost * self.pricing_rules['min_margin']
        max_price = cost * self.pricing_rules['max_margin']
        
        price_decimal = Decimal(str(price))
        min_price_decimal = Decimal(str(min_price))
        max_price_decimal = Decimal(str(max_price))
        
        if price_decimal < min_price_decimal:
            return min_price_decimal
        elif price_decimal > max_price_decimal:
            return max_price_decimal
        else:
            return price_decimal
    
    def analyze_pricing_performance(self, produit):
        """Analyse la performance tarifaire d'un produit"""
        try:
            if produit.prix_achat <= 0 or produit.prix_vente <= 0:
                return None
            
            # Calculer la marge actuelle
            marge_actuelle = (produit.prix_vente - produit.prix_achat) / produit.prix_achat
            marge_pourcentage = marge_actuelle * 100
            
            # Prix suggéré
            prix_suggere = self.calculate_suggested_price(produit)
            
            # Écart avec le prix suggéré
            ecart_prix = produit.prix_vente - prix_suggere if prix_suggere else 0
            ecart_pourcentage = (ecart_prix / prix_suggere * 100) if prix_suggere and prix_suggere > 0 else 0
            
            # Analyse des ventes récentes
            ventes_stats = self.get_sales_stats(produit.id)
            
            # Recommandations
            recommandations = self.generate_pricing_recommendations(
                produit, marge_pourcentage, ecart_pourcentage, ventes_stats
            )
            
            return {
                'produit_id': produit.id,
                'produit_nom': produit.nom,
                'prix_achat': produit.prix_achat,
                'prix_vente_actuel': produit.prix_vente,
                'prix_suggere': prix_suggere,
                'marge_actuelle': marge_pourcentage,
                'ecart_prix': ecart_prix,
                'ecart_pourcentage': ecart_pourcentage,
                'ventes_stats': ventes_stats,
                'recommandations': recommandations
            }
            
        except Exception as e:
            logging.error(f"Erreur lors de l'analyse tarifaire pour {produit.nom}: {e}")
            return None
    
    def get_sales_stats(self, produit_id, days=30):
        """Récupère les statistiques de vente d'un produit"""
        try:
            from models.detail_vente import DetailVente
            from models.vente import Vente
            from datetime import date, timedelta
            
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            # Récupérer les détails de vente pour ce produit
            query = """
                SELECT dv.quantite, dv.prix_unitaire, v.date_vente
                FROM details_vente dv
                JOIN ventes v ON dv.vente_id = v.id
                WHERE dv.produit_id = ? AND v.date_vente >= ? AND v.statut = 'termine'
                ORDER BY v.date_vente DESC
            """
            
            from database.db_manager import DatabaseManager
            db = DatabaseManager()
            results = db.execute_query(query, (produit_id, start_date))
            
            if not results:
                return {
                    'total_vendu': 0,
                    'nombre_ventes': 0,
                    'prix_moyen': 0,
                    'derniere_vente': None,
                    'tendance': 'aucune_donnee'
                }
            
            total_vendu = sum(row[0] for row in results)
            nombre_ventes = len(results)
            prix_moyen = sum(row[1] for row in results) / nombre_ventes
            derniere_vente = results[0][2] if results else None
            
            # Analyser la tendance (première vs dernière semaine)
            if len(results) >= 7:
                premiere_semaine = results[-7:]
                derniere_semaine = results[:7]
                
                ventes_premiere = sum(row[0] for row in premiere_semaine)
                ventes_derniere = sum(row[0] for row in derniere_semaine)
                
                if ventes_derniere > ventes_premiere * 1.1:
                    tendance = 'hausse'
                elif ventes_derniere < ventes_premiere * 0.9:
                    tendance = 'baisse'
                else:
                    tendance = 'stable'
            else:
                tendance = 'donnees_insuffisantes'
            
            return {
                'total_vendu': total_vendu,
                'nombre_ventes': nombre_ventes,
                'prix_moyen': prix_moyen,
                'derniere_vente': derniere_vente,
                'tendance': tendance
            }
            
        except Exception as e:
            logging.error(f"Erreur lors de la récupération des stats de vente: {e}")
            return None
    
    def generate_pricing_recommendations(self, produit, marge_actuelle, ecart_pourcentage, ventes_stats):
        """Génère des recommandations tarifaires"""
        recommandations = []
        
        # Recommandations basées sur la marge
        if marge_actuelle < 50:  # Marge trop faible
            recommandations.append({
                'type': 'warning',
                'message': f"Marge très faible ({marge_actuelle:.1f}%). Considérez une augmentation.",
                'action': 'augmenter_prix',
                'priorite': 'haute'
            })
        elif marge_actuelle > 300:  # Marge très élevée
            recommandations.append({
                'type': 'info',
                'message': f"Marge très élevée ({marge_actuelle:.1f}%). Risque de perte de compétitivité.",
                'action': 'reduire_prix',
                'priorite': 'moyenne'
            })
        
        # Recommandations basées sur l'écart avec le prix suggéré
        if abs(ecart_pourcentage) > 10:
            if ecart_pourcentage > 0:
                recommandations.append({
                    'type': 'info',
                    'message': f"Prix {ecart_pourcentage:.1f}% au-dessus du prix suggéré.",
                    'action': 'ajuster_prix',
                    'priorite': 'moyenne'
                })
            else:
                recommandations.append({
                    'type': 'warning',
                    'message': f"Prix {abs(ecart_pourcentage):.1f}% en-dessous du prix suggéré.",
                    'action': 'augmenter_prix',
                    'priorite': 'moyenne'
                })
        
        # Recommandations basées sur les ventes
        if ventes_stats:
            if ventes_stats['tendance'] == 'baisse' and marge_actuelle > 100:
                recommandations.append({
                    'type': 'suggestion',
                    'message': "Ventes en baisse. Une réduction de prix pourrait stimuler les ventes.",
                    'action': 'reduire_prix',
                    'priorite': 'moyenne'
                })
            elif ventes_stats['tendance'] == 'hausse' and marge_actuelle < 150:
                recommandations.append({
                    'type': 'opportunity',
                    'message': "Ventes en hausse. Opportunité d'augmentation de prix.",
                    'action': 'augmenter_prix',
                    'priorite': 'basse'
                })
            
            if ventes_stats['total_vendu'] == 0:
                recommandations.append({
                    'type': 'warning',
                    'message': "Aucune vente récente. Vérifiez la compétitivité du prix.",
                    'action': 'analyser_marche',
                    'priorite': 'haute'
                })
        
        return recommandations
    
    def bulk_price_update(self, produits, margin_adjustments=None):
        """Met à jour les prix en lot"""
        results = {
            'updated': 0,
            'errors': 0,
            'details': []
        }
        
        for produit in produits:
            try:
                # Calculer le nouveau prix
                margin = margin_adjustments.get(produit.id) if margin_adjustments else None
                nouveau_prix = self.calculate_suggested_price(produit, margin)
                
                if nouveau_prix and nouveau_prix != produit.prix_vente:
                    ancien_prix = produit.prix_vente
                    produit.prix_vente = nouveau_prix
                    produit.save()
                    
                    results['updated'] += 1
                    results['details'].append({
                        'produit_id': produit.id,
                        'nom': produit.nom,
                        'ancien_prix': ancien_prix,
                        'nouveau_prix': nouveau_prix,
                        'status': 'updated'
                    })
                    
                    logging.info(f"Prix mis à jour pour {produit.nom}: {ancien_prix}€ -> {nouveau_prix}€")
                else:
                    results['details'].append({
                        'produit_id': produit.id,
                        'nom': produit.nom,
                        'prix_actuel': produit.prix_vente,
                        'status': 'no_change'
                    })
                    
            except Exception as e:
                results['errors'] += 1
                results['details'].append({
                    'produit_id': produit.id,
                    'nom': produit.nom,
                    'error': str(e),
                    'status': 'error'
                })
                logging.error(f"Erreur lors de la mise à jour du prix pour {produit.nom}: {e}")
        
        return results
    
    def generate_pricing_report(self, category_id=None):
        """Génère un rapport de tarification"""
        try:
            from models.produit import Produit
            
            # Récupérer les produits
            if category_id:
                produits = Produit.find_all("categorie_id = ? AND actif = 1", (category_id,))
            else:
                produits = Produit.find_all("actif = 1")
            
            rapport = {
                'date_generation': datetime.now().isoformat(),
                'nombre_produits': len(produits),
                'analyses': [],
                'resume': {
                    'marge_moyenne': 0,
                    'produits_sous_evalues': 0,
                    'produits_sur_evalues': 0,
                    'produits_optimaux': 0
                }
            }
            
            marges = []
            
            for produit in produits:
                analyse = self.analyze_pricing_performance(produit)
                if analyse:
                    rapport['analyses'].append(analyse)
                    marges.append(analyse['marge_actuelle'])
                    
                    # Classifier les produits
                    if analyse['ecart_pourcentage'] < -10:
                        rapport['resume']['produits_sous_evalues'] += 1
                    elif analyse['ecart_pourcentage'] > 10:
                        rapport['resume']['produits_sur_evalues'] += 1
                    else:
                        rapport['resume']['produits_optimaux'] += 1
            
            # Calculer la marge moyenne
            if marges:
                rapport['resume']['marge_moyenne'] = sum(marges) / len(marges)
            
            return rapport
            
        except Exception as e:
            logging.error(f"Erreur lors de la génération du rapport de tarification: {e}")
            return None
    
    def update_pricing_rules(self, new_rules):
        """Met à jour les règles de tarification"""
        try:
            for key, value in new_rules.items():
                if key in self.pricing_rules:
                    self.pricing_rules[key] = value
                    logging.info(f"Règle de tarification mise à jour: {key} = {value}")
            
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des règles: {e}")
            return False
    
    def update_category_margins(self, new_margins):
        """Met à jour les marges par catégorie"""
        try:
            for category, margin in new_margins.items():
                if category in self.default_margins:
                    self.default_margins[category] = margin
                    logging.info(f"Marge mise à jour pour {category}: {margin}")
            
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des marges: {e}")
            return False

# Instance globale
pricing_manager = PricingManager()
