"""
Dialogue pour la création/modification d'approvisionnements
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.approvisionnement import Approvisionnement
from models.detail_approvisionnement import DetailApprovisionnement
from models.fournisseur import Fournisseur
from models.produit import Produit

class ApprovisionnementDialog:
    def __init__(self, parent, current_user, approvisionnement=None):
        self.parent = parent
        self.current_user = current_user
        self.approvisionnement = approvisionnement
        self.result = None
        self.details = []  # Liste des détails d'approvisionnement
        self.bon_livraison_path = None
        
        self.setup_dialog()
        
        if self.approvisionnement:
            self.load_approvisionnement_data()
    
    def setup_dialog(self):
        """Configure le dialogue"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Nouvel Approvisionnement" if not self.approvisionnement else "Modifier Approvisionnement")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Centrer la fenêtre
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
    
    def create_widgets(self):
        """Crée les widgets du dialogue"""
        # Titre
        title_frame = ttk.Frame(self.dialog)
        title_frame.pack(fill=tk.X, padx=20, pady=10)
        
        title_text = "📦 Nouvel Approvisionnement" if not self.approvisionnement else "📦 Modifier Approvisionnement"
        ttk.Label(title_frame, text=title_text, font=('Arial', 14, 'bold')).pack()
        
        # Notebook pour organiser les sections
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Onglet Informations générales
        info_frame = ttk.Frame(notebook)
        notebook.add(info_frame, text="Informations générales")
        
        # Onglet Produits
        produits_frame = ttk.Frame(notebook)
        notebook.add(produits_frame, text="Produits")
        
        # Onglet Documents
        docs_frame = ttk.Frame(notebook)
        notebook.add(docs_frame, text="Documents")
        
        self.create_info_tab(info_frame)
        self.create_produits_tab(produits_frame)
        self.create_docs_tab(docs_frame)
        
        # Boutons
        buttons_frame = ttk.Frame(self.dialog)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(buttons_frame, text="Annuler", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(buttons_frame, text="Enregistrer", 
                  command=self.save_approvisionnement).pack(side=tk.RIGHT)
    
    def create_info_tab(self, parent):
        """Crée l'onglet informations générales"""
        # Variables
        self.fournisseur_var = tk.StringVar()
        self.date_commande_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.date_livraison_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.numero_commande_var = tk.StringVar()
        self.statut_var = tk.StringVar(value="en_attente")
        self.notes_var = tk.StringVar()
        
        # Informations de base
        info_frame = ttk.LabelFrame(parent, text="Informations de base", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Fournisseur
        ttk.Label(info_frame, text="Fournisseur *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.fournisseur_combo = ttk.Combobox(info_frame, textvariable=self.fournisseur_var, 
                                             state="readonly", width=30)
        self.fournisseur_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Charger les fournisseurs
        self.load_fournisseurs()
        
        # Numéro de commande
        ttk.Label(info_frame, text="N° Commande:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        ttk.Entry(info_frame, textvariable=self.numero_commande_var, width=20).grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Dates
        ttk.Label(info_frame, text="Date commande *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.date_commande_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(info_frame, text="Date livraison *:").grid(row=1, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        ttk.Entry(info_frame, textvariable=self.date_livraison_var, width=15).grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Statut
        ttk.Label(info_frame, text="Statut:").grid(row=2, column=0, sticky=tk.W, pady=5)
        statut_combo = ttk.Combobox(info_frame, textvariable=self.statut_var,
                                   values=["en_attente", "commande", "livre", "facture"],
                                   state="readonly", width=15)
        statut_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Notes
        ttk.Label(info_frame, text="Notes:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.notes_var, width=50).grid(row=3, column=1, columnspan=3, sticky=tk.W, padx=(10, 0), pady=5)
    
    def create_produits_tab(self, parent):
        """Crée l'onglet produits"""
        # Ajout de produit
        add_frame = ttk.LabelFrame(parent, text="Ajouter un produit", padding="10")
        add_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Variables pour l'ajout
        self.produit_var = tk.StringVar()
        self.quantite_var = tk.StringVar()
        self.prix_unitaire_var = tk.StringVar()
        
        ttk.Label(add_frame, text="Produit:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.produit_combo = ttk.Combobox(add_frame, textvariable=self.produit_var, 
                                         state="readonly", width=30)
        self.produit_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        self.produit_combo.bind('<<ComboboxSelected>>', self.on_produit_selected)
        
        ttk.Label(add_frame, text="Quantité:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        ttk.Entry(add_frame, textvariable=self.quantite_var, width=10).grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Label(add_frame, text="Prix unitaire:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(add_frame, textvariable=self.prix_unitaire_var, width=15).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Button(add_frame, text="➕ Ajouter", 
                  command=self.ajouter_produit).grid(row=1, column=2, padx=(20, 0), pady=5)
        
        # Charger les produits
        self.load_produits()
        
        # Liste des produits
        list_frame = ttk.LabelFrame(parent, text="Produits commandés", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour les détails
        columns = ("Produit", "Quantité", "Prix unitaire", "Sous-total")
        self.details_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.details_tree.heading(col, text=col)
            self.details_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.details_tree.yview)
        self.details_tree.configure(yscrollcommand=scrollbar.set)
        
        self.details_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons pour la liste
        list_buttons_frame = ttk.Frame(list_frame)
        list_buttons_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(list_buttons_frame, text="🗑️ Supprimer", 
                  command=self.supprimer_produit).pack(side=tk.LEFT, padx=5)
        
        # Total
        self.total_label = ttk.Label(list_buttons_frame, text="Total: 0.00 €", 
                                    font=('Arial', 12, 'bold'))
        self.total_label.pack(side=tk.RIGHT, padx=5)
    
    def create_docs_tab(self, parent):
        """Crée l'onglet documents"""
        docs_frame = ttk.LabelFrame(parent, text="Documents joints", padding="10")
        docs_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Bon de livraison
        bon_frame = ttk.Frame(docs_frame)
        bon_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(bon_frame, text="Bon de livraison:").pack(side=tk.LEFT)
        
        self.bon_livraison_label = ttk.Label(bon_frame, text="Aucun fichier sélectionné", 
                                           foreground="gray")
        self.bon_livraison_label.pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Button(bon_frame, text="📁 Parcourir", 
                  command=self.select_bon_livraison).pack(side=tk.RIGHT)
        
        # Informations sur les formats acceptés
        info_frame = ttk.Frame(docs_frame)
        info_frame.pack(fill=tk.X, pady=10)
        
        info_text = "Formats acceptés: PDF, JPG, PNG\nTaille maximum: 10 MB"
        ttk.Label(info_frame, text=info_text, foreground="gray").pack()
    
    def load_fournisseurs(self):
        """Charge les fournisseurs dans le combobox"""
        try:
            fournisseurs = Fournisseur.find_all("actif = 1")
            fournisseur_values = [f"{f.nom} - {f.contact}" for f in fournisseurs]
            self.fournisseur_combo['values'] = fournisseur_values
            
            # Stocker les objets fournisseurs pour récupération
            self.fournisseurs_dict = {f"{f.nom} - {f.contact}": f for f in fournisseurs}
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des fournisseurs: {e}")
    
    def load_produits(self):
        """Charge les produits dans le combobox"""
        try:
            produits = Produit.find_all("actif = 1")
            produit_values = [f"{p.nom} ({p.unite})" for p in produits]
            self.produit_combo['values'] = produit_values
            
            # Stocker les objets produits pour récupération
            self.produits_dict = {f"{p.nom} ({p.unite})": p for p in produits}
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def on_produit_selected(self, event):
        """Appelé quand un produit est sélectionné"""
        produit_key = self.produit_var.get()
        if produit_key in self.produits_dict:
            produit = self.produits_dict[produit_key]
            # Pré-remplir le prix d'achat
            self.prix_unitaire_var.set(str(produit.prix_achat))
    
    def ajouter_produit(self):
        """Ajoute un produit à la liste"""
        try:
            produit_key = self.produit_var.get()
            quantite = float(self.quantite_var.get())
            prix_unitaire = float(self.prix_unitaire_var.get())
            
            if not produit_key or quantite <= 0 or prix_unitaire <= 0:
                messagebox.showerror("Erreur", "Veuillez remplir tous les champs avec des valeurs valides")
                return
            
            if produit_key not in self.produits_dict:
                messagebox.showerror("Erreur", "Produit non valide")
                return
            
            produit = self.produits_dict[produit_key]
            sous_total = quantite * prix_unitaire
            
            # Vérifier si le produit n'est pas déjà dans la liste
            for detail in self.details:
                if detail['produit_id'] == produit.id:
                    messagebox.showerror("Erreur", "Ce produit est déjà dans la liste")
                    return
            
            # Ajouter à la liste
            detail = {
                'produit_id': produit.id,
                'produit_nom': produit.nom,
                'quantite': quantite,
                'prix_unitaire': prix_unitaire,
                'sous_total': sous_total
            }
            
            self.details.append(detail)
            
            # Ajouter au treeview
            self.details_tree.insert("", "end", values=(
                produit.nom,
                f"{quantite} {produit.unite}",
                f"{prix_unitaire:.2f} €",
                f"{sous_total:.2f} €"
            ))
            
            # Réinitialiser les champs
            self.produit_var.set("")
            self.quantite_var.set("")
            self.prix_unitaire_var.set("")
            
            # Mettre à jour le total
            self.update_total()
            
        except ValueError:
            messagebox.showerror("Erreur", "Quantité et prix doivent être des nombres valides")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {e}")
    
    def supprimer_produit(self):
        """Supprime le produit sélectionné"""
        selection = self.details_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à supprimer")
            return
        
        # Récupérer l'index
        item = selection[0]
        index = self.details_tree.index(item)
        
        # Supprimer de la liste et du treeview
        del self.details[index]
        self.details_tree.delete(item)
        
        # Mettre à jour le total
        self.update_total()
    
    def update_total(self):
        """Met à jour le total"""
        total = sum(detail['sous_total'] for detail in self.details)
        self.total_label.config(text=f"Total: {total:.2f} €")
    
    def select_bon_livraison(self):
        """Sélectionne un fichier de bon de livraison"""
        filetypes = [
            ("Tous les fichiers supportés", "*.pdf *.jpg *.jpeg *.png"),
            ("PDF", "*.pdf"),
            ("Images", "*.jpg *.jpeg *.png")
        ]
        
        filename = filedialog.askopenfilename(
            title="Sélectionner le bon de livraison",
            filetypes=filetypes
        )
        
        if filename:
            # Vérifier la taille du fichier (10 MB max)
            file_size = os.path.getsize(filename)
            if file_size > 10 * 1024 * 1024:
                messagebox.showerror("Erreur", "Le fichier est trop volumineux (max 10 MB)")
                return
            
            self.bon_livraison_path = filename
            self.bon_livraison_label.config(text=os.path.basename(filename), foreground="black")
    
    def load_approvisionnement_data(self):
        """Charge les données d'un approvisionnement existant"""
        if not self.approvisionnement:
            return
        
        try:
            # Charger les informations de base
            fournisseur = Fournisseur.find_by_id(self.approvisionnement.fournisseur_id)
            if fournisseur:
                fournisseur_key = f"{fournisseur.nom} - {fournisseur.contact}"
                self.fournisseur_var.set(fournisseur_key)
            
            self.date_commande_var.set(self.approvisionnement.date_commande)
            self.date_livraison_var.set(self.approvisionnement.date_livraison or "")
            self.numero_commande_var.set(self.approvisionnement.numero_commande or "")
            self.statut_var.set(self.approvisionnement.statut)
            self.notes_var.set(self.approvisionnement.notes or "")
            
            # Charger les détails
            details = DetailApprovisionnement.find_all(f"approvisionnement_id = {self.approvisionnement.id}")
            
            for detail in details:
                produit = Produit.find_by_id(detail.produit_id)
                if produit:
                    detail_dict = {
                        'produit_id': produit.id,
                        'produit_nom': produit.nom,
                        'quantite': detail.quantite,
                        'prix_unitaire': detail.prix_unitaire,
                        'sous_total': detail.sous_total
                    }
                    
                    self.details.append(detail_dict)
                    
                    self.details_tree.insert("", "end", values=(
                        produit.nom,
                        f"{detail.quantite} {produit.unite}",
                        f"{detail.prix_unitaire:.2f} €",
                        f"{detail.sous_total:.2f} €"
                    ))
            
            self.update_total()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")
    
    def save_approvisionnement(self):
        """Sauvegarde l'approvisionnement"""
        try:
            # Validation
            if not self.fournisseur_var.get():
                messagebox.showerror("Erreur", "Veuillez sélectionner un fournisseur")
                return
            
            if not self.date_commande_var.get():
                messagebox.showerror("Erreur", "Veuillez saisir la date de commande")
                return
            
            if not self.details:
                messagebox.showerror("Erreur", "Veuillez ajouter au moins un produit")
                return
            
            # Récupérer le fournisseur
            fournisseur_key = self.fournisseur_var.get()
            if fournisseur_key not in self.fournisseurs_dict:
                messagebox.showerror("Erreur", "Fournisseur non valide")
                return
            
            fournisseur = self.fournisseurs_dict[fournisseur_key]
            
            # Créer ou mettre à jour l'approvisionnement
            if not self.approvisionnement:
                self.approvisionnement = Approvisionnement()
            
            self.approvisionnement.fournisseur_id = fournisseur.id
            self.approvisionnement.date_commande = self.date_commande_var.get()
            self.approvisionnement.date_livraison = self.date_livraison_var.get() or None
            self.approvisionnement.numero_commande = self.numero_commande_var.get() or None
            self.approvisionnement.statut = self.statut_var.get()
            self.approvisionnement.notes = self.notes_var.get() or None
            self.approvisionnement.utilisateur_id = self.current_user.id
            
            # Calculer le total
            total = sum(detail['sous_total'] for detail in self.details)
            self.approvisionnement.total = total
            
            # Sauvegarder
            approvisionnement_id = self.approvisionnement.save()
            
            # Sauvegarder les détails
            if approvisionnement_id:
                # Supprimer les anciens détails si modification
                if self.approvisionnement.id:
                    DetailApprovisionnement.delete_all(f"approvisionnement_id = {self.approvisionnement.id}")
                
                # Ajouter les nouveaux détails
                for detail in self.details:
                    detail_obj = DetailApprovisionnement()
                    detail_obj.approvisionnement_id = approvisionnement_id
                    detail_obj.produit_id = detail['produit_id']
                    detail_obj.quantite = detail['quantite']
                    detail_obj.prix_unitaire = detail['prix_unitaire']
                    detail_obj.sous_total = detail['sous_total']
                    detail_obj.save()
                
                # Si l'approvisionnement est livré, mettre à jour le stock
                if self.statut_var.get() == "livre":
                    self.update_stock()
                
                self.result = True
                messagebox.showinfo("Succès", "Approvisionnement sauvegardé avec succès")
                self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def update_stock(self):
        """Met à jour le stock des produits livrés"""
        try:
            from models.mouvement_stock import MouvementStock
            
            for detail in self.details:
                # Mettre à jour le stock du produit
                produit = Produit.find_by_id(detail['produit_id'])
                if produit:
                    produit.stock_actuel += detail['quantite']
                    produit.save()
                    
                    # Enregistrer le mouvement de stock
                    mouvement = MouvementStock()
                    mouvement.produit_id = produit.id
                    mouvement.type_mouvement = "entree"
                    mouvement.quantite = detail['quantite']
                    mouvement.prix_unitaire = detail['prix_unitaire']
                    mouvement.reference = f"Approvisionnement #{self.approvisionnement.id}"
                    mouvement.utilisateur_id = self.current_user.id
                    mouvement.save()
            
        except Exception as e:
            print(f"Erreur lors de la mise à jour du stock: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.result = False
        self.dialog.destroy()
