"""
Fenêtre de gestion des approvisionnements
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.approvisionnement import Approvisionnement
from models.fournisseur import Fournisseur

class ApprovisionnementWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        self.selected_approvisionnement = None
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion des Approvisionnements", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="➕ Nouveau", 
                  command=self.nouvel_approvisionnement).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="✏️ Modifier", 
                  command=self.modifier_approvisionnement).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="✅ Finaliser", 
                  command=self.finaliser_approvisionnement).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Filtres
        filters_frame = ttk.Frame(self)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtre par statut
        ttk.Label(filters_frame, text="Statut:").pack(side=tk.LEFT)
        self.statut_var = tk.StringVar(value="Tous")
        statut_combo = ttk.Combobox(filters_frame, textvariable=self.statut_var,
                                   values=["Tous", "Commande", "Reçu", "Facturé", "Annulé"],
                                   state="readonly", width=12)
        statut_combo.pack(side=tk.LEFT, padx=(5, 20))
        statut_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Filtre par fournisseur
        ttk.Label(filters_frame, text="Fournisseur:").pack(side=tk.LEFT)
        self.fournisseur_var = tk.StringVar(value="Tous")
        self.fournisseur_combo = ttk.Combobox(filters_frame, textvariable=self.fournisseur_var,
                                             state="readonly", width=20)
        self.fournisseur_combo.pack(side=tk.LEFT, padx=(5, 20))
        self.fournisseur_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Filtre par période
        ttk.Label(filters_frame, text="Période:").pack(side=tk.LEFT)
        self.periode_var = tk.StringVar(value="Ce mois")
        periode_combo = ttk.Combobox(filters_frame, textvariable=self.periode_var,
                                    values=["Tous", "Aujourd'hui", "Cette semaine", "Ce mois", "Mois dernier"],
                                    state="readonly", width=12)
        periode_combo.pack(side=tk.LEFT, padx=(5, 0))
        periode_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Frame principal avec deux panneaux
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Panneau gauche - Liste des approvisionnements
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Panneau droit - Détails de l'approvisionnement
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_approvisionnements_list(left_frame)
        self.setup_approvisionnement_details(right_frame)
    
    def setup_approvisionnements_list(self, parent):
        """Configure la liste des approvisionnements"""
        # Treeview pour les approvisionnements
        columns = ("ID", "Date", "Fournisseur", "N° Facture", "Total TTC", "Statut", "Utilisateur")
        self.appro_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        self.appro_tree.heading("ID", text="ID")
        self.appro_tree.heading("Date", text="Date")
        self.appro_tree.heading("Fournisseur", text="Fournisseur")
        self.appro_tree.heading("N° Facture", text="N° Facture")
        self.appro_tree.heading("Total TTC", text="Total TTC")
        self.appro_tree.heading("Statut", text="Statut")
        self.appro_tree.heading("Utilisateur", text="Utilisateur")
        
        # Largeur des colonnes
        self.appro_tree.column("ID", width=50)
        self.appro_tree.column("Date", width=100)
        self.appro_tree.column("Fournisseur", width=150)
        self.appro_tree.column("N° Facture", width=120)
        self.appro_tree.column("Total TTC", width=100)
        self.appro_tree.column("Statut", width=80)
        self.appro_tree.column("Utilisateur", width=120)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.appro_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.appro_tree.xview)
        self.appro_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.appro_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind de sélection
        self.appro_tree.bind('<<TreeviewSelect>>', self.on_approvisionnement_select)
        self.appro_tree.bind('<Double-1>', self.modifier_approvisionnement)
    
    def setup_approvisionnement_details(self, parent):
        """Configure le panneau des détails de l'approvisionnement"""
        details_frame = ttk.LabelFrame(parent, text="Détails de l'approvisionnement", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations générales
        info_frame = ttk.LabelFrame(details_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_labels = {}
        
        # Créer les labels d'information
        fields = [
            ("N° Facture", "numero_facture"),
            ("Fournisseur", "fournisseur"),
            ("Date livraison", "date_livraison"),
            ("Statut", "statut"),
            ("Utilisateur", "utilisateur")
        ]
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(info_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Totaux
        totaux_frame = ttk.LabelFrame(details_frame, text="Totaux", padding="10")
        totaux_frame.pack(fill=tk.X, pady=(0, 10))
        
        totaux_fields = [
            ("Total HT", "total_ht"),
            ("Total TTC", "total_ttc")
        ]
        
        for i, (label, key) in enumerate(totaux_fields):
            ttk.Label(totaux_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(totaux_frame, text="-", font=('Arial', 10, 'bold'))
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Détails des produits
        produits_frame = ttk.LabelFrame(details_frame, text="Produits", padding="5")
        produits_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview pour les produits
        prod_columns = ("Produit", "Quantité", "Prix unit.", "Total")
        self.produits_tree = ttk.Treeview(produits_frame, columns=prod_columns, show="headings", height=6)
        
        for col in prod_columns:
            self.produits_tree.heading(col, text=col)
            self.produits_tree.column(col, width=80)
        
        prod_scrollbar = ttk.Scrollbar(produits_frame, orient=tk.VERTICAL, command=self.produits_tree.yview)
        self.produits_tree.configure(yscrollcommand=prod_scrollbar.set)
        
        self.produits_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        prod_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Actions sur l'approvisionnement sélectionné
        actions_frame = ttk.LabelFrame(details_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X)
        
        ttk.Button(actions_frame, text="📝 Modifier", 
                  command=self.modifier_approvisionnement).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="✅ Finaliser", 
                  command=self.finaliser_approvisionnement).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="🖨️ Imprimer", 
                  command=self.imprimer_approvisionnement).pack(fill=tk.X, pady=2)
    
    def load_data(self):
        """Charge les données"""
        self.load_fournisseurs()
        self.load_approvisionnements()
    
    def load_fournisseurs(self):
        """Charge les fournisseurs dans le combobox"""
        try:
            fournisseurs = Fournisseur.find_all()
            fournisseur_values = ["Tous"] + [f.nom for f in fournisseurs]
            self.fournisseur_combo['values'] = fournisseur_values
            if not self.fournisseur_var.get() or self.fournisseur_var.get() not in fournisseur_values:
                self.fournisseur_var.set("Tous")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des fournisseurs: {e}")
    
    def load_approvisionnements(self):
        """Charge les approvisionnements dans le treeview"""
        # Nettoyer le treeview
        for item in self.appro_tree.get_children():
            self.appro_tree.delete(item)
        
        try:
            # Appliquer les filtres
            approvisionnements = self.get_filtered_approvisionnements()
            
            # Ajouter les approvisionnements au treeview
            for appro in approvisionnements:
                statut_icon = {
                    "commande": "📋",
                    "recu": "📦", 
                    "facture": "✅",
                    "annule": "❌"
                }.get(appro.statut, "")
                
                self.appro_tree.insert("", "end", values=(
                    appro.id,
                    appro.date_livraison,
                    appro.get_fournisseur_nom(),
                    appro.numero_facture or "-",
                    f"{appro.total_ttc:.2f} €",
                    f"{statut_icon} {appro.statut.title()}",
                    appro.get_utilisateur_nom()
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des approvisionnements: {e}")
    
    def get_filtered_approvisionnements(self):
        """Retourne les approvisionnements filtrés"""
        # Filtre par période
        date_debut, date_fin = self.get_date_range()
        
        if date_debut and date_fin:
            approvisionnements = Approvisionnement.find_approvisionnements_periode(date_debut, date_fin)
        else:
            approvisionnements = Approvisionnement.find_all()
        
        # Filtre par statut
        statut_filter = self.statut_var.get()
        if statut_filter != "Tous":
            statut_mapping = {
                "Commande": "commande",
                "Reçu": "recu", 
                "Facturé": "facture",
                "Annulé": "annule"
            }
            statut_value = statut_mapping.get(statut_filter)
            if statut_value:
                approvisionnements = [a for a in approvisionnements if a.statut == statut_value]
        
        # Filtre par fournisseur
        fournisseur_filter = self.fournisseur_var.get()
        if fournisseur_filter != "Tous":
            approvisionnements = [a for a in approvisionnements if a.get_fournisseur_nom() == fournisseur_filter]
        
        # Trier par date décroissante
        return sorted(approvisionnements, key=lambda x: x.date_livraison or "", reverse=True)
    
    def get_date_range(self):
        """Retourne la plage de dates selon le filtre de période"""
        periode = self.periode_var.get()
        today = datetime.now().date()
        
        if periode == "Aujourd'hui":
            return today, today
        elif periode == "Cette semaine":
            start = today - timedelta(days=today.weekday())
            return start, today
        elif periode == "Ce mois":
            start = today.replace(day=1)
            return start, today
        elif periode == "Mois dernier":
            first_this_month = today.replace(day=1)
            last_month = first_this_month - timedelta(days=1)
            start = last_month.replace(day=1)
            return start, last_month
        else:  # "Tous"
            return None, None
    
    def on_filter_change(self, event):
        """Appelé quand un filtre change"""
        self.load_approvisionnements()
    
    def on_approvisionnement_select(self, event):
        """Appelé quand un approvisionnement est sélectionné"""
        selection = self.appro_tree.selection()
        if selection:
            item = self.appro_tree.item(selection[0])
            appro_id = item['values'][0]
            self.load_approvisionnement_details(appro_id)
    
    def load_approvisionnement_details(self, appro_id):
        """Charge les détails d'un approvisionnement"""
        try:
            appro = Approvisionnement.find_by_id(appro_id)
            if appro:
                self.selected_approvisionnement = appro
                
                # Mettre à jour les labels
                self.detail_labels['numero_facture'].config(text=appro.numero_facture or "-")
                self.detail_labels['fournisseur'].config(text=appro.get_fournisseur_nom())
                self.detail_labels['date_livraison'].config(text=appro.date_livraison or "-")
                self.detail_labels['statut'].config(text=appro.statut.title())
                self.detail_labels['utilisateur'].config(text=appro.get_utilisateur_nom())
                
                self.detail_labels['total_ht'].config(text=f"{appro.total_ht:.2f} €")
                self.detail_labels['total_ttc'].config(text=f"{appro.total_ttc:.2f} €")
                
                # Charger les produits
                self.load_approvisionnement_produits(appro)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des détails: {e}")
    
    def load_approvisionnement_produits(self, appro):
        """Charge les produits de l'approvisionnement"""
        # Nettoyer le treeview
        for item in self.produits_tree.get_children():
            self.produits_tree.delete(item)
        
        for detail in appro.details:
            self.produits_tree.insert("", "end", values=(
                detail.get_produit_nom(),
                f"{detail.quantite} {detail.get_produit_unite()}",
                f"{detail.prix_unitaire:.2f} €",
                f"{detail.sous_total:.2f} €"
            ))
    
    def nouvel_approvisionnement(self):
        """Crée un nouvel approvisionnement"""
        try:
            from gui.dialogs.approvisionnement_dialog import ApprovisionnementDialog
            dialog = ApprovisionnementDialog(self, self.current_user)
            if dialog.result:
                self.load_data()
                messagebox.showinfo("Succès", "Approvisionnement créé avec succès")
        except ImportError:
            messagebox.showinfo("Info", "Dialogue d'approvisionnement non disponible")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def modifier_approvisionnement(self):
        """Modifie l'approvisionnement sélectionné"""
        if not self.selected_approvisionnement:
            messagebox.showwarning("Attention", "Veuillez sélectionner un approvisionnement à modifier")
            return

        try:
            from gui.dialogs.approvisionnement_dialog import ApprovisionnementDialog
            dialog = ApprovisionnementDialog(self, self.current_user, self.selected_approvisionnement)
            if dialog.result:
                self.load_data()
                messagebox.showinfo("Succès", "Approvisionnement modifié avec succès")
        except ImportError:
            messagebox.showinfo("Info", "Dialogue d'approvisionnement non disponible")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur: {e}")
    
    def finaliser_approvisionnement(self):
        """Finalise l'approvisionnement sélectionné"""
        if not self.selected_approvisionnement:
            messagebox.showwarning("Attention", "Veuillez sélectionner un approvisionnement à finaliser")
            return
        
        if self.selected_approvisionnement.statut == "facture":
            messagebox.showinfo("Info", "Cet approvisionnement est déjà finalisé")
            return
        
        if messagebox.askyesno("Confirmation", 
                              f"Finaliser l'approvisionnement {self.selected_approvisionnement.numero_facture} ?\n"
                              "Cette action mettra à jour les stocks."):
            try:
                self.selected_approvisionnement.finaliser_approvisionnement()
                self.load_approvisionnements()
                self.load_approvisionnement_details(self.selected_approvisionnement.id)
                messagebox.showinfo("Succès", "Approvisionnement finalisé avec succès")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la finalisation: {e}")
    
    def imprimer_approvisionnement(self):
        """Imprime l'approvisionnement sélectionné"""
        if not self.selected_approvisionnement:
            messagebox.showwarning("Attention", "Veuillez sélectionner un approvisionnement à imprimer")
            return
        
        messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Imprimer {self.selected_approvisionnement.numero_facture}")
