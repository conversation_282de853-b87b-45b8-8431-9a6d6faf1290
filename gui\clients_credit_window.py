"""
Fenêtre de gestion des clients à crédit
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.client_credit import ClientCredit
from models.vente import Vente

class ClientsCreditWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        self.selected_client = None
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Gestion des Clients à Crédit", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="➕ Nouveau client", 
                  command=self.nouveau_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="✏️ Modifier", 
                  command=self.modifier_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="💰 Paiement", 
                  command=self.enregistrer_paiement).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Filtres
        filters_frame = ttk.Frame(self)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtre par statut
        ttk.Label(filters_frame, text="Afficher:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="Tous")
        filter_combo = ttk.Combobox(filters_frame, textvariable=self.filter_var,
                                   values=["Tous", "Avec crédit", "Limite atteinte", "Sans dette"],
                                   state="readonly", width=15)
        filter_combo.pack(side=tk.LEFT, padx=(5, 20))
        filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # Recherche
        ttk.Label(filters_frame, text="Rechercher:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(filters_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # Frame principal avec deux panneaux
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Panneau gauche - Liste des clients
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Panneau droit - Détails du client
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_clients_list(left_frame)
        self.setup_client_details(right_frame)
    
    def setup_clients_list(self, parent):
        """Configure la liste des clients"""
        # Treeview pour les clients
        columns = ("ID", "Nom", "Téléphone", "Limite", "Utilisé", "Disponible", "Statut")
        self.clients_tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # Configuration des colonnes
        self.clients_tree.heading("ID", text="ID")
        self.clients_tree.heading("Nom", text="Nom")
        self.clients_tree.heading("Téléphone", text="Téléphone")
        self.clients_tree.heading("Limite", text="Limite")
        self.clients_tree.heading("Utilisé", text="Utilisé")
        self.clients_tree.heading("Disponible", text="Disponible")
        self.clients_tree.heading("Statut", text="Statut")
        
        # Largeur des colonnes
        self.clients_tree.column("ID", width=50)
        self.clients_tree.column("Nom", width=150)
        self.clients_tree.column("Téléphone", width=120)
        self.clients_tree.column("Limite", width=80)
        self.clients_tree.column("Utilisé", width=80)
        self.clients_tree.column("Disponible", width=80)
        self.clients_tree.column("Statut", width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.clients_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.clients_tree.xview)
        self.clients_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.clients_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind de sélection
        self.clients_tree.bind('<<TreeviewSelect>>', self.on_client_select)
        self.clients_tree.bind('<Double-1>', self.modifier_client)
    
    def setup_client_details(self, parent):
        """Configure le panneau des détails du client"""
        details_frame = ttk.LabelFrame(parent, text="Détails du client", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations générales
        info_frame = ttk.LabelFrame(details_frame, text="Informations", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.detail_labels = {}
        
        # Créer les labels d'information
        fields = [
            ("Nom", "nom"),
            ("Téléphone", "telephone"),
            ("Créé le", "created_at")
        ]
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(info_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(info_frame, text="-")
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Informations de crédit
        credit_frame = ttk.LabelFrame(details_frame, text="Crédit", padding="10")
        credit_frame.pack(fill=tk.X, pady=(0, 10))
        
        credit_fields = [
            ("Limite de crédit", "limite_credit"),
            ("Crédit utilisé", "credit_utilise"),
            ("Crédit disponible", "credit_disponible"),
            ("Statut", "statut_credit")
        ]
        
        for i, (label, key) in enumerate(credit_fields):
            ttk.Label(credit_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
            self.detail_labels[key] = ttk.Label(credit_frame, text="-", font=('Arial', 10, 'bold'))
            self.detail_labels[key].grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Historique des ventes à crédit
        ventes_frame = ttk.LabelFrame(details_frame, text="Ventes à crédit récentes", padding="5")
        ventes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Treeview pour les ventes
        vente_columns = ("Date", "Ticket", "Montant")
        self.ventes_tree = ttk.Treeview(ventes_frame, columns=vente_columns, show="headings", height=6)
        
        for col in vente_columns:
            self.ventes_tree.heading(col, text=col)
            self.ventes_tree.column(col, width=80)
        
        ventes_scrollbar = ttk.Scrollbar(ventes_frame, orient=tk.VERTICAL, command=self.ventes_tree.yview)
        self.ventes_tree.configure(yscrollcommand=ventes_scrollbar.set)
        
        self.ventes_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ventes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Actions sur le client sélectionné
        actions_frame = ttk.LabelFrame(details_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X)
        
        ttk.Button(actions_frame, text="📝 Modifier", 
                  command=self.modifier_client).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="💰 Paiement", 
                  command=self.enregistrer_paiement).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="📊 Historique complet", 
                  command=self.voir_historique_complet).pack(fill=tk.X, pady=2)
    
    def load_data(self):
        """Charge les données des clients"""
        self.load_clients()
    
    def load_clients(self):
        """Charge les clients dans le treeview"""
        # Nettoyer le treeview
        for item in self.clients_tree.get_children():
            self.clients_tree.delete(item)
        
        try:
            # Appliquer les filtres
            clients = self.get_filtered_clients()
            
            # Ajouter les clients au treeview
            for client in clients:
                credit_disponible = client.get_credit_disponible()
                
                # Déterminer le statut
                if not client.actif:
                    statut = "❌ Inactif"
                elif client.credit_utilise == 0:
                    statut = "✅ Sans dette"
                elif client.is_limite_atteinte():
                    statut = "🔴 Limite atteinte"
                elif credit_disponible < (client.limite_credit * 0.2):  # Moins de 20% disponible
                    statut = "🟡 Proche limite"
                else:
                    statut = "💳 Crédit OK"
                
                self.clients_tree.insert("", "end", values=(
                    client.id,
                    client.nom,
                    client.telephone or "-",
                    f"{client.limite_credit:.2f} €",
                    f"{client.credit_utilise:.2f} €",
                    f"{credit_disponible:.2f} €",
                    statut
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des clients: {e}")
    
    def get_filtered_clients(self):
        """Retourne les clients filtrés"""
        # Récupérer tous les clients actifs
        clients = ClientCredit.find_all("actif = 1")
        
        # Filtre par recherche
        search_term = self.search_var.get().strip().lower()
        if search_term:
            clients = [c for c in clients if search_term in c.nom.lower() or 
                      (c.telephone and search_term in c.telephone.lower())]
        
        # Filtre par statut
        filter_value = self.filter_var.get()
        if filter_value == "Avec crédit":
            clients = [c for c in clients if c.credit_utilise > 0]
        elif filter_value == "Limite atteinte":
            clients = [c for c in clients if c.is_limite_atteinte()]
        elif filter_value == "Sans dette":
            clients = [c for c in clients if c.credit_utilise == 0]
        
        # Trier par nom
        return sorted(clients, key=lambda x: x.nom.lower())
    
    def on_filter_change(self, event):
        """Appelé quand le filtre change"""
        self.load_clients()
    
    def on_search_change(self, *args):
        """Appelé quand le texte de recherche change"""
        self.load_clients()
    
    def on_client_select(self, event):
        """Appelé quand un client est sélectionné"""
        selection = self.clients_tree.selection()
        if selection:
            item = self.clients_tree.item(selection[0])
            client_id = item['values'][0]
            self.load_client_details(client_id)
    
    def load_client_details(self, client_id):
        """Charge les détails d'un client"""
        try:
            client = ClientCredit.find_by_id(client_id)
            if client:
                self.selected_client = client
                
                # Mettre à jour les labels
                self.detail_labels['nom'].config(text=client.nom)
                self.detail_labels['telephone'].config(text=client.telephone or "-")
                self.detail_labels['created_at'].config(text=client.created_at)
                
                self.detail_labels['limite_credit'].config(text=f"{client.limite_credit:.2f} €")
                self.detail_labels['credit_utilise'].config(text=f"{client.credit_utilise:.2f} €")
                
                credit_disponible = client.get_credit_disponible()
                self.detail_labels['credit_disponible'].config(text=f"{credit_disponible:.2f} €")
                
                # Couleur selon le crédit disponible
                if client.is_limite_atteinte():
                    self.detail_labels['credit_disponible'].config(foreground="red")
                    self.detail_labels['statut_credit'].config(text="🔴 Limite atteinte", foreground="red")
                elif credit_disponible < (client.limite_credit * 0.2):
                    self.detail_labels['credit_disponible'].config(foreground="orange")
                    self.detail_labels['statut_credit'].config(text="🟡 Proche limite", foreground="orange")
                else:
                    self.detail_labels['credit_disponible'].config(foreground="green")
                    self.detail_labels['statut_credit'].config(text="✅ OK", foreground="green")
                
                # Charger les ventes récentes
                self.load_client_ventes(client)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des détails: {e}")
    
    def load_client_ventes(self, client):
        """Charge les ventes récentes du client"""
        # Nettoyer le treeview
        for item in self.ventes_tree.get_children():
            self.ventes_tree.delete(item)
        
        try:
            ventes = client.get_ventes_credit()
            # Prendre les 10 plus récentes
            ventes_recentes = sorted(ventes, key=lambda x: x.date_vente, reverse=True)[:10]
            
            for vente in ventes_recentes:
                self.ventes_tree.insert("", "end", values=(
                    vente.date_vente,
                    vente.numero_ticket,
                    f"{vente.total_ttc:.2f} €"
                ))
                
        except Exception as e:
            print(f"Erreur lors du chargement des ventes: {e}")
    
    def nouveau_client(self):
        """Ouvre la fenêtre de création d'un nouveau client"""
        try:
            from gui.dialogs.client_credit_dialog import ClientCreditDialog
            dialog = ClientCreditDialog(self, "Nouveau client à crédit")
            if dialog.result:
                self.load_clients()
                messagebox.showinfo("Succès", "Client créé avec succès")
        except ImportError:
            messagebox.showinfo("Info", "Fonctionnalité à implémenter: Nouveau client")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création du client: {e}")
    
    def modifier_client(self):
        """Ouvre la fenêtre de modification du client sélectionné"""
        if not self.selected_client:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à modifier")
            return
        
        try:
            from gui.dialogs.client_credit_dialog import ClientCreditDialog
            dialog = ClientCreditDialog(self, f"Modifier {self.selected_client.nom}", self.selected_client)
            if dialog.result:
                self.load_clients()
                self.load_client_details(self.selected_client.id)
                messagebox.showinfo("Succès", "Client modifié avec succès")
        except ImportError:
            messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Modifier {self.selected_client.nom}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la modification du client: {e}")
    
    def enregistrer_paiement(self):
        """Enregistre un paiement de crédit"""
        if not self.selected_client:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client")
            return
        
        if self.selected_client.credit_utilise <= 0:
            messagebox.showinfo("Info", "Ce client n'a pas de dette à rembourser")
            return
        
        try:
            from gui.dialogs.paiement_credit_dialog import PaiementCreditDialog
            dialog = PaiementCreditDialog(self, self.selected_client, self.current_user)
            if dialog.result:
                self.load_clients()
                self.load_client_details(self.selected_client.id)
        except ImportError:
            messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Paiement pour {self.selected_client.nom}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement du paiement: {e}")
    
    def voir_historique_complet(self):
        """Affiche l'historique complet du client"""
        if not self.selected_client:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client")
            return
        
        messagebox.showinfo("Info", f"Fonctionnalité à implémenter: Historique complet de {self.selected_client.nom}")
