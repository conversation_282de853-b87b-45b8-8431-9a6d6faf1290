"""
Dialogue pour la création/modification de produits
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.produit import Produit
from models.categorie import Categorie
from models.fournisseur import Fournisseur
from config import UNITS

class ProduitDialog:
    def __init__(self, parent, title="Nouveau produit", produit=None):
        self.result = None
        self.produit = produit
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
        
        # Charger les données si modification
        if self.produit:
            self.load_product_data()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"500x600+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.nom_var = tk.StringVar()
        self.code_barre_var = tk.StringVar()
        self.categorie_var = tk.StringVar()
        self.unite_var = tk.StringVar(value="unité")
        self.prix_achat_var = tk.StringVar(value="0.00")
        self.prix_vente_var = tk.StringVar(value="0.00")
        self.stock_actuel_var = tk.StringVar(value="0")
        self.seuil_minimum_var = tk.StringVar(value="5")
        self.fournisseur_var = tk.StringVar()
        self.actif_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal avec scroll
        canvas = tk.Canvas(self.window)
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Nom du produit
        ttk.Label(info_frame, text="Nom du produit *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(info_frame, textvariable=self.nom_var, width=40)
        nom_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        nom_entry.focus()
        
        # Code-barres
        ttk.Label(info_frame, text="Code-barres:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.code_barre_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        ttk.Button(info_frame, text="Générer", command=self.generer_code_barre).grid(row=1, column=2, pady=5, padx=(5, 0))
        
        # Catégorie
        ttk.Label(info_frame, text="Catégorie:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.categorie_combo = ttk.Combobox(info_frame, textvariable=self.categorie_var, state="readonly", width=25)
        self.categorie_combo.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        ttk.Button(info_frame, text="Nouvelle", command=self.nouvelle_categorie).grid(row=2, column=2, pady=5, padx=(5, 0))
        
        # Unité
        ttk.Label(info_frame, text="Unité *:").grid(row=3, column=0, sticky=tk.W, pady=5)
        unite_combo = ttk.Combobox(info_frame, textvariable=self.unite_var, values=UNITS, width=15)
        unite_combo.grid(row=3, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Fournisseur
        ttk.Label(info_frame, text="Fournisseur:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.fournisseur_combo = ttk.Combobox(info_frame, textvariable=self.fournisseur_var, state="readonly", width=25)
        self.fournisseur_combo.grid(row=4, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        ttk.Button(info_frame, text="Nouveau", command=self.nouveau_fournisseur).grid(row=4, column=2, pady=5, padx=(5, 0))
        
        # Configurer les colonnes
        info_frame.columnconfigure(1, weight=1)
        
        # Informations financières
        finance_frame = ttk.LabelFrame(main_frame, text="Informations financières", padding="10")
        finance_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Prix d'achat
        ttk.Label(finance_frame, text="Prix d'achat (€) *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        prix_achat_entry = ttk.Entry(finance_frame, textvariable=self.prix_achat_var, width=15)
        prix_achat_entry.grid(row=0, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        prix_achat_entry.bind('<FocusOut>', self.calculer_marge)
        
        # Prix de vente
        ttk.Label(finance_frame, text="Prix de vente (€) *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        prix_vente_entry = ttk.Entry(finance_frame, textvariable=self.prix_vente_var, width=15)
        prix_vente_entry.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        prix_vente_entry.bind('<FocusOut>', self.calculer_marge)
        
        # Marge calculée
        ttk.Label(finance_frame, text="Marge:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.marge_label = ttk.Label(finance_frame, text="0.0%", font=('Arial', 10, 'bold'))
        self.marge_label.grid(row=2, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Informations de stock
        stock_frame = ttk.LabelFrame(main_frame, text="Informations de stock", padding="10")
        stock_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Stock actuel
        ttk.Label(stock_frame, text="Stock actuel:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(stock_frame, textvariable=self.stock_actuel_var, width=15).grid(row=0, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Seuil minimum
        ttk.Label(stock_frame, text="Seuil minimum *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(stock_frame, textvariable=self.seuil_minimum_var, width=15).grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        # Statut
        status_frame = ttk.LabelFrame(main_frame, text="Statut", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Checkbutton(status_frame, text="Produit actif", variable=self.actif_var).pack(anchor=tk.W)
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Enregistrer", command=self.save).pack(side=tk.RIGHT)
        
        # Pack du canvas et scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Charger les données des combobox
        self.load_combobox_data()
    
    def load_combobox_data(self):
        """Charge les données des combobox"""
        try:
            # Catégories
            categories = Categorie.find_all()
            cat_values = [""] + [cat.nom for cat in categories]
            self.categorie_combo['values'] = cat_values
            
            # Fournisseurs
            fournisseurs = Fournisseur.find_all()
            fourn_values = [""] + [f.nom for f in fournisseurs]
            self.fournisseur_combo['values'] = fourn_values
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")
    
    def load_product_data(self):
        """Charge les données du produit pour modification"""
        if not self.produit:
            return
        
        self.nom_var.set(self.produit.nom)
        self.code_barre_var.set(self.produit.code_barre or "")
        self.unite_var.set(self.produit.unite)
        self.prix_achat_var.set(f"{self.produit.prix_achat:.2f}")
        self.prix_vente_var.set(f"{self.produit.prix_vente:.2f}")
        self.stock_actuel_var.set(str(self.produit.stock_actuel))
        self.seuil_minimum_var.set(str(self.produit.seuil_minimum))
        self.actif_var.set(self.produit.actif)
        
        # Catégorie
        if self.produit.categorie_id:
            cat_nom = self.produit.get_categorie_nom()
            self.categorie_var.set(cat_nom)
        
        # Fournisseur
        if self.produit.fournisseur_id:
            fourn_nom = self.produit.get_fournisseur_nom()
            self.fournisseur_var.set(fourn_nom)
        
        # Calculer la marge
        self.calculer_marge()
    
    def calculer_marge(self, event=None):
        """Calcule et affiche la marge"""
        try:
            prix_achat = float(self.prix_achat_var.get() or 0)
            prix_vente = float(self.prix_vente_var.get() or 0)
            
            if prix_achat > 0:
                marge = ((prix_vente - prix_achat) / prix_achat) * 100
                self.marge_label.config(text=f"{marge:.1f}%")
                
                # Couleur selon la marge
                if marge < 20:
                    self.marge_label.config(foreground="red")
                elif marge < 50:
                    self.marge_label.config(foreground="orange")
                else:
                    self.marge_label.config(foreground="green")
            else:
                self.marge_label.config(text="0.0%", foreground="black")
                
        except ValueError:
            self.marge_label.config(text="N/A", foreground="black")
    
    def generer_code_barre(self):
        """Génère un code-barres automatique"""
        import random
        code = "".join([str(random.randint(0, 9)) for _ in range(13)])
        self.code_barre_var.set(code)
    
    def nouvelle_categorie(self):
        """Ouvre le dialogue de création de catégorie"""
        from gui.dialogs.categorie_dialog import CategorieDialog
        dialog = CategorieDialog(self.window, "Nouvelle catégorie")
        if dialog.result:
            # Recharger les catégories
            self.load_combobox_data()
            self.categorie_var.set(dialog.result['nom'])
    
    def nouveau_fournisseur(self):
        """Ouvre le dialogue de création de fournisseur"""
        from gui.dialogs.fournisseur_dialog import FournisseurDialog
        dialog = FournisseurDialog(self.window, "Nouveau fournisseur")
        if dialog.result:
            # Recharger les fournisseurs
            self.load_combobox_data()
            self.fournisseur_var.set(dialog.result['nom'])
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Nom obligatoire
        if not self.nom_var.get().strip():
            errors.append("Le nom du produit est obligatoire")
        
        # Unité obligatoire
        if not self.unite_var.get().strip():
            errors.append("L'unité est obligatoire")
        
        # Prix valides
        try:
            prix_achat = float(self.prix_achat_var.get())
            if prix_achat < 0:
                errors.append("Le prix d'achat doit être positif")
        except ValueError:
            errors.append("Le prix d'achat doit être un nombre valide")
        
        try:
            prix_vente = float(self.prix_vente_var.get())
            if prix_vente < 0:
                errors.append("Le prix de vente doit être positif")
        except ValueError:
            errors.append("Le prix de vente doit être un nombre valide")
        
        # Stock valide
        try:
            stock = int(self.stock_actuel_var.get())
            if stock < 0:
                errors.append("Le stock doit être positif ou nul")
        except ValueError:
            errors.append("Le stock doit être un nombre entier valide")
        
        # Seuil valide
        try:
            seuil = int(self.seuil_minimum_var.get())
            if seuil < 0:
                errors.append("Le seuil minimum doit être positif ou nul")
        except ValueError:
            errors.append("Le seuil minimum doit être un nombre entier valide")
        
        # Code-barres unique (si fourni)
        code_barre = self.code_barre_var.get().strip()
        if code_barre:
            existing = Produit.find_by_code_barre(code_barre)
            if existing and (not self.produit or existing.id != self.produit.id):
                errors.append("Ce code-barres est déjà utilisé par un autre produit")
        
        return errors
    
    def save(self):
        """Sauvegarde le produit"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            # Créer ou modifier le produit
            if self.produit:
                produit = self.produit
            else:
                produit = Produit()
            
            # Remplir les données
            produit.nom = self.nom_var.get().strip()
            produit.code_barre = self.code_barre_var.get().strip() or None
            produit.unite = self.unite_var.get().strip()
            produit.prix_achat = float(self.prix_achat_var.get())
            produit.prix_vente = float(self.prix_vente_var.get())
            produit.stock_actuel = int(self.stock_actuel_var.get())
            produit.seuil_minimum = int(self.seuil_minimum_var.get())
            produit.actif = self.actif_var.get()
            
            # Catégorie
            cat_nom = self.categorie_var.get().strip()
            if cat_nom:
                categories = Categorie.find_all("nom = ?", (cat_nom,))
                if categories:
                    produit.categorie_id = categories[0].id
                else:
                    produit.categorie_id = None
            else:
                produit.categorie_id = None
            
            # Fournisseur
            fourn_nom = self.fournisseur_var.get().strip()
            if fourn_nom:
                fournisseurs = Fournisseur.find_all("nom = ?", (fourn_nom,))
                if fournisseurs:
                    produit.fournisseur_id = fournisseurs[0].id
                else:
                    produit.fournisseur_id = None
            else:
                produit.fournisseur_id = None
            
            # Sauvegarder
            produit.save()
            
            self.result = produit
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
