#!/bin/bash

echo "========================================"
echo "  Application de Gestion de Stock - Bar"
echo "========================================"
echo ""
echo "Démarrage de l'application..."
echo ""

# Vérifier si Python est installé
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERREUR: Python n'est pas installé"
        echo "Veuillez installer Python 3.7 ou supérieur"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Afficher la version de Python
echo "Version de Python:"
$PYTHON_CMD --version
echo ""

# Lancer l'application
$PYTHON_CMD main.py

# Si l'application se ferme avec une erreur, afficher un message
if [ $? -ne 0 ]; then
    echo ""
    echo "L'application s'est fermée avec une erreur."
    echo "Consultez le fichier bar_stock.log pour plus de détails."
    read -p "Appuyez sur Entrée pour continuer..."
fi
