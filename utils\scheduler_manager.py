"""
Gestionnaire de planification automatique des tâches
"""
import threading
import time
from datetime import datetime, timedelta
import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

class SchedulerManager:
    def __init__(self):
        self.scheduler = None
        self.jobs = {}
        self.running = False
        
        try:
            self.scheduler = BackgroundScheduler()
            self.setup_default_jobs()
        except ImportError:
            logging.warning("APScheduler non installé. Planification automatique désactivée.")
            self.scheduler = None
    
    def setup_default_jobs(self):
        """Configure les tâches par défaut"""
        if not self.scheduler:
            return
        
        # Sauvegarde automatique quotidienne à 2h du matin
        self.add_job(
            'backup_daily',
            self.backup_database,
            CronTrigger(hour=2, minute=0),
            'Sauvegarde quotidienne de la base de données'
        )
        
        # Nettoyage des logs hebdomadaire
        self.add_job(
            'cleanup_logs',
            self.cleanup_old_logs,
            CronTrigger(day_of_week=0, hour=3, minute=0),  # Dimanche à 3h
            'Nettoyage des anciens logs'
        )
        
        # Vérification des alertes de stock toutes les heures
        self.add_job(
            'stock_alerts',
            self.check_stock_alerts,
            CronTrigger(minute=0),  # Toutes les heures
            'Vérification des alertes de stock'
        )
        
        # Rapport de fin de journée à 23h
        self.add_job(
            'daily_report',
            self.generate_daily_report,
            CronTrigger(hour=23, minute=0),
            'Génération du rapport quotidien'
        )
        
        # Nettoyage des sessions expirées toutes les 30 minutes
        self.add_job(
            'cleanup_sessions',
            self.cleanup_expired_sessions,
            CronTrigger(minute='*/30'),
            'Nettoyage des sessions expirées'
        )
    
    def add_job(self, job_id, func, trigger, description=""):
        """Ajoute une tâche planifiée"""
        if not self.scheduler:
            return False
        
        try:
            job = self.scheduler.add_job(
                func,
                trigger,
                id=job_id,
                replace_existing=True,
                max_instances=1
            )
            
            self.jobs[job_id] = {
                'job': job,
                'description': description,
                'last_run': None,
                'next_run': job.next_run_time,
                'enabled': True
            }
            
            logging.info(f"Tâche planifiée ajoutée: {job_id} - {description}")
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de l'ajout de la tâche {job_id}: {e}")
            return False
    
    def remove_job(self, job_id):
        """Supprime une tâche planifiée"""
        if not self.scheduler:
            return False
        
        try:
            self.scheduler.remove_job(job_id)
            if job_id in self.jobs:
                del self.jobs[job_id]
            logging.info(f"Tâche supprimée: {job_id}")
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la suppression de la tâche {job_id}: {e}")
            return False
    
    def start(self):
        """Démarre le planificateur"""
        if not self.scheduler or self.running:
            return False
        
        try:
            self.scheduler.start()
            self.running = True
            logging.info("Planificateur de tâches démarré")
            return True
        except Exception as e:
            logging.error(f"Erreur lors du démarrage du planificateur: {e}")
            return False
    
    def stop(self):
        """Arrête le planificateur"""
        if not self.scheduler or not self.running:
            return False
        
        try:
            self.scheduler.shutdown()
            self.running = False
            logging.info("Planificateur de tâches arrêté")
            return True
        except Exception as e:
            logging.error(f"Erreur lors de l'arrêt du planificateur: {e}")
            return False
    
    def get_job_status(self, job_id):
        """Retourne le statut d'une tâche"""
        if job_id not in self.jobs:
            return None
        
        job_info = self.jobs[job_id]
        job = job_info['job']
        
        return {
            'id': job_id,
            'description': job_info['description'],
            'enabled': job_info['enabled'],
            'last_run': job_info['last_run'],
            'next_run': job.next_run_time if job else None,
            'running': self.running
        }
    
    def get_all_jobs_status(self):
        """Retourne le statut de toutes les tâches"""
        return {job_id: self.get_job_status(job_id) for job_id in self.jobs}
    
    # Tâches automatiques
    def backup_database(self):
        """Effectue une sauvegarde de la base de données"""
        try:
            from utils.backup_manager import BackupManager
            backup_manager = BackupManager()
            
            backup_path = backup_manager.create_backup()
            logging.info(f"Sauvegarde automatique créée: {backup_path}")
            
            # Nettoyer les anciennes sauvegardes (garder 30 jours)
            backup_manager.cleanup_old_backups(30)
            
            self.jobs['backup_daily']['last_run'] = datetime.now()
            
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde automatique: {e}")
    
    def cleanup_old_logs(self):
        """Nettoie les anciens fichiers de logs"""
        try:
            import os
            import glob
            
            # Nettoyer les logs de plus de 30 jours
            cutoff_date = datetime.now() - timedelta(days=30)
            
            log_files = glob.glob("*.log*")
            for log_file in log_files:
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    if file_time < cutoff_date:
                        os.remove(log_file)
                        logging.info(f"Ancien log supprimé: {log_file}")
                except Exception as e:
                    logging.warning(f"Impossible de supprimer {log_file}: {e}")
            
            self.jobs['cleanup_logs']['last_run'] = datetime.now()
            
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage des logs: {e}")
    
    def check_stock_alerts(self):
        """Vérifie les alertes de stock"""
        try:
            from utils.notification_manager import notification_manager
            notification_manager.check_stock_alerts()
            
            self.jobs['stock_alerts']['last_run'] = datetime.now()
            
        except Exception as e:
            logging.error(f"Erreur lors de la vérification des alertes: {e}")
    
    def generate_daily_report(self):
        """Génère le rapport quotidien"""
        try:
            from models.vente import Vente
            from datetime import date
            
            today = date.today()
            ventes = Vente.find_ventes_periode(today, today)
            
            if ventes:
                total_ca = sum(v.total_ttc for v in ventes)
                nb_ventes = len(ventes)
                
                # Sauvegarder le rapport
                rapport = {
                    'date': str(today),
                    'nb_ventes': nb_ventes,
                    'ca_total': total_ca,
                    'ticket_moyen': total_ca / nb_ventes if nb_ventes > 0 else 0
                }
                
                # Optionnel: envoyer par email ou sauvegarder
                logging.info(f"Rapport quotidien: {nb_ventes} ventes, CA: {total_ca:.2f}€")
            
            self.jobs['daily_report']['last_run'] = datetime.now()
            
        except Exception as e:
            logging.error(f"Erreur lors de la génération du rapport quotidien: {e}")
    
    def cleanup_expired_sessions(self):
        """Nettoie les sessions expirées"""
        try:
            from models.utilisateur import Utilisateur
            
            # Nettoyer les sessions expirées (plus de 8 heures)
            cutoff_time = datetime.now() - timedelta(hours=8)
            
            # Cette fonctionnalité nécessiterait une table de sessions
            # Pour l'instant, on log juste l'exécution
            logging.debug("Nettoyage des sessions expirées effectué")
            
            self.jobs['cleanup_sessions']['last_run'] = datetime.now()
            
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage des sessions: {e}")
    
    def pause_job(self, job_id):
        """Met en pause une tâche"""
        if not self.scheduler or job_id not in self.jobs:
            return False
        
        try:
            self.scheduler.pause_job(job_id)
            self.jobs[job_id]['enabled'] = False
            logging.info(f"Tâche mise en pause: {job_id}")
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la mise en pause de {job_id}: {e}")
            return False
    
    def resume_job(self, job_id):
        """Reprend une tâche en pause"""
        if not self.scheduler or job_id not in self.jobs:
            return False
        
        try:
            self.scheduler.resume_job(job_id)
            self.jobs[job_id]['enabled'] = True
            logging.info(f"Tâche reprise: {job_id}")
            return True
        except Exception as e:
            logging.error(f"Erreur lors de la reprise de {job_id}: {e}")
            return False
    
    def run_job_now(self, job_id):
        """Exécute immédiatement une tâche"""
        if not self.scheduler or job_id not in self.jobs:
            return False
        
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.func()
                logging.info(f"Tâche exécutée manuellement: {job_id}")
                return True
        except Exception as e:
            logging.error(f"Erreur lors de l'exécution manuelle de {job_id}: {e}")
            return False
    
    def is_available(self):
        """Vérifie si le planificateur est disponible"""
        return self.scheduler is not None

# Instance globale
scheduler_manager = SchedulerManager()
