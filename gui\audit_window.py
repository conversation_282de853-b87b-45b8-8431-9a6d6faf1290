"""
Fenêtre de gestion des logs d'audit
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.audit_log import AuditLog
from models.utilisateur import Utilisateur

class AuditWindow(ttk.Frame):
    def __init__(self, parent, current_user):
        super().__init__(parent)
        self.current_user = current_user
        
        # Vérifier les permissions
        if not self.current_user.can_manage_users():
            messagebox.showerror("Erreur", "Vous n'avez pas les permissions pour accéder aux logs d'audit")
            return
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre et barre d'outils
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(header_frame, text="Logs d'Audit et Sécurité", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'action
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="📤 Exporter", 
                  command=self.export_logs).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="🗑️ Nettoyer", 
                  command=self.clean_old_logs).pack(side=tk.LEFT, padx=2)
        
        # Notebook pour les onglets
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Logs récents
        logs_frame = ttk.Frame(notebook)
        notebook.add(logs_frame, text="Logs récents")
        
        # Onglet Connexions
        connexions_frame = ttk.Frame(notebook)
        notebook.add(connexions_frame, text="Connexions")
        
        # Onglet Statistiques
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistiques")
        
        # Onglet Alertes
        alertes_frame = ttk.Frame(notebook)
        notebook.add(alertes_frame, text="Alertes")
        
        self.setup_logs_tab(logs_frame)
        self.setup_connexions_tab(connexions_frame)
        self.setup_stats_tab(stats_frame)
        self.setup_alertes_tab(alertes_frame)
    
    def setup_logs_tab(self, parent):
        """Configure l'onglet des logs"""
        # Filtres
        filters_frame = ttk.LabelFrame(parent, text="Filtres", padding="10")
        filters_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Ligne 1 de filtres
        filter_row1 = ttk.Frame(filters_frame)
        filter_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(filter_row1, text="Période:").pack(side=tk.LEFT)
        self.date_debut_var = tk.StringVar(value=(date.today() - timedelta(days=7)).strftime("%Y-%m-%d"))
        ttk.Entry(filter_row1, textvariable=self.date_debut_var, width=12).pack(side=tk.LEFT, padx=(5, 5))
        
        ttk.Label(filter_row1, text="au").pack(side=tk.LEFT)
        self.date_fin_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        ttk.Entry(filter_row1, textvariable=self.date_fin_var, width=12).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(filter_row1, text="Module:").pack(side=tk.LEFT)
        self.module_var = tk.StringVar(value="Tous")
        module_combo = ttk.Combobox(filter_row1, textvariable=self.module_var, 
                                   values=["Tous", "authentification", "ventes", "stock", "produits", "utilisateurs"],
                                   state="readonly", width=15)
        module_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        # Ligne 2 de filtres
        filter_row2 = ttk.Frame(filters_frame)
        filter_row2.pack(fill=tk.X)
        
        ttk.Label(filter_row2, text="Utilisateur:").pack(side=tk.LEFT)
        self.utilisateur_var = tk.StringVar(value="Tous")
        self.utilisateur_combo = ttk.Combobox(filter_row2, textvariable=self.utilisateur_var,
                                             state="readonly", width=20)
        self.utilisateur_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(filter_row2, text="Résultat:").pack(side=tk.LEFT)
        self.resultat_var = tk.StringVar(value="Tous")
        resultat_combo = ttk.Combobox(filter_row2, textvariable=self.resultat_var,
                                     values=["Tous", "succes", "echec", "erreur"],
                                     state="readonly", width=10)
        resultat_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(filter_row2, text="Filtrer", command=self.load_logs).pack(side=tk.LEFT, padx=(20, 0))
        
        # Liste des logs
        logs_list_frame = ttk.LabelFrame(parent, text="Journal d'audit", padding="5")
        logs_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour les logs
        columns = ("Date/Heure", "Utilisateur", "Action", "Module", "Objet", "Résultat", "Détails")
        self.logs_tree = ttk.Treeview(logs_list_frame, columns=columns, show="headings")
        
        for col in columns:
            self.logs_tree.heading(col, text=col)
            self.logs_tree.column(col, width=100)
        
        # Ajuster les largeurs
        self.logs_tree.column("Date/Heure", width=130)
        self.logs_tree.column("Utilisateur", width=120)
        self.logs_tree.column("Action", width=100)
        self.logs_tree.column("Module", width=80)
        self.logs_tree.column("Objet", width=80)
        self.logs_tree.column("Résultat", width=80)
        self.logs_tree.column("Détails", width=200)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(logs_list_frame, orient=tk.VERTICAL, command=self.logs_tree.yview)
        h_scrollbar = ttk.Scrollbar(logs_list_frame, orient=tk.HORIZONTAL, command=self.logs_tree.xview)
        self.logs_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.logs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind pour voir les détails
        self.logs_tree.bind('<Double-1>', self.show_log_details)
    
    def setup_connexions_tab(self, parent):
        """Configure l'onglet des connexions"""
        # Statistiques de connexion
        stats_frame = ttk.LabelFrame(parent, text="Statistiques de connexion", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        self.create_info_card(stats_grid, "Connexions aujourd'hui", "0", 0, 0, "connexions_jour")
        self.create_info_card(stats_grid, "Échecs aujourd'hui", "0", 0, 1, "echecs_jour")
        self.create_info_card(stats_grid, "Utilisateurs actifs", "0", 0, 2, "utilisateurs_actifs")
        self.create_info_card(stats_grid, "Dernière connexion", "-", 0, 3, "derniere_connexion")
        
        # Liste des connexions récentes
        connexions_frame = ttk.LabelFrame(parent, text="Connexions récentes", padding="5")
        connexions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview pour les connexions
        conn_columns = ("Date/Heure", "Utilisateur", "Résultat", "IP", "Détails")
        self.connexions_tree = ttk.Treeview(connexions_frame, columns=conn_columns, show="headings")
        
        for col in conn_columns:
            self.connexions_tree.heading(col, text=col)
            self.connexions_tree.column(col, width=120)
        
        conn_scrollbar = ttk.Scrollbar(connexions_frame, orient=tk.VERTICAL, command=self.connexions_tree.yview)
        self.connexions_tree.configure(yscrollcommand=conn_scrollbar.set)
        
        self.connexions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        conn_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_stats_tab(self, parent):
        """Configure l'onglet des statistiques"""
        # Période d'analyse
        period_frame = ttk.LabelFrame(parent, text="Période d'analyse", padding="10")
        period_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(period_frame, text="Du:").pack(side=tk.LEFT)
        self.stats_debut_var = tk.StringVar(value=(date.today() - timedelta(days=30)).strftime("%Y-%m-%d"))
        ttk.Entry(period_frame, textvariable=self.stats_debut_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(period_frame, text="Au:").pack(side=tk.LEFT)
        self.stats_fin_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        ttk.Entry(period_frame, textvariable=self.stats_fin_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(period_frame, text="Analyser", command=self.load_statistics).pack(side=tk.LEFT, padx=(20, 0))
        
        # Affichage des statistiques
        self.stats_text = tk.Text(parent, height=20, width=80, state=tk.DISABLED)
        stats_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
    
    def setup_alertes_tab(self, parent):
        """Configure l'onglet des alertes"""
        # Alertes de sécurité
        alertes_frame = ttk.LabelFrame(parent, text="Alertes de sécurité", padding="10")
        alertes_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.alertes_text = tk.Text(alertes_frame, height=15, width=80, state=tk.DISABLED)
        alertes_scrollbar = ttk.Scrollbar(alertes_frame, orient=tk.VERTICAL, command=self.alertes_text.yview)
        self.alertes_text.configure(yscrollcommand=alertes_scrollbar.set)
        
        self.alertes_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alertes_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bouton de mise à jour
        ttk.Button(parent, text="Actualiser les alertes", 
                  command=self.load_alertes).pack(pady=10)
    
    def create_info_card(self, parent, title, value, row, col, key):
        """Crée une carte d'information"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(content_frame, text=title, font=('Arial', 10)).pack(anchor=tk.W)
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 12, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"info_{key}", value_label)
    
    def load_data(self):
        """Charge toutes les données"""
        self.load_utilisateurs()
        self.load_logs()
        self.load_connexions()
        self.load_statistics()
        self.load_alertes()
    
    def load_utilisateurs(self):
        """Charge les utilisateurs dans le combobox"""
        try:
            utilisateurs = Utilisateur.find_all()
            user_values = ["Tous"] + [u.nom_complet for u in utilisateurs]
            self.utilisateur_combo['values'] = user_values
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des utilisateurs: {e}")
    
    def load_logs(self):
        """Charge les logs selon les filtres"""
        # Nettoyer le treeview
        for item in self.logs_tree.get_children():
            self.logs_tree.delete(item)
        
        try:
            date_debut = datetime.strptime(self.date_debut_var.get(), "%Y-%m-%d").date()
            date_fin = datetime.strptime(self.date_fin_var.get(), "%Y-%m-%d").date()
            
            # Récupérer les logs de la période
            logs = AuditLog.get_logs_periode(date_debut, date_fin, 500)
            
            # Appliquer les filtres
            logs_filtered = self.apply_filters(logs)
            
            # Ajouter au treeview
            for log in logs_filtered:
                # Formater la date
                date_str = datetime.fromisoformat(log.created_at).strftime("%d/%m/%Y %H:%M:%S")
                
                # Icône selon le résultat
                result_icons = {
                    "succes": "✅",
                    "echec": "❌", 
                    "erreur": "⚠️"
                }
                result_text = f"{result_icons.get(log.resultat, '')} {log.resultat}"
                
                # Objet
                objet_text = f"{log.objet_type} #{log.objet_id}" if log.objet_id else ""
                
                self.logs_tree.insert("", "end", values=(
                    date_str,
                    log.get_utilisateur_nom(),
                    log.action,
                    log.module,
                    objet_text,
                    result_text,
                    log.details[:50] + "..." if len(log.details) > 50 else log.details
                ), tags=(str(log.id),))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des logs: {e}")
    
    def apply_filters(self, logs):
        """Applique les filtres aux logs"""
        filtered_logs = logs
        
        # Filtre par module
        module_filter = self.module_var.get()
        if module_filter != "Tous":
            filtered_logs = [log for log in filtered_logs if log.module == module_filter]
        
        # Filtre par utilisateur
        utilisateur_filter = self.utilisateur_var.get()
        if utilisateur_filter != "Tous":
            filtered_logs = [log for log in filtered_logs if log.get_utilisateur_nom() == utilisateur_filter]
        
        # Filtre par résultat
        resultat_filter = self.resultat_var.get()
        if resultat_filter != "Tous":
            filtered_logs = [log for log in filtered_logs if log.resultat == resultat_filter]
        
        return filtered_logs
    
    def load_connexions(self):
        """Charge les statistiques et logs de connexion"""
        try:
            # Statistiques du jour
            today = date.today()
            logs_jour = AuditLog.get_logs_periode(today, today)
            
            connexions_jour = len([log for log in logs_jour if log.action == "connexion_succes"])
            echecs_jour = len([log for log in logs_jour if log.action == "connexion_echec"])
            utilisateurs_actifs = len(set(log.utilisateur_id for log in logs_jour if log.utilisateur_id))
            
            self.info_connexions_jour.config(text=str(connexions_jour))
            self.info_echecs_jour.config(text=str(echecs_jour))
            self.info_utilisateurs_actifs.config(text=str(utilisateurs_actifs))
            
            # Dernière connexion
            connexions_recentes = AuditLog.get_connexions_recentes(1)
            if connexions_recentes:
                derniere = datetime.fromisoformat(connexions_recentes[0].created_at)
                self.info_derniere_connexion.config(text=derniere.strftime("%d/%m %H:%M"))
            
            # Charger la liste des connexions
            self.load_connexions_list()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des connexions: {e}")
    
    def load_connexions_list(self):
        """Charge la liste des connexions récentes"""
        # Nettoyer le treeview
        for item in self.connexions_tree.get_children():
            self.connexions_tree.delete(item)
        
        try:
            connexions = AuditLog.get_connexions_recentes(50)
            
            for conn in connexions:
                date_str = datetime.fromisoformat(conn.created_at).strftime("%d/%m/%Y %H:%M:%S")
                
                result_text = "✅ Succès" if conn.action == "connexion_succes" else "❌ Échec"
                
                self.connexions_tree.insert("", "end", values=(
                    date_str,
                    conn.get_utilisateur_nom(),
                    result_text,
                    conn.adresse_ip,
                    conn.details
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement de la liste des connexions: {e}")
    
    def load_statistics(self):
        """Charge les statistiques d'activité"""
        try:
            date_debut = datetime.strptime(self.stats_debut_var.get(), "%Y-%m-%d").date()
            date_fin = datetime.strptime(self.stats_fin_var.get(), "%Y-%m-%d").date()
            
            stats = AuditLog.get_statistiques_activite(date_debut, date_fin)
            
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            
            stats_text = f"STATISTIQUES D'ACTIVITÉ\n"
            stats_text += f"Période: {date_debut} au {date_fin}\n"
            stats_text += "=" * 50 + "\n\n"
            
            stats_text += f"ACTIVITÉ GÉNÉRALE:\n"
            stats_text += f"  Total d'actions: {stats['total_actions']}\n"
            stats_text += f"  Utilisateurs actifs: {stats['utilisateurs_actifs']}\n\n"
            
            stats_text += f"CONNEXIONS:\n"
            stats_text += f"  Connexions réussies: {stats['connexions_succes']}\n"
            stats_text += f"  Tentatives échouées: {stats['connexions_echecs']}\n"
            if stats['connexions_succes'] + stats['connexions_echecs'] > 0:
                taux_succes = stats['connexions_succes'] / (stats['connexions_succes'] + stats['connexions_echecs']) * 100
                stats_text += f"  Taux de succès: {taux_succes:.1f}%\n"
            stats_text += "\n"
            
            stats_text += f"ACTIVITÉ MÉTIER:\n"
            stats_text += f"  Ventes: {stats['ventes']}\n"
            stats_text += f"  Mouvements de stock: {stats['mouvements_stock']}\n"
            stats_text += f"  Modifications: {stats['modifications']}\n\n"
            
            # Activité par jour
            logs = AuditLog.get_logs_periode(date_debut, date_fin)
            activite_par_jour = {}
            
            for log in logs:
                jour = datetime.fromisoformat(log.created_at).date()
                if jour not in activite_par_jour:
                    activite_par_jour[jour] = 0
                activite_par_jour[jour] += 1
            
            if activite_par_jour:
                stats_text += f"ACTIVITÉ PAR JOUR:\n"
                for jour in sorted(activite_par_jour.keys(), reverse=True)[:10]:
                    stats_text += f"  {jour}: {activite_par_jour[jour]} actions\n"
            
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des statistiques: {e}")
    
    def load_alertes(self):
        """Charge les alertes de sécurité"""
        try:
            self.alertes_text.config(state=tk.NORMAL)
            self.alertes_text.delete(1.0, tk.END)
            
            alertes_text = "ALERTES DE SÉCURITÉ\n"
            alertes_text += "=" * 30 + "\n\n"
            
            # Tentatives de connexion échouées récentes
            echecs_recents = AuditLog.find_all(
                "action = 'connexion_echec' AND created_at > datetime('now', '-24 hours') ORDER BY created_at DESC LIMIT 10"
            )
            
            if echecs_recents:
                alertes_text += f"⚠️ TENTATIVES DE CONNEXION ÉCHOUÉES (24h):\n"
                for echec in echecs_recents:
                    date_str = datetime.fromisoformat(echec.created_at).strftime("%d/%m %H:%M")
                    alertes_text += f"  {date_str} - {echec.get_utilisateur_nom()} - {echec.details}\n"
                alertes_text += "\n"
            
            # Logs d'erreur récents
            erreurs_recentes = AuditLog.find_all(
                "resultat = 'erreur' AND created_at > datetime('now', '-7 days') ORDER BY created_at DESC LIMIT 5"
            )
            
            if erreurs_recentes:
                alertes_text += f"🔴 ERREURS RÉCENTES (7 jours):\n"
                for erreur in erreurs_recentes:
                    date_str = datetime.fromisoformat(erreur.created_at).strftime("%d/%m %H:%M")
                    alertes_text += f"  {date_str} - {erreur.module} - {erreur.details}\n"
                alertes_text += "\n"
            
            # Activité suspecte (beaucoup d'actions en peu de temps)
            logs_recents = AuditLog.find_all(
                "created_at > datetime('now', '-1 hour') ORDER BY utilisateur_id, created_at"
            )
            
            activite_par_user = {}
            for log in logs_recents:
                if log.utilisateur_id not in activite_par_user:
                    activite_par_user[log.utilisateur_id] = 0
                activite_par_user[log.utilisateur_id] += 1
            
            activite_suspecte = {uid: count for uid, count in activite_par_user.items() if count > 50}
            
            if activite_suspecte:
                alertes_text += f"🚨 ACTIVITÉ INTENSIVE (1h):\n"
                for uid, count in activite_suspecte.items():
                    user = Utilisateur.find_by_id(uid)
                    nom = user.nom_complet if user else f"ID {uid}"
                    alertes_text += f"  {nom}: {count} actions\n"
                alertes_text += "\n"
            
            if not echecs_recents and not erreurs_recentes and not activite_suspecte:
                alertes_text += "✅ Aucune alerte de sécurité détectée.\n"
            
            self.alertes_text.insert(1.0, alertes_text)
            self.alertes_text.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des alertes: {e}")
    
    def show_log_details(self, event):
        """Affiche les détails d'un log"""
        selection = self.logs_tree.selection()
        if not selection:
            return
        
        try:
            log_id = int(self.logs_tree.item(selection[0])['tags'][0])
            log = AuditLog.find_by_id(log_id)
            
            if log:
                # Créer une fenêtre de détails
                details_window = tk.Toplevel(self)
                details_window.title(f"Détails du log #{log.id}")
                details_window.geometry("600x400")
                details_window.resizable(True, True)
                
                # Contenu des détails
                details_text = tk.Text(details_window, wrap=tk.WORD)
                details_scrollbar = ttk.Scrollbar(details_window, orient=tk.VERTICAL, command=details_text.yview)
                details_text.configure(yscrollcommand=details_scrollbar.set)
                
                # Formater les détails
                content = f"LOG D'AUDIT #{log.id}\n"
                content += "=" * 30 + "\n\n"
                content += f"Date/Heure: {log.created_at}\n"
                content += f"Utilisateur: {log.get_utilisateur_nom()}\n"
                content += f"Action: {log.action}\n"
                content += f"Module: {log.module}\n"
                content += f"Résultat: {log.resultat}\n"
                content += f"Objet: {log.objet_type} #{log.objet_id}\n" if log.objet_id else ""
                content += f"Adresse IP: {log.adresse_ip}\n"
                content += f"User Agent: {log.user_agent}\n\n"
                
                if log.details:
                    content += f"Détails:\n{log.details}\n\n"
                
                if log.donnees_avant:
                    content += f"Données avant:\n{log.donnees_avant}\n\n"
                
                if log.donnees_apres:
                    content += f"Données après:\n{log.donnees_apres}\n"
                
                details_text.insert(1.0, content)
                details_text.config(state=tk.DISABLED)
                
                details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'affichage des détails: {e}")
    
    def export_logs(self):
        """Exporte les logs vers un fichier"""
        try:
            from utils.export_manager import ExportManager
            export_manager = ExportManager()
            
            # Récupérer les logs filtrés
            date_debut = datetime.strptime(self.date_debut_var.get(), "%Y-%m-%d").date()
            date_fin = datetime.strptime(self.date_fin_var.get(), "%Y-%m-%d").date()
            logs = AuditLog.get_logs_periode(date_debut, date_fin, 1000)
            logs_filtered = self.apply_filters(logs)
            
            # Convertir en format d'export
            data = [log.to_dict() for log in logs_filtered]
            
            # Exporter
            filename = f"audit_logs_{date_debut}_{date_fin}.csv"
            filepath = export_manager.export_to_csv(data, filename)
            
            messagebox.showinfo("Export", f"Logs exportés vers:\n{filepath}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export: {e}")
    
    def clean_old_logs(self):
        """Nettoie les anciens logs"""
        if messagebox.askyesno("Confirmation", 
                              "Supprimer les logs de plus de 90 jours ?\n"
                              "Cette action est irréversible."):
            try:
                nb_supprime = AuditLog.nettoyer_anciens_logs(90)
                messagebox.showinfo("Nettoyage", f"{nb_supprime} anciens logs supprimés")
                self.load_data()
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du nettoyage: {e}")
