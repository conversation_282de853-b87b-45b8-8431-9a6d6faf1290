"""
Fenêtre de gestion des ventes et point de vente
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.vente import Vente
from models.produit import Produit
from models.client_credit import ClientCredit
from utils.ticket_generator import TicketGenerator

class VentesWindow(ttk.Frame):
    def __init__(self, parent, current_user, mode="pos"):
        super().__init__(parent)
        self.current_user = current_user
        self.mode = mode  # "pos" pour point de vente, "historique" pour historique

        # Variables pour le POS
        self.panier = []  # Liste des articles dans le panier
        self.total_ht = 0.0
        self.total_ttc = 0.0
        self.ticket_generator = TicketGenerator()

        if mode == "pos":
            self.setup_pos_ui()
        else:
            self.setup_historique_ui()
    
    def setup_pos_ui(self):
        """Configure l'interface du point de vente"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="Point de Vente", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Frame principal avec deux panneaux
        main_paned = ttk.PanedWindow(self, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Panneau gauche - Sélection des produits
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=2)
        
        # Panneau droit - Panier et paiement
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_product_selection(left_frame)
        self.setup_cart_and_payment(right_frame)
    
    def setup_product_selection(self, parent):
        """Configure la sélection des produits"""
        # Barre de recherche
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="Rechercher produit:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 12))
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        search_entry.bind('<Return>', self.search_product)
        
        ttk.Button(search_frame, text="🔍", command=self.search_product).pack(side=tk.LEFT, padx=(5, 0))
        
        # Liste des produits
        products_frame = ttk.LabelFrame(parent, text="Produits disponibles", padding="5")
        products_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview pour les produits
        columns = ("Nom", "Prix", "Stock", "Unité")
        self.products_tree = ttk.Treeview(products_frame, columns=columns, show="headings")
        
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=100)
        
        # Scrollbar
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, 
                                         command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Double-clic pour ajouter au panier
        self.products_tree.bind('<Double-1>', self.add_to_cart)
        
        # Charger les produits
        self.load_products_for_sale()
    
    def setup_cart_and_payment(self, parent):
        """Configure le panier et le paiement"""
        # Panier
        cart_frame = ttk.LabelFrame(parent, text="Panier", padding="5")
        cart_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Treeview pour le panier
        cart_columns = ("Produit", "Qté", "Prix unit.", "Total")
        self.cart_tree = ttk.Treeview(cart_frame, columns=cart_columns, show="headings", height=8)
        
        for col in cart_columns:
            self.cart_tree.heading(col, text=col)
            self.cart_tree.column(col, width=80)
        
        self.cart_tree.pack(fill=tk.BOTH, expand=True)
        
        # Boutons du panier
        cart_buttons_frame = ttk.Frame(cart_frame)
        cart_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(cart_buttons_frame, text="➕ Ajouter", 
                  command=self.add_selected_to_cart).pack(side=tk.LEFT, padx=2)
        ttk.Button(cart_buttons_frame, text="➖ Retirer", 
                  command=self.remove_from_cart).pack(side=tk.LEFT, padx=2)
        ttk.Button(cart_buttons_frame, text="🗑️ Vider", 
                  command=self.clear_cart).pack(side=tk.LEFT, padx=2)
        
        # Total
        total_frame = ttk.Frame(parent)
        total_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(total_frame, text="Total:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT)
        self.total_label = ttk.Label(total_frame, text="0.00 €", font=('Arial', 14, 'bold'))
        self.total_label.pack(side=tk.RIGHT)
        
        # Paiement
        payment_frame = ttk.LabelFrame(parent, text="Paiement", padding="10")
        payment_frame.pack(fill=tk.X)
        
        # Mode de paiement
        ttk.Label(payment_frame, text="Mode de paiement:").pack(anchor=tk.W)
        self.payment_var = tk.StringVar(value="cash")
        
        payment_modes = [("Espèces", "cash"), ("Carte", "carte"), ("Mobile", "mobile"), ("Crédit", "credit")]
        for text, value in payment_modes:
            ttk.Radiobutton(payment_frame, text=text, variable=self.payment_var, 
                           value=value).pack(anchor=tk.W)
        
        # Boutons de paiement
        payment_buttons_frame = ttk.Frame(payment_frame)
        payment_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(payment_buttons_frame, text="💳 Finaliser la vente", 
                  command=self.finalize_sale, style='Success.TButton').pack(fill=tk.X, pady=2)
        ttk.Button(payment_buttons_frame, text="❌ Annuler", 
                  command=self.cancel_sale).pack(fill=tk.X, pady=2)
        
        # Initialiser le panier
        self.cart_items = []
    
    def setup_historique_ui(self):
        """Configure l'interface de l'historique des ventes"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(title_frame, text="Historique des Ventes", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Filtres
        filters_frame = ttk.Frame(self)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Date
        ttk.Label(filters_frame, text="Date:").pack(side=tk.LEFT)
        # Ici on ajouterait des widgets de sélection de date
        
        # Liste des ventes
        sales_frame = ttk.LabelFrame(self, text="Ventes", padding="5")
        sales_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview pour les ventes
        columns = ("Date", "Ticket", "Total", "Mode paiement", "Vendeur")
        self.sales_tree = ttk.Treeview(sales_frame, columns=columns, show="headings")
        
        for col in columns:
            self.sales_tree.heading(col, text=col)
            self.sales_tree.column(col, width=120)
        
        # Scrollbar
        sales_scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, 
                                       command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)
        
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Charger les ventes
        self.load_sales_history()
    
    def load_products_for_sale(self):
        """Charge les produits disponibles pour la vente"""
        # Nettoyer le treeview
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        try:
            produits = Produit.find_all("actif = 1 AND stock_actuel > 0")
            
            for produit in produits:
                self.products_tree.insert("", "end", values=(
                    produit.nom,
                    f"{produit.prix_vente:.2f} €",
                    produit.stock_actuel,
                    produit.unite
                ), tags=(str(produit.id),))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def search_product(self, event=None):
        """Recherche un produit"""
        search_term = self.search_var.get().strip()
        
        # Nettoyer le treeview
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        try:
            if search_term:
                produits = Produit.search(search_term)
                produits = [p for p in produits if p.actif and p.stock_actuel > 0]
            else:
                produits = Produit.find_all("actif = 1 AND stock_actuel > 0")
            
            for produit in produits:
                self.products_tree.insert("", "end", values=(
                    produit.nom,
                    f"{produit.prix_vente:.2f} €",
                    produit.stock_actuel,
                    produit.unite
                ), tags=(str(produit.id),))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la recherche: {e}")
    
    def add_to_cart(self, event=None):
        """Ajoute le produit sélectionné au panier"""
        selection = self.products_tree.selection()
        if not selection:
            return
        
        item = self.products_tree.item(selection[0])
        produit_id = int(item['tags'][0])
        
        # Demander la quantité
        quantity = self.ask_quantity()
        if quantity is None or quantity <= 0:
            return
        
        try:
            produit = Produit.find_by_id(produit_id)
            if not produit:
                messagebox.showerror("Erreur", "Produit introuvable")
                return
            
            if produit.stock_actuel < quantity:
                messagebox.showerror("Erreur", "Stock insuffisant")
                return
            
            # Vérifier si le produit est déjà dans le panier
            for i, cart_item in enumerate(self.cart_items):
                if cart_item['produit_id'] == produit_id:
                    self.cart_items[i]['quantite'] += quantity
                    break
            else:
                # Ajouter nouveau produit au panier
                self.cart_items.append({
                    'produit_id': produit_id,
                    'nom': produit.nom,
                    'prix_unitaire': produit.prix_vente,
                    'quantite': quantity
                })
            
            self.update_cart_display()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout au panier: {e}")
    
    def ask_quantity(self):
        """Demande la quantité à ajouter"""
        # Fenêtre simple pour demander la quantité
        dialog = tk.Toplevel(self)
        dialog.title("Quantité")
        dialog.geometry("200x100")
        dialog.resizable(False, False)
        
        # Centrer la fenêtre
        dialog.transient(self)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Quantité:").pack(pady=10)
        
        quantity_var = tk.StringVar(value="1")
        entry = ttk.Entry(dialog, textvariable=quantity_var)
        entry.pack(pady=5)
        entry.focus()
        entry.select_range(0, tk.END)
        
        result = [None]
        
        def ok():
            try:
                result[0] = int(quantity_var.get())
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Erreur", "Veuillez saisir un nombre valide")
        
        def cancel():
            dialog.destroy()
        
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="OK", command=ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Annuler", command=cancel).pack(side=tk.LEFT, padx=5)
        
        entry.bind('<Return>', lambda e: ok())
        dialog.bind('<Escape>', lambda e: cancel())
        
        dialog.wait_window()
        return result[0]
    
    def add_selected_to_cart(self):
        """Ajoute le produit sélectionné au panier"""
        self.add_to_cart()
    
    def remove_from_cart(self):
        """Retire un produit du panier"""
        selection = self.cart_tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à retirer")
            return
        
        item_index = self.cart_tree.index(selection[0])
        del self.cart_items[item_index]
        self.update_cart_display()
    
    def clear_cart(self):
        """Vide le panier"""
        if self.cart_items and messagebox.askyesno("Confirmation", "Vider le panier ?"):
            self.cart_items.clear()
            self.update_cart_display()
    
    def update_cart_display(self):
        """Met à jour l'affichage du panier"""
        # Nettoyer le treeview
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        
        total = 0
        for cart_item in self.cart_items:
            sous_total = cart_item['quantite'] * cart_item['prix_unitaire']
            total += sous_total
            
            self.cart_tree.insert("", "end", values=(
                cart_item['nom'],
                cart_item['quantite'],
                f"{cart_item['prix_unitaire']:.2f} €",
                f"{sous_total:.2f} €"
            ))
        
        self.total_label.config(text=f"{total:.2f} €")
    
    def finalize_sale(self):
        """Finalise la vente avec génération de ticket"""
        if not self.cart_items:
            messagebox.showwarning("Attention", "Le panier est vide")
            return

        # Vérifier les stocks avant la vente
        stock_errors = []
        for item in self.cart_items:
            produit = Produit.find_by_id(item['produit_id'])
            if produit and produit.stock_actuel < item['quantite']:
                stock_errors.append(f"{produit.nom}: stock insuffisant ({produit.stock_actuel} disponible, {item['quantite']} demandé)")

        if stock_errors:
            messagebox.showerror("Stock insuffisant", "\n".join(stock_errors))
            return

        try:
            # Créer la vente
            vente = Vente()
            vente.utilisateur_id = self.current_user.id
            vente.mode_paiement = self.payment_var.get()

            # Ajouter les produits
            for cart_item in self.cart_items:
                vente.ajouter_produit(
                    cart_item['produit_id'],
                    cart_item['quantite'],
                    cart_item['prix_unitaire']
                )

            # Finaliser (cela déduira automatiquement le stock)
            vente_id = vente.finaliser_vente()

            # Recharger la vente avec tous ses détails pour le ticket
            vente = Vente.find_by_id(vente_id)

            # Générer et afficher le ticket
            self.generate_and_show_ticket(vente)

            messagebox.showinfo("Succès", f"Vente finalisée\nTicket: {vente.numero_ticket}")

            # Vider le panier et actualiser
            self.cart_items.clear()
            self.update_cart_display()
            self.load_products_for_sale()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la finalisation: {e}")

    def generate_and_show_ticket(self, vente):
        """Génère et affiche le ticket de caisse"""
        try:
            # Demander à l'utilisateur ce qu'il veut faire
            choice = messagebox.askyesnocancel(
                "Ticket de caisse",
                "Voulez-vous imprimer le ticket ?\n\n"
                "Oui = Ouvrir pour impression\n"
                "Non = Sauvegarder seulement\n"
                "Annuler = Rien faire"
            )

            if choice is True:
                # Ouvrir le ticket HTML pour impression
                ticket_file = self.ticket_generator.open_ticket_for_print(vente)
                messagebox.showinfo("Ticket", f"Ticket ouvert pour impression:\n{ticket_file}")

            elif choice is False:
                # Sauvegarder seulement
                ticket_file = self.ticket_generator.save_ticket_to_file(vente)
                messagebox.showinfo("Ticket", f"Ticket sauvegardé:\n{ticket_file}")

            # Dans tous les cas, sauvegarder une copie texte
            self.ticket_generator.save_ticket_to_file(vente)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la génération du ticket: {e}")
    
    def cancel_sale(self):
        """Annule la vente en cours"""
        if self.cart_items and messagebox.askyesno("Confirmation", "Annuler la vente en cours ?"):
            self.cart_items.clear()
            self.update_cart_display()
    
    def load_sales_history(self):
        """Charge l'historique des ventes"""
        # Nettoyer le treeview
        for item in self.sales_tree.get_children():
            self.sales_tree.delete(item)
        
        try:
            # Charger les ventes récentes (par exemple les 100 dernières)
            ventes = Vente.find_all("statut = 'termine' ORDER BY date_vente DESC LIMIT 100")
            
            for vente in ventes:
                self.sales_tree.insert("", "end", values=(
                    vente.date_vente,
                    vente.numero_ticket,
                    f"{vente.total_ttc:.2f} €",
                    vente.mode_paiement,
                    vente.get_utilisateur_nom()
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement de l'historique: {e}")
