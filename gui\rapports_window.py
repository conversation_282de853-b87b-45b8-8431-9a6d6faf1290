"""
Fenêtre de rapports et statistiques
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.vente import Vente
from models.produit import Produit
from models.mouvement_stock import MouvementStock

class RapportsWindow(ttk.Frame):
    def __init__(self, parent, current_user, type_rapport="ventes"):
        super().__init__(parent)
        self.current_user = current_user
        self.type_rapport = type_rapport
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Titre
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_text = {
            "ventes": "Rapports de Ventes",
            "stock": "Rapports de Stock", 
            "financier": "Rapports Financiers"
        }.get(self.type_rapport, "Rapports")
        
        ttk.Label(title_frame, text=title_text, 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        # Boutons d'export
        export_frame = ttk.Frame(title_frame)
        export_frame.pack(side=tk.RIGHT)
        
        ttk.Button(export_frame, text="📊 Export Excel", 
                  command=self.export_excel).pack(side=tk.LEFT, padx=2)
        ttk.Button(export_frame, text="📄 Export PDF", 
                  command=self.export_pdf).pack(side=tk.LEFT, padx=2)
        ttk.Button(export_frame, text="🔄 Actualiser", 
                  command=self.load_data).pack(side=tk.LEFT, padx=2)
        
        # Filtres de période
        filters_frame = ttk.LabelFrame(self, text="Période", padding="10")
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Période prédéfinie
        ttk.Label(filters_frame, text="Période:").pack(side=tk.LEFT)
        
        self.period_var = tk.StringVar(value="Aujourd'hui")
        period_combo = ttk.Combobox(filters_frame, textvariable=self.period_var,
                                   values=["Aujourd'hui", "Cette semaine", "Ce mois", 
                                          "Mois dernier", "Cette année", "Personnalisée"],
                                   state="readonly")
        period_combo.pack(side=tk.LEFT, padx=(5, 20))
        period_combo.bind('<<ComboboxSelected>>', self.on_period_change)
        
        # Dates personnalisées
        ttk.Label(filters_frame, text="Du:").pack(side=tk.LEFT, padx=(20, 5))
        self.date_debut_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(filters_frame, textvariable=self.date_debut_var, width=12).pack(side=tk.LEFT)
        
        ttk.Label(filters_frame, text="Au:").pack(side=tk.LEFT, padx=(10, 5))
        self.date_fin_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(filters_frame, textvariable=self.date_fin_var, width=12).pack(side=tk.LEFT)
        
        ttk.Button(filters_frame, text="Appliquer", 
                  command=self.load_data).pack(side=tk.LEFT, padx=(10, 0))
        
        # Notebook pour les différents rapports
        notebook = ttk.Notebook(self)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        if self.type_rapport == "ventes":
            self.setup_sales_reports(notebook)
        elif self.type_rapport == "stock":
            self.setup_stock_reports(notebook)
        elif self.type_rapport == "financier":
            self.setup_financial_reports(notebook)
    
    def setup_sales_reports(self, notebook):
        """Configure les rapports de ventes"""
        # Onglet Résumé des ventes
        summary_frame = ttk.Frame(notebook)
        notebook.add(summary_frame, text="Résumé")
        
        # Statistiques générales
        stats_frame = ttk.LabelFrame(summary_frame, text="Statistiques générales", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Grid pour les statistiques
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)
        
        # Cartes de statistiques
        self.create_stat_card(stats_grid, "Chiffre d'affaires", "0 €", 0, 0)
        self.create_stat_card(stats_grid, "Nombre de ventes", "0", 0, 1)
        self.create_stat_card(stats_grid, "Ticket moyen", "0 €", 0, 2)
        self.create_stat_card(stats_grid, "Articles vendus", "0", 0, 3)
        
        # Graphique des ventes par jour (simulé avec un treeview)
        chart_frame = ttk.LabelFrame(summary_frame, text="Évolution des ventes", padding="5")
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("Date", "Nombre de ventes", "Chiffre d'affaires", "Ticket moyen")
        self.sales_chart_tree = ttk.Treeview(chart_frame, columns=columns, show="headings")
        
        for col in columns:
            self.sales_chart_tree.heading(col, text=col)
            self.sales_chart_tree.column(col, width=120)
        
        chart_scrollbar = ttk.Scrollbar(chart_frame, orient=tk.VERTICAL, command=self.sales_chart_tree.yview)
        self.sales_chart_tree.configure(yscrollcommand=chart_scrollbar.set)
        
        self.sales_chart_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        chart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Onglet Produits les plus vendus
        products_frame = ttk.Frame(notebook)
        notebook.add(products_frame, text="Produits populaires")
        
        columns = ("Rang", "Produit", "Quantité vendue", "Chiffre d'affaires", "% du CA")
        self.top_products_tree = ttk.Treeview(products_frame, columns=columns, show="headings")
        
        for col in columns:
            self.top_products_tree.heading(col, text=col)
            self.top_products_tree.column(col, width=120)
        
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.top_products_tree.yview)
        self.top_products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.top_products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Onglet Ventes par vendeur
        sellers_frame = ttk.Frame(notebook)
        notebook.add(sellers_frame, text="Par vendeur")
        
        columns = ("Vendeur", "Nombre de ventes", "Chiffre d'affaires", "Ticket moyen")
        self.sellers_tree = ttk.Treeview(sellers_frame, columns=columns, show="headings")
        
        for col in columns:
            self.sellers_tree.heading(col, text=col)
            self.sellers_tree.column(col, width=150)
        
        sellers_scrollbar = ttk.Scrollbar(sellers_frame, orient=tk.VERTICAL, command=self.sellers_tree.yview)
        self.sellers_tree.configure(yscrollcommand=sellers_scrollbar.set)
        
        self.sellers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sellers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_stock_reports(self, notebook):
        """Configure les rapports de stock"""
        # Onglet État du stock
        stock_frame = ttk.Frame(notebook)
        notebook.add(stock_frame, text="État du stock")

        # Statistiques du stock
        stats_frame = ttk.LabelFrame(stock_frame, text="Statistiques du stock", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        for i in range(4):
            stats_grid.columnconfigure(i, weight=1)

        self.create_stat_card(stats_grid, "Valeur totale", "0 €", 0, 0)
        self.create_stat_card(stats_grid, "Produits actifs", "0", 0, 1)
        self.create_stat_card(stats_grid, "Stock faible", "0", 0, 2)
        self.create_stat_card(stats_grid, "Ruptures", "0", 0, 3)

        # Liste des produits
        list_frame = ttk.LabelFrame(stock_frame, text="Détail du stock", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("Produit", "Stock actuel", "Valeur stock", "Rotation", "Statut")
        self.stock_tree = ttk.Treeview(list_frame, columns=columns, show="headings")

        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=120)

        stock_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.stock_tree.yview)
        self.stock_tree.configure(yscrollcommand=stock_scrollbar.set)

        self.stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stock_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Onglet Mouvements de stock
        movements_frame = ttk.Frame(notebook)
        notebook.add(movements_frame, text="Mouvements")

        # Filtres pour les mouvements
        filter_frame = ttk.LabelFrame(movements_frame, text="Filtres", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(filter_frame, text="Type:").grid(row=0, column=0, padx=5, sticky=tk.W)
        self.movement_type_var = tk.StringVar(value="tous")
        type_combo = ttk.Combobox(filter_frame, textvariable=self.movement_type_var,
                                 values=["tous", "entree", "sortie", "inventaire"], state="readonly")
        type_combo.grid(row=0, column=1, padx=5, sticky=tk.W)
        type_combo.bind('<<ComboboxSelected>>', lambda e: self.load_movements_data())

        ttk.Button(filter_frame, text="🔄 Actualiser",
                  command=self.load_movements_data).grid(row=0, column=2, padx=10)

        # Liste des mouvements
        list_movements_frame = ttk.LabelFrame(movements_frame, text="Historique des mouvements", padding="5")
        list_movements_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("Date", "Produit", "Type", "Quantité", "Valeur", "Utilisateur")
        self.movements_tree = ttk.Treeview(list_movements_frame, columns=columns, show="headings")

        for col in columns:
            self.movements_tree.heading(col, text=col)
            self.movements_tree.column(col, width=120)

        movements_scrollbar = ttk.Scrollbar(list_movements_frame, orient=tk.VERTICAL, command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=movements_scrollbar.set)

        self.movements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        movements_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Onglet Alertes
        alerts_frame = ttk.Frame(notebook)
        notebook.add(alerts_frame, text="Alertes")

        # Alertes de stock faible
        low_stock_frame = ttk.LabelFrame(alerts_frame, text="Produits en stock faible", padding="5")
        low_stock_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        columns = ("Produit", "Stock actuel", "Seuil minimum", "Dernière vente", "Action recommandée")
        self.alerts_tree = ttk.Treeview(low_stock_frame, columns=columns, show="headings")

        for col in columns:
            self.alerts_tree.heading(col, text=col)
            self.alerts_tree.column(col, width=120)

        alerts_scrollbar = ttk.Scrollbar(low_stock_frame, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)

        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_financial_reports(self, notebook):
        """Configure les rapports financiers"""
        # Onglet Résumé financier
        financial_frame = ttk.Frame(notebook)
        notebook.add(financial_frame, text="Résumé financier")
        
        # Statistiques financières
        stats_frame = ttk.LabelFrame(financial_frame, text="Indicateurs financiers", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        for i in range(3):
            stats_grid.columnconfigure(i, weight=1)
        
        self.create_stat_card(stats_grid, "Chiffre d'affaires", "0 €", 0, 0)
        self.create_stat_card(stats_grid, "Coût des ventes", "0 €", 0, 1)
        self.create_stat_card(stats_grid, "Marge brute", "0 €", 0, 2)
        
        # Détail par mode de paiement
        payment_frame = ttk.LabelFrame(financial_frame, text="Répartition par mode de paiement", padding="5")
        payment_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ("Mode de paiement", "Nombre", "Montant", "Pourcentage")
        self.payment_tree = ttk.Treeview(payment_frame, columns=columns, show="headings")
        
        for col in columns:
            self.payment_tree.heading(col, text=col)
            self.payment_tree.column(col, width=150)
        
        payment_scrollbar = ttk.Scrollbar(payment_frame, orient=tk.VERTICAL, command=self.payment_tree.yview)
        self.payment_tree.configure(yscrollcommand=payment_scrollbar.set)
        
        self.payment_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        payment_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_stat_card(self, parent, title, value, row, col):
        """Crée une carte de statistique"""
        card_frame = ttk.Frame(parent, relief="solid", borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(content_frame, text=title, font=('Arial', 10)).pack(anchor=tk.W)
        value_label = ttk.Label(content_frame, text=value, font=('Arial', 14, 'bold'))
        value_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Stocker la référence pour mise à jour
        setattr(self, f"stat_{title.lower().replace(' ', '_').replace('é', 'e')}", value_label)
    
    def get_date_range(self):
        """Retourne la plage de dates sélectionnée"""
        period = self.period_var.get()
        today = datetime.now().date()
        
        if period == "Aujourd'hui":
            return today, today
        elif period == "Cette semaine":
            start = today - timedelta(days=today.weekday())
            return start, today
        elif period == "Ce mois":
            start = today.replace(day=1)
            return start, today
        elif period == "Mois dernier":
            first_this_month = today.replace(day=1)
            last_month = first_this_month - timedelta(days=1)
            start = last_month.replace(day=1)
            return start, last_month
        elif period == "Cette année":
            start = today.replace(month=1, day=1)
            return start, today
        else:  # Personnalisée
            try:
                start = datetime.strptime(self.date_debut_var.get(), "%Y-%m-%d").date()
                end = datetime.strptime(self.date_fin_var.get(), "%Y-%m-%d").date()
                return start, end
            except ValueError:
                return today, today
    
    def on_period_change(self, event):
        """Appelé quand la période change"""
        start_date, end_date = self.get_date_range()
        self.date_debut_var.set(start_date.strftime("%Y-%m-%d"))
        self.date_fin_var.set(end_date.strftime("%Y-%m-%d"))
        self.load_data()
    
    def load_data(self):
        """Charge les données selon le type de rapport"""
        if self.type_rapport == "ventes":
            self.load_sales_data()
        elif self.type_rapport == "stock":
            self.load_stock_data()
        elif self.type_rapport == "financier":
            self.load_financial_data()
    
    def load_sales_data(self):
        """Charge les données de ventes"""
        try:
            start_date, end_date = self.get_date_range()
            ventes = Vente.find_ventes_periode(start_date, end_date)
            
            # Statistiques générales
            total_ca = sum(vente.total_ttc for vente in ventes)
            nb_ventes = len(ventes)
            ticket_moyen = total_ca / nb_ventes if nb_ventes > 0 else 0
            
            total_articles = 0
            for vente in ventes:
                total_articles += sum(detail.quantite for detail in vente.details)
            
            # Mise à jour des statistiques
            if hasattr(self, 'stat_chiffre_daffaires'):
                self.stat_chiffre_daffaires.config(text=f"{total_ca:.2f} €")
            if hasattr(self, 'stat_nombre_de_ventes'):
                self.stat_nombre_de_ventes.config(text=str(nb_ventes))
            if hasattr(self, 'stat_ticket_moyen'):
                self.stat_ticket_moyen.config(text=f"{ticket_moyen:.2f} €")
            if hasattr(self, 'stat_articles_vendus'):
                self.stat_articles_vendus.config(text=str(total_articles))
            
            # Charger les autres données
            self.load_sales_chart_data(ventes)
            self.load_top_products_data(ventes)
            self.load_sellers_data(ventes)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données de ventes: {e}")
    
    def load_sales_chart_data(self, ventes):
        """Charge les données du graphique des ventes"""
        # Nettoyer le treeview
        for item in self.sales_chart_tree.get_children():
            self.sales_chart_tree.delete(item)
        
        # Grouper par date
        ventes_par_date = {}
        for vente in ventes:
            date = datetime.fromisoformat(vente.date_vente).date()
            if date not in ventes_par_date:
                ventes_par_date[date] = []
            ventes_par_date[date].append(vente)
        
        # Ajouter au treeview
        for date, ventes_jour in sorted(ventes_par_date.items()):
            nb_ventes = len(ventes_jour)
            ca_jour = sum(v.total_ttc for v in ventes_jour)
            ticket_moyen = ca_jour / nb_ventes if nb_ventes > 0 else 0
            
            self.sales_chart_tree.insert("", "end", values=(
                date.strftime("%d/%m/%Y"),
                nb_ventes,
                f"{ca_jour:.2f} €",
                f"{ticket_moyen:.2f} €"
            ))
    
    def load_top_products_data(self, ventes):
        """Charge les données des produits les plus vendus"""
        # Nettoyer le treeview
        for item in self.top_products_tree.get_children():
            self.top_products_tree.delete(item)
        
        # Calculer les ventes par produit
        produits_ventes = {}
        total_ca = sum(vente.total_ttc for vente in ventes)
        
        for vente in ventes:
            for detail in vente.details:
                if detail.produit_id not in produits_ventes:
                    produits_ventes[detail.produit_id] = {
                        'nom': detail.get_produit_nom(),
                        'quantite': 0,
                        'ca': 0
                    }
                produits_ventes[detail.produit_id]['quantite'] += detail.quantite
                produits_ventes[detail.produit_id]['ca'] += detail.sous_total
        
        # Trier par chiffre d'affaires
        produits_tries = sorted(produits_ventes.items(), 
                               key=lambda x: x[1]['ca'], reverse=True)
        
        # Ajouter au treeview
        for i, (produit_id, data) in enumerate(produits_tries[:20], 1):
            pourcentage = (data['ca'] / total_ca * 100) if total_ca > 0 else 0
            
            self.top_products_tree.insert("", "end", values=(
                i,
                data['nom'],
                data['quantite'],
                f"{data['ca']:.2f} €",
                f"{pourcentage:.1f}%"
            ))
    
    def load_sellers_data(self, ventes):
        """Charge les données des vendeurs"""
        # Nettoyer le treeview
        for item in self.sellers_tree.get_children():
            self.sellers_tree.delete(item)
        
        # Calculer les ventes par vendeur
        vendeurs_ventes = {}
        
        for vente in ventes:
            vendeur_nom = vente.get_utilisateur_nom()
            if vendeur_nom not in vendeurs_ventes:
                vendeurs_ventes[vendeur_nom] = {
                    'nb_ventes': 0,
                    'ca': 0
                }
            vendeurs_ventes[vendeur_nom]['nb_ventes'] += 1
            vendeurs_ventes[vendeur_nom]['ca'] += vente.total_ttc
        
        # Ajouter au treeview
        for vendeur, data in vendeurs_ventes.items():
            ticket_moyen = data['ca'] / data['nb_ventes'] if data['nb_ventes'] > 0 else 0
            
            self.sellers_tree.insert("", "end", values=(
                vendeur,
                data['nb_ventes'],
                f"{data['ca']:.2f} €",
                f"{ticket_moyen:.2f} €"
            ))
    
    def load_stock_data(self):
        """Charge les données de stock"""
        try:
            # Charger tous les produits
            produits = Produit.find_all("actif = 1")

            # Nettoyer le treeview
            for item in self.stock_tree.get_children():
                self.stock_tree.delete(item)

            total_valeur = 0
            produits_faible_stock = 0

            for produit in produits:
                # Calculer la valeur du stock
                valeur_stock = produit.stock_actuel * produit.prix_achat
                total_valeur += valeur_stock

                # Déterminer le statut
                if produit.is_stock_faible():
                    statut = "⚠️ Stock faible"
                    produits_faible_stock += 1
                elif produit.stock_actuel == 0:
                    statut = "❌ Rupture"
                else:
                    statut = "✅ Normal"

                # Calculer la rotation (approximative)
                rotation = self.calculer_rotation_stock(produit.id)

                self.stock_tree.insert("", "end", values=(
                    produit.nom,
                    f"{produit.stock_actuel} {produit.unite}",
                    f"{valeur_stock:.2f} €",
                    f"{rotation:.1f}",
                    statut
                ))

            # Mettre à jour les statistiques si elles existent
            if hasattr(self, 'stat_valeur_totale'):
                self.stat_valeur_totale.config(text=f"{total_valeur:.2f} €")
            if hasattr(self, 'stat_produits_actifs'):
                self.stat_produits_actifs.config(text=str(len(produits)))
            if hasattr(self, 'stat_stock_faible'):
                self.stat_stock_faible.config(text=str(produits_faible_stock))
            if hasattr(self, 'stat_ruptures'):
                ruptures = sum(1 for p in produits if p.stock_actuel == 0)
                self.stat_ruptures.config(text=str(ruptures))

            # Charger aussi les mouvements et alertes
            self.load_movements_data()
            self.load_alerts_data()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données de stock: {e}")

    def load_movements_data(self):
        """Charge les données des mouvements de stock"""
        try:
            # Nettoyer le treeview
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # Construire la requête avec filtre
            where_clause = "1=1"
            params = []

            if hasattr(self, 'movement_type_var') and self.movement_type_var.get() != "tous":
                where_clause += " AND type_mouvement = ?"
                params.append(self.movement_type_var.get())

            # Limiter aux 100 derniers mouvements
            mouvements = MouvementStock.find_all(
                f"{where_clause} ORDER BY date_mouvement DESC LIMIT 100",
                params
            )

            for mouvement in mouvements:
                # Formater la date
                date_str = datetime.fromisoformat(mouvement.date_mouvement).strftime('%d/%m/%Y %H:%M')

                # Calculer la valeur
                valeur = mouvement.quantite * mouvement.prix_unitaire if mouvement.prix_unitaire else 0

                # Icône selon le type
                type_icon = {
                    'entree': '⬆️ Entrée',
                    'sortie': '⬇️ Sortie',
                    'inventaire': '📊 Inventaire'
                }.get(mouvement.type_mouvement, mouvement.type_mouvement)

                self.movements_tree.insert("", "end", values=(
                    date_str,
                    mouvement.get_produit_nom(),
                    type_icon,
                    f"{mouvement.quantite}",
                    f"{valeur:.2f} €",
                    mouvement.get_utilisateur_nom()
                ))

        except Exception as e:
            print(f"Erreur lors du chargement des mouvements: {e}")

    def load_alerts_data(self):
        """Charge les données des alertes de stock"""
        try:
            # Nettoyer le treeview
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)

            # Récupérer les produits en stock faible
            produits_faible_stock = Produit.find_produits_stock_faible()

            for produit in produits_faible_stock:
                # Trouver la dernière vente de ce produit
                derniere_vente = self.get_derniere_vente_produit(produit.id)

                # Déterminer l'action recommandée
                if produit.stock_actuel == 0:
                    action = "🚨 Commande urgente"
                elif produit.stock_actuel <= produit.seuil_minimum / 2:
                    action = "⚠️ Commande recommandée"
                else:
                    action = "📋 Surveiller"

                self.alerts_tree.insert("", "end", values=(
                    produit.nom,
                    f"{produit.stock_actuel} {produit.unite}",
                    f"{produit.seuil_minimum} {produit.unite}",
                    derniere_vente,
                    action
                ))

        except Exception as e:
            print(f"Erreur lors du chargement des alertes: {e}")

    def get_derniere_vente_produit(self, produit_id):
        """Récupère la date de la dernière vente d'un produit"""
        try:
            query = """
                SELECT MAX(v.date_vente)
                FROM ventes v
                JOIN details_vente dv ON v.id = dv.vente_id
                WHERE dv.produit_id = ?
            """
            from database.db_manager import DatabaseManager
            db = DatabaseManager()
            result = db.execute_query(query, (produit_id,))

            if result and result[0][0]:
                date_vente = datetime.fromisoformat(result[0][0])
                return date_vente.strftime('%d/%m/%Y')
            else:
                return "Jamais vendu"

        except Exception:
            return "Inconnue"

    def calculer_rotation_stock(self, produit_id):
        """Calcule la rotation du stock pour un produit"""
        try:
            # Récupérer les mouvements de sortie des 30 derniers jours
            query = """
                SELECT SUM(quantite) FROM mouvements_stock
                WHERE produit_id = ? AND type_mouvement = 'sortie'
                AND date_mouvement >= date('now', '-30 days')
            """
            from database.db_manager import DatabaseManager
            db = DatabaseManager()
            result = db.execute_query(query, (produit_id,))

            sorties_30j = result[0][0] if result and result[0][0] else 0

            # Récupérer le stock actuel
            produit = Produit.find_by_id(produit_id)
            if not produit or produit.stock_actuel == 0:
                return 0

            # Calculer la rotation mensuelle
            rotation = sorties_30j / produit.stock_actuel if produit.stock_actuel > 0 else 0
            return rotation

        except Exception:
            return 0

    def load_financial_data(self):
        """Charge les données financières"""
        try:
            start_date, end_date = self.get_date_range()

            # Récupérer les ventes de la période
            ventes = Vente.find_ventes_periode(start_date, end_date)

            # Calculer les statistiques financières
            total_ca = sum(vente.total_ttc for vente in ventes)
            total_cout = 0

            # Calculer le coût des ventes
            for vente in ventes:
                for detail in vente.details:
                    produit = Produit.find_by_id(detail.produit_id)
                    if produit:
                        total_cout += detail.quantite * produit.prix_achat

            marge_brute = total_ca - total_cout

            # Mettre à jour les statistiques
            if hasattr(self, 'stat_chiffre_daffaires_fin'):
                self.stat_chiffre_daffaires_fin.config(text=f"{total_ca:.2f} €")
            if hasattr(self, 'stat_cout_des_ventes'):
                self.stat_cout_des_ventes.config(text=f"{total_cout:.2f} €")
            if hasattr(self, 'stat_marge_brute'):
                self.stat_marge_brute.config(text=f"{marge_brute:.2f} €")

            # Charger les données par mode de paiement
            self.load_payment_data(ventes)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données financières: {e}")

    def load_payment_data(self, ventes):
        """Charge les données par mode de paiement"""
        try:
            # Nettoyer le treeview
            for item in self.payment_tree.get_children():
                self.payment_tree.delete(item)

            # Grouper par mode de paiement
            paiements = {}
            total_montant = 0

            for vente in ventes:
                mode = vente.mode_paiement
                if mode not in paiements:
                    paiements[mode] = {'nombre': 0, 'montant': 0}

                paiements[mode]['nombre'] += 1
                paiements[mode]['montant'] += vente.total_ttc
                total_montant += vente.total_ttc

            # Ajouter au treeview
            for mode, data in paiements.items():
                pourcentage = (data['montant'] / total_montant * 100) if total_montant > 0 else 0

                mode_nom = {
                    'cash': 'Espèces',
                    'carte': 'Carte bancaire',
                    'mobile': 'Paiement mobile',
                    'credit': 'Crédit client',
                    'cheque': 'Chèque'
                }.get(mode, mode.capitalize())

                self.payment_tree.insert("", "end", values=(
                    mode_nom,
                    data['nombre'],
                    f"{data['montant']:.2f} €",
                    f"{pourcentage:.1f}%"
                ))

        except Exception as e:
            print(f"Erreur lors du chargement des données de paiement: {e}")

    def export_excel(self):
        """Exporte les données vers Excel"""
        try:
            from utils.export_manager import ExportManager
            export_manager = ExportManager()

            if self.type_rapport == "ventes":
                # Export des ventes
                start_date, end_date = self.get_date_range()
                ventes = Vente.find_ventes_periode(start_date, end_date)

                data_dict = {
                    'Ventes': [vente.to_dict() for vente in ventes],
                    'Résumé': [{
                        'Période': f"{start_date} au {end_date}",
                        'Nombre de ventes': len(ventes),
                        'Chiffre d\'affaires': sum(v.total_ttc for v in ventes),
                        'Ticket moyen': sum(v.total_ttc for v in ventes) / len(ventes) if ventes else 0
                    }]
                }

                filename = f"rapport_ventes_{start_date}_{end_date}.xlsx"

            elif self.type_rapport == "stock":
                # Export du stock
                produits = Produit.find_all("actif = 1")

                data_dict = {
                    'État du stock': [{
                        'Produit': p.nom,
                        'Stock actuel': p.stock_actuel,
                        'Unité': p.unite,
                        'Prix achat': p.prix_achat,
                        'Prix vente': p.prix_vente,
                        'Valeur stock': p.stock_actuel * p.prix_achat,
                        'Seuil minimum': p.seuil_minimum,
                        'Statut': 'Stock faible' if p.is_stock_faible() else 'Normal'
                    } for p in produits]
                }

                filename = f"rapport_stock_{datetime.now().strftime('%Y%m%d')}.xlsx"

            elif self.type_rapport == "financier":
                # Export financier
                start_date, end_date = self.get_date_range()
                ventes = Vente.find_ventes_periode(start_date, end_date)

                # Données par mode de paiement
                paiements = {}
                for vente in ventes:
                    mode = vente.mode_paiement
                    if mode not in paiements:
                        paiements[mode] = {'nombre': 0, 'montant': 0}
                    paiements[mode]['nombre'] += 1
                    paiements[mode]['montant'] += vente.total_ttc

                data_dict = {
                    'Résumé financier': [{
                        'Période': f"{start_date} au {end_date}",
                        'Chiffre d\'affaires': sum(v.total_ttc for v in ventes),
                        'Nombre de transactions': len(ventes)
                    }],
                    'Par mode de paiement': [{
                        'Mode': mode,
                        'Nombre': data['nombre'],
                        'Montant': data['montant']
                    } for mode, data in paiements.items()]
                }

                filename = f"rapport_financier_{start_date}_{end_date}.xlsx"

            filepath = export_manager.create_excel_export(data_dict, filename)
            messagebox.showinfo("Succès", f"Export Excel créé: {filepath}")

        except ImportError:
            messagebox.showerror("Erreur", "Module openpyxl non installé. Installez-le avec: pip install openpyxl")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export Excel: {e}")

    def export_pdf(self):
        """Exporte les données vers PDF"""
        try:
            from utils.pdf_generator import PDFGenerator
            pdf_generator = PDFGenerator()

            if self.type_rapport == "ventes":
                start_date, end_date = self.get_date_range()
                ventes = Vente.find_ventes_periode(start_date, end_date)
                filepath = pdf_generator.generate_sales_report(ventes, start_date, end_date)

            elif self.type_rapport == "stock":
                produits = Produit.find_all("actif = 1")
                filepath = pdf_generator.generate_stock_report(produits)

            elif self.type_rapport == "financier":
                start_date, end_date = self.get_date_range()
                ventes = Vente.find_ventes_periode(start_date, end_date)
                filepath = pdf_generator.generate_financial_report(ventes, start_date, end_date)

            messagebox.showinfo("Succès", f"Export PDF créé: {filepath}")

        except ImportError:
            messagebox.showerror("Erreur", "Module reportlab non installé. Installez-le avec: pip install reportlab")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'export PDF: {e}")
