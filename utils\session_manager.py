"""
Gestionnaire de sessions utilisateur
"""
import uuid
import hashlib
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional

class UserSession:
    """Représente une session utilisateur"""
    
    def __init__(self, user_id: int, username: str, role: str):
        self.session_id = str(uuid.uuid4())
        self.user_id = user_id
        self.username = username
        self.role = role
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.ip_address = "127.0.0.1"  # À améliorer
        self.is_active = True
        self.login_attempts = 0
        self.permissions = []
        
        # Générer un token de session sécurisé
        self.token = self._generate_session_token()
    
    def _generate_session_token(self) -> str:
        """Génère un token de session sécurisé"""
        data = f"{self.session_id}{self.user_id}{self.username}{time.time()}"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def update_activity(self):
        """Met à jour l'heure de dernière activité"""
        self.last_activity = datetime.now()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """Vérifie si la session a expiré"""
        if not self.is_active:
            return True
        
        expiry_time = self.last_activity + timedelta(minutes=timeout_minutes)
        return datetime.now() > expiry_time
    
    def invalidate(self):
        """Invalide la session"""
        self.is_active = False
    
    def to_dict(self) -> dict:
        """Convertit la session en dictionnaire"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'username': self.username,
            'role': self.role,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'ip_address': self.ip_address,
            'is_active': self.is_active,
            'token': self.token
        }

class SessionManager:
    """Gestionnaire centralisé des sessions"""
    
    def __init__(self):
        self.active_sessions: Dict[str, UserSession] = {}
        self.session_timeout_minutes = 30
        self.max_sessions_per_user = 3
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = datetime.now()
        
        # Statistiques
        self.total_sessions_created = 0
        self.total_sessions_expired = 0
        self.total_sessions_invalidated = 0
    
    def create_session(self, user_id: int, username: str, role: str, permissions: list = None) -> UserSession:
        """Crée une nouvelle session utilisateur"""
        try:
            # Nettoyer les anciennes sessions
            self._cleanup_expired_sessions()
            
            # Vérifier le nombre de sessions actives pour cet utilisateur
            user_sessions = [s for s in self.active_sessions.values() 
                           if s.user_id == user_id and s.is_active]
            
            if len(user_sessions) >= self.max_sessions_per_user:
                # Invalider la plus ancienne session
                oldest_session = min(user_sessions, key=lambda s: s.created_at)
                self.invalidate_session(oldest_session.session_id)
                logging.warning(f"Session la plus ancienne invalidée pour l'utilisateur {username}")
            
            # Créer la nouvelle session
            session = UserSession(user_id, username, role)
            if permissions:
                session.permissions = permissions
            
            self.active_sessions[session.session_id] = session
            self.total_sessions_created += 1
            
            logging.info(f"Session créée pour {username} (ID: {session.session_id})")
            
            # Enregistrer dans les logs d'audit
            self._log_session_event("SESSION_CREATED", session)
            
            return session
            
        except Exception as e:
            logging.error(f"Erreur lors de la création de session: {e}")
            raise
    
    def get_session(self, session_id: str) -> Optional[UserSession]:
        """Récupère une session par son ID"""
        session = self.active_sessions.get(session_id)
        
        if session and not session.is_expired(self.session_timeout_minutes):
            session.update_activity()
            return session
        elif session and session.is_expired(self.session_timeout_minutes):
            # Session expirée
            self.invalidate_session(session_id)
            return None
        
        return None
    
    def validate_session(self, session_id: str, token: str) -> bool:
        """Valide une session avec son token"""
        session = self.get_session(session_id)
        
        if session and session.token == token and session.is_active:
            session.update_activity()
            return True
        
        return False
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalide une session"""
        try:
            session = self.active_sessions.get(session_id)
            
            if session:
                session.invalidate()
                self.total_sessions_invalidated += 1
                
                logging.info(f"Session invalidée: {session_id} (utilisateur: {session.username})")
                
                # Enregistrer dans les logs d'audit
                self._log_session_event("SESSION_INVALIDATED", session)
                
                # Supprimer de la liste active
                del self.active_sessions[session_id]
                
                return True
            
            return False
            
        except Exception as e:
            logging.error(f"Erreur lors de l'invalidation de session: {e}")
            return False
    
    def invalidate_user_sessions(self, user_id: int) -> int:
        """Invalide toutes les sessions d'un utilisateur"""
        count = 0
        sessions_to_remove = []
        
        for session_id, session in self.active_sessions.items():
            if session.user_id == user_id:
                session.invalidate()
                sessions_to_remove.append(session_id)
                count += 1
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
        
        if count > 0:
            logging.info(f"{count} sessions invalidées pour l'utilisateur ID {user_id}")
        
        return count
    
    def get_user_sessions(self, user_id: int) -> list:
        """Récupère toutes les sessions actives d'un utilisateur"""
        return [session for session in self.active_sessions.values() 
                if session.user_id == user_id and session.is_active]
    
    def get_all_active_sessions(self) -> list:
        """Récupère toutes les sessions actives"""
        return [session for session in self.active_sessions.values() 
                if session.is_active and not session.is_expired(self.session_timeout_minutes)]
    
    def _cleanup_expired_sessions(self):
        """Nettoie les sessions expirées"""
        current_time = datetime.now()
        
        # Nettoyer seulement si nécessaire
        if (current_time - self.last_cleanup).seconds < self.cleanup_interval:
            return
        
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if session.is_expired(self.session_timeout_minutes):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            session = self.active_sessions[session_id]
            logging.info(f"Session expirée nettoyée: {session_id} (utilisateur: {session.username})")
            
            # Enregistrer dans les logs d'audit
            self._log_session_event("SESSION_EXPIRED", session)
            
            del self.active_sessions[session_id]
            self.total_sessions_expired += 1
        
        self.last_cleanup = current_time
        
        if expired_sessions:
            logging.info(f"{len(expired_sessions)} sessions expirées nettoyées")
    
    def _log_session_event(self, event_type: str, session: UserSession):
        """Enregistre un événement de session dans les logs d'audit"""
        try:
            from models.audit_log import AuditLog
            
            audit_log = AuditLog()
            audit_log.utilisateur_id = session.user_id
            audit_log.action = event_type
            audit_log.description = f"Session {session.session_id} - {event_type}"
            audit_log.date_action = datetime.now().isoformat()
            audit_log.adresse_ip = session.ip_address
            audit_log.save()
            
        except Exception as e:
            logging.error(f"Erreur lors de l'enregistrement de l'audit de session: {e}")
    
    def get_session_statistics(self) -> dict:
        """Retourne les statistiques des sessions"""
        active_count = len(self.get_all_active_sessions())
        
        return {
            'active_sessions': active_count,
            'total_created': self.total_sessions_created,
            'total_expired': self.total_sessions_expired,
            'total_invalidated': self.total_sessions_invalidated,
            'session_timeout_minutes': self.session_timeout_minutes,
            'max_sessions_per_user': self.max_sessions_per_user
        }
    
    def force_cleanup(self):
        """Force le nettoyage de toutes les sessions expirées"""
        self.last_cleanup = datetime.now() - timedelta(seconds=self.cleanup_interval + 1)
        self._cleanup_expired_sessions()
    
    def set_session_timeout(self, timeout_minutes: int):
        """Définit le timeout des sessions"""
        if timeout_minutes > 0:
            self.session_timeout_minutes = timeout_minutes
            logging.info(f"Timeout de session défini à {timeout_minutes} minutes")
    
    def set_max_sessions_per_user(self, max_sessions: int):
        """Définit le nombre maximum de sessions par utilisateur"""
        if max_sessions > 0:
            self.max_sessions_per_user = max_sessions
            logging.info(f"Nombre maximum de sessions par utilisateur défini à {max_sessions}")
    
    def export_session_data(self) -> list:
        """Exporte les données de session pour analyse"""
        return [session.to_dict() for session in self.active_sessions.values()]
    
    def get_session_by_user(self, user_id: int) -> Optional[UserSession]:
        """Récupère la session la plus récente d'un utilisateur"""
        user_sessions = self.get_user_sessions(user_id)
        
        if user_sessions:
            # Retourner la session la plus récente
            return max(user_sessions, key=lambda s: s.last_activity)
        
        return None

# Instance globale
session_manager = SessionManager()
