"""
Dialogue pour la création/modification de fournisseurs
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.fournisseur import Fournisseur

class FournisseurDialog:
    def __init__(self, parent, title="Nouveau fournisseur", fournisseur=None):
        self.result = None
        self.fournisseur = fournisseur
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("450x400")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
        
        # Charger les données si modification
        if self.fournisseur:
            self.load_supplier_data()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.window.winfo_screenheight() // 2) - (400 // 2)
        self.window.geometry(f"450x400+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.nom_var = tk.StringVar()
        self.contact_var = tk.StringVar()
        self.telephone_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.adresse_var = tk.StringVar()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations générales", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Nom du fournisseur
        ttk.Label(info_frame, text="Nom du fournisseur *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        nom_entry = ttk.Entry(info_frame, textvariable=self.nom_var, width=30)
        nom_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        nom_entry.focus()
        
        # Personne de contact
        ttk.Label(info_frame, text="Contact:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(info_frame, textvariable=self.contact_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Configurer les colonnes
        info_frame.columnconfigure(1, weight=1)
        
        # Informations de contact
        contact_frame = ttk.LabelFrame(main_frame, text="Coordonnées", padding="10")
        contact_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Téléphone
        ttk.Label(contact_frame, text="Téléphone:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(contact_frame, textvariable=self.telephone_var, width=30).grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Email
        ttk.Label(contact_frame, text="Email:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(contact_frame, textvariable=self.email_var, width=30).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Adresse
        ttk.Label(contact_frame, text="Adresse:").grid(row=2, column=0, sticky=tk.W+tk.N, pady=5)
        adresse_text = tk.Text(contact_frame, height=4, width=30)
        adresse_text.grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Scrollbar pour l'adresse
        adresse_scrollbar = ttk.Scrollbar(contact_frame, orient=tk.VERTICAL, command=adresse_text.yview)
        adresse_text.configure(yscrollcommand=adresse_scrollbar.set)
        adresse_scrollbar.grid(row=2, column=2, sticky=tk.N+tk.S, pady=5)
        
        # Stocker la référence du widget Text
        self.adresse_text = adresse_text
        
        # Configurer les colonnes
        contact_frame.columnconfigure(1, weight=1)
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Enregistrer", command=self.save).pack(side=tk.RIGHT)
        
        # Bind Enter key pour les champs Entry
        for widget in [nom_entry]:
            widget.bind('<Return>', lambda e: self.save())
        
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def load_supplier_data(self):
        """Charge les données du fournisseur pour modification"""
        if not self.fournisseur:
            return
        
        self.nom_var.set(self.fournisseur.nom)
        self.contact_var.set(self.fournisseur.contact or "")
        self.telephone_var.set(self.fournisseur.telephone or "")
        self.email_var.set(self.fournisseur.email or "")
        
        # Adresse dans le widget Text
        if self.fournisseur.adresse:
            self.adresse_text.delete(1.0, tk.END)
            self.adresse_text.insert(1.0, self.fournisseur.adresse)
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Nom obligatoire
        nom = self.nom_var.get().strip()
        if not nom:
            errors.append("Le nom du fournisseur est obligatoire")
        elif len(nom) < 2:
            errors.append("Le nom doit contenir au moins 2 caractères")
        
        # Validation de l'email si fourni
        email = self.email_var.get().strip()
        if email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                errors.append("L'adresse email n'est pas valide")
        
        # Validation du téléphone si fourni
        telephone = self.telephone_var.get().strip()
        if telephone:
            # Supprimer les espaces et caractères spéciaux pour la validation
            tel_clean = re.sub(r'[^\d+]', '', telephone)
            if len(tel_clean) < 8:
                errors.append("Le numéro de téléphone doit contenir au moins 8 chiffres")
        
        return errors
    
    def save(self):
        """Sauvegarde le fournisseur"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            # Créer ou modifier le fournisseur
            if self.fournisseur:
                fournisseur = self.fournisseur
            else:
                fournisseur = Fournisseur()
            
            # Remplir les données
            fournisseur.nom = self.nom_var.get().strip()
            fournisseur.contact = self.contact_var.get().strip() or None
            fournisseur.telephone = self.telephone_var.get().strip() or None
            fournisseur.email = self.email_var.get().strip() or None
            fournisseur.adresse = self.adresse_text.get(1.0, tk.END).strip() or None
            
            # Sauvegarder
            fournisseur.save()
            
            self.result = fournisseur.to_dict()
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
