"""
Dialogue pour l'enregistrement des paiements de crédit
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.client_credit import ClientCredit

class PaiementCreditDialog:
    def __init__(self, parent, client, current_user):
        self.result = None
        self.client = client
        self.current_user = current_user
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(f"Paiement de crédit - {client.nom}")
        self.window.geometry("400x350")
        self.window.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.setup_variables()
        
        # Interface
        self.setup_ui()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (350 // 2)
        self.window.geometry(f"400x350+{x}+{y}")
    
    def setup_variables(self):
        """Initialise les variables du formulaire"""
        self.montant_var = tk.StringVar()
        self.mode_paiement_var = tk.StringVar(value="cash")
        self.reference_var = tk.StringVar()
        self.commentaire_var = tk.StringVar()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Informations du client
        client_frame = ttk.LabelFrame(main_frame, text="Client", padding="10")
        client_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(client_frame, text="Nom:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(client_frame, text=self.client.nom, font=('Arial', 10, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(client_frame, text="Dette actuelle:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.dette_label = ttk.Label(client_frame, text=f"{self.client.credit_utilise:.2f} €", 
                                    font=('Arial', 12, 'bold'), foreground="red")
        self.dette_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Détails du paiement
        paiement_frame = ttk.LabelFrame(main_frame, text="Détails du paiement", padding="10")
        paiement_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Montant
        ttk.Label(paiement_frame, text="Montant (€) *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        montant_frame = ttk.Frame(paiement_frame)
        montant_frame.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        montant_entry = ttk.Entry(montant_frame, textvariable=self.montant_var, width=15)
        montant_entry.pack(side=tk.LEFT)
        montant_entry.focus()
        montant_entry.bind('<KeyRelease>', self.calculer_nouveau_solde)
        
        ttk.Button(montant_frame, text="Tout", command=self.payer_tout).pack(side=tk.LEFT, padx=(5, 0))
        
        # Mode de paiement
        ttk.Label(paiement_frame, text="Mode de paiement *:").grid(row=1, column=0, sticky=tk.W, pady=5)
        mode_frame = ttk.Frame(paiement_frame)
        mode_frame.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(10, 0))
        
        modes = [("Espèces", "cash"), ("Carte", "carte"), ("Virement", "virement"), ("Chèque", "cheque")]
        for i, (text, value) in enumerate(modes):
            ttk.Radiobutton(mode_frame, text=text, variable=self.mode_paiement_var, 
                           value=value).grid(row=i//2, column=i%2, sticky=tk.W, padx=(0, 10))
        
        # Référence
        ttk.Label(paiement_frame, text="Référence:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(paiement_frame, textvariable=self.reference_var, width=25).grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        
        # Commentaire
        ttk.Label(paiement_frame, text="Commentaire:").grid(row=3, column=0, sticky=tk.W+tk.N, pady=5)
        commentaire_text = tk.Text(paiement_frame, height=3, width=25)
        commentaire_text.grid(row=3, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        self.commentaire_text = commentaire_text
        
        # Configurer les colonnes
        paiement_frame.columnconfigure(1, weight=1)
        
        # Résultat
        result_frame = ttk.LabelFrame(main_frame, text="Après paiement", padding="10")
        result_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(result_frame, text="Nouvelle dette:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.nouvelle_dette_label = ttk.Label(result_frame, text=f"{self.client.credit_utilise:.2f} €", 
                                             font=('Arial', 12, 'bold'))
        self.nouvelle_dette_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(result_frame, text="Crédit disponible:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.credit_disponible_label = ttk.Label(result_frame, text=f"{self.client.get_credit_disponible():.2f} €", 
                                                font=('Arial', 10, 'bold'))
        self.credit_disponible_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="Annuler", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(buttons_frame, text="Enregistrer", command=self.save).pack(side=tk.RIGHT)
        
        # Bind Enter key
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
        
        # Générer une référence par défaut
        self.generer_reference_defaut()
    
    def generer_reference_defaut(self):
        """Génère une référence par défaut"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.reference_var.set(f"PAIEMENT_{timestamp}")
    
    def payer_tout(self):
        """Remplit le montant avec la dette totale"""
        self.montant_var.set(f"{self.client.credit_utilise:.2f}")
        self.calculer_nouveau_solde()
    
    def calculer_nouveau_solde(self, event=None):
        """Calcule et affiche le nouveau solde"""
        try:
            montant = float(self.montant_var.get() or 0)
            nouvelle_dette = max(0, self.client.credit_utilise - montant)
            nouveau_credit_disponible = self.client.limite_credit - nouvelle_dette
            
            # Afficher la nouvelle dette
            self.nouvelle_dette_label.config(text=f"{nouvelle_dette:.2f} €")
            
            # Couleur selon la nouvelle dette
            if nouvelle_dette == 0:
                self.nouvelle_dette_label.config(foreground="green")
            elif nouvelle_dette < self.client.credit_utilise:
                self.nouvelle_dette_label.config(foreground="orange")
            else:
                self.nouvelle_dette_label.config(foreground="red")
            
            # Afficher le nouveau crédit disponible
            self.credit_disponible_label.config(text=f"{nouveau_credit_disponible:.2f} €")
            self.credit_disponible_label.config(foreground="green")
            
        except ValueError:
            self.nouvelle_dette_label.config(text="Montant invalide", foreground="red")
            self.credit_disponible_label.config(text="N/A", foreground="black")
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        # Montant valide
        try:
            montant = float(self.montant_var.get())
            if montant <= 0:
                errors.append("Le montant doit être positif")
            elif montant > self.client.credit_utilise:
                errors.append(f"Le montant ne peut pas dépasser la dette ({self.client.credit_utilise:.2f} €)")
        except ValueError:
            errors.append("Le montant doit être un nombre valide")
        
        # Mode de paiement sélectionné
        if not self.mode_paiement_var.get():
            errors.append("Veuillez sélectionner un mode de paiement")
        
        return errors
    
    def save(self):
        """Enregistre le paiement"""
        # Validation
        errors = self.validate_form()
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return
        
        try:
            montant = float(self.montant_var.get())
            mode_paiement = self.mode_paiement_var.get()
            reference = self.reference_var.get().strip()
            commentaire = self.commentaire_text.get(1.0, tk.END).strip()
            
            # Enregistrer le paiement
            montant_paye = self.client.payer_credit(montant)
            
            # Log de l'action
            if self.current_user:
                self.current_user.log_action(
                    f"Paiement crédit: {montant_paye:.2f}€ pour {self.client.nom}",
                    "clients_credit",
                    self.client.id,
                    {"credit_avant": self.client.credit_utilise + montant_paye},
                    {"credit_apres": self.client.credit_utilise}
                )
            
            # TODO: Enregistrer dans un journal des paiements si nécessaire
            # Cela pourrait être une nouvelle table "paiements_credit"
            
            self.result = {
                'montant': montant_paye,
                'mode_paiement': mode_paiement,
                'reference': reference,
                'commentaire': commentaire,
                'nouvelle_dette': self.client.credit_utilise
            }
            
            messagebox.showinfo("Succès", 
                              f"Paiement de {montant_paye:.2f} € enregistré avec succès\n"
                              f"Nouvelle dette: {self.client.credit_utilise:.2f} €")
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement du paiement: {e}")
    
    def cancel(self):
        """Annule le dialogue"""
        self.window.destroy()
